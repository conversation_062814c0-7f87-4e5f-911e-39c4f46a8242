package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.domain.R;
import org.dromara.web.domain.vo.LoginVo;
import org.dromara.web.service.SdtService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sdt")
public class SdtController {

    private final SdtService sdtService;

    /**
     * 山东通登录
     * @param code
     * @return
     */
    @SaIgnore
    @GetMapping("/login")
    public R<LoginVo> sdtLogin(@Param("code") String code) {
        try {
            LoginVo loginVo = sdtService.sdtLogin(code);
            log.info("******************" + JSONUtil.toJsonStr(loginVo));
            return R.ok(loginVo);
        }catch (Exception e){
            log.error("山东通登录失败" + e.getMessage());
            return R.fail("登录失败" + e.getMessage());
        }
    }

}
