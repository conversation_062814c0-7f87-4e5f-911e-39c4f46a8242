package org.dromara.web.service;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.domain.model.LoginBody;
import org.dromara.common.core.domain.model.RegisterBody;
import org.dromara.common.core.domain.model.SmsLoginBody;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.exception.user.CaptchaException;
import org.dromara.common.core.exception.user.CaptchaExpireException;
import org.dromara.common.core.exception.user.UserException;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.event.LogininforEvent;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.common.web.config.properties.CaptchaProperties;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.ISysRoleService;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;

/**
 * 注册校验方法
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysRegisterService {

    private final ISysUserService userService;
    private final SysUserMapper userMapper;
    private final CaptchaProperties captchaProperties;
    private final ISysRoleService sysRoleService;

    /**
     * 注册
     */
    public void register(RegisterBody registerBody) {
        // 检验确认密码和密码是否一致
        if (!registerBody.getPassword().equals(registerBody.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }
        String tenantId = registerBody.getTenantId();
        String username = registerBody.getUsername();
        // 校验username是否是手机号
//        if (!isMobile(username)) {
//            throw new ServiceException("正确输入手机号");
//        }
        // 校验用户名只能是数字和字母
        if (!username.matches("[0-9a-zA-Z]+")) {
            throw new ServiceException("用户名只能是数字和字母");
        }
        String password = registerBody.getPassword();
        // 校验用户类型是否存在
        String userType = UserType.getUserType(registerBody.getUserType()).getUserType();

//        boolean captchaEnabled = captchaProperties.getEnable();
        boolean captchaEnabled = false;
        // 验证码开关
        if (captchaEnabled) {
            validateCaptcha(tenantId, username, registerBody.getCode(), registerBody.getUuid());
        }

        // 在创建用户对象前，先检查用户名是否已存在
        boolean userExists = userMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getUserName, username)
            .eq(SysUser::getDelFlag, "0"));
        if (userExists) {
            throw new UserException("该用户已注册，请直接登录");
        }

        SysUserBo sysUser = new SysUserBo();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPhonenumber(username);
        sysUser.setPassword(BCrypt.hashpw(password));
        sysUser.setUserType(userType);

        // 已在前面检查过用户名是否存在，此处不再重复检查
        boolean regFlag = userService.registerUser(sysUser, tenantId);
        if (!regFlag) {
            throw new UserException("user.register.error");
        }
        recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.register.success"));
    }

    /**
     * 检验是否是手机号
     * @param username
     * @return
     */
    private static boolean isMobile(String username) {
        // 定义手机号的正则表达式
        String regex = "^1[3-9]\\d{9}$";
        // 使用正则表达式匹配手机号
        return username.matches(regex);
    }

    public static void main(String[] args) {
        System.out.println(isMobile("13811345678"));
    }


    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     */
    public void validateCaptcha(String tenantId, String username, String code, String uuid) {
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + StringUtils.blankToDefault(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    private void recordLogininfor(String tenantId, String username, String status, String message) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setTenantId(tenantId);
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(status);
        logininforEvent.setMessage(message);
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 短信登录注册用户
     * @param body
     */
    public SysUserVo registerByCode(String body) {
        SmsLoginBody loginBody = JsonUtils.parseObject(body, SmsLoginBody.class);
        String tenantId = loginBody.getTenantId();
        String username = loginBody.getPhonenumber();
        String password = "000000";//默认，密码
        // 校验用户类型是否存在,默认PC端用户
        String userType = UserType.getUserType("sys_user").getUserType();

        SysUserBo sysUser = new SysUserBo();
        sysUser.setUserName(username);
        //用户昵称取值用户+手机号后四位
        sysUser.setNickName("用户" + username.substring(username.length() - 4));
        sysUser.setPhonenumber(username);
        sysUser.setPassword(BCrypt.hashpw(password));
        sysUser.setUserType(userType);
        // 校验用户名唯一性
        if(!userService.checkUserNameUnique(sysUser)){
            //用户已存在，返回用户信息
            return userService.selectUserByUserName(username);
        }
        boolean exist = TenantHelper.dynamic(tenantId, () -> {
            return userMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, sysUser.getUserName()).eq(SysUser::getDelFlag,"0"));
        });
        if (exist) {
            throw new UserException("用户注册保存失败", username);
        }
        boolean regFlag = userService.registerUser(sysUser, tenantId);
        if (!regFlag) {
            throw new UserException("用户注册失败");
        }
        SysUserVo sysUserVo = userService.selectUserByUserName(username);
        //根据权限code。查询用户普通角色common，并给用户赋值普通角色
        String roleKey = "common";
        SysRoleVo sysRoleVo = sysRoleService.selectRoleByKey(roleKey);
        if(null == sysRoleVo){
            throw new ServiceException("用户角色信息未找到，请重试");
        }
        Long[] roleIds = new Long[]{sysRoleVo.getRoleId()};
        userService.insertUserAuthUnlogin(sysUserVo.getUserId(),roleIds);
        recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.register.success"));
        return userService.selectUserByUserName(username);
    }



    /**
     * 山东通用户登录
     * @param phoneNumber 手机号
     */
    public SysUserVo registerByPhone(String phoneNumber,String nickName,String deptName) {
        String tenantId = "000000";
        String username = phoneNumber;
        String password = "sdt" + phoneNumber;//默认，密码
        // 校验用户类型是否存在,默认PC端用户
        String userType = UserType.getUserType("sdt_user").getUserType();

        SysUserBo sysUser = new SysUserBo();
        sysUser.setUserName(username);
        //用户昵称取值用户+手机号后四位
        sysUser.setNickName(nickName);
        sysUser.setPhonenumber(username);
        sysUser.setRemark(deptName);
        sysUser.setPassword(BCrypt.hashpw(password));
        sysUser.setUserType(userType);
        // 校验用户名唯一性
        if(!userService.checkUserNameUnique(sysUser)){
            //用户已存在，返回用户信息
            return userService.selectUserByUserName(username);
        }
        boolean exist = TenantHelper.dynamic(tenantId, () -> {
            return userMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, sysUser.getUserName()).eq(SysUser::getDelFlag,"0"));
        });
        if (exist) {
            throw new UserException("用户注册保存失败", username);
        }
        boolean regFlag = userService.registerUser(sysUser, tenantId);
        if (!regFlag) {
            throw new UserException("用户注册失败");
        }
        SysUserVo sysUserVo = userService.selectUserByUserName(username);
        //根据权限code。查询用户普通角色common，并给用户赋值普通角色
        String roleKey = "common";
        SysRoleVo sysRoleVo = sysRoleService.selectRoleByKey(roleKey);
        if(null == sysRoleVo){
            throw new ServiceException("用户角色信息未找到，请重试");
        }
        Long[] roleIds = new Long[]{sysRoleVo.getRoleId()};
        userService.insertUserAuthUnlogin(sysUserVo.getUserId(),roleIds);
//        recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.register.success"));
        return userService.selectUserByUserName(username);
    }
}
