package org.dromara.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.system.sdt.properties.SdtProperties;
import org.dromara.web.domain.vo.LoginVo;
import org.dromara.web.service.SdtService;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Service
@Slf4j
@RequiredArgsConstructor
public class SdtServiceImpl implements SdtService {

    private final SmsAuthStrategy smsAuthStrategy;

    private final RestTemplate restTemplate;

    private final SdtProperties sdtProperties;

    /**
     * 山东通登录
     * @param code
     * @return
     */
    @Override
    public LoginVo sdtLogin(String code) {
        if (ObjectUtil.isEmpty(code)) throw new ServiceException("未获取到登录信息，请退出应用重试");
        JSONObject obj = sdtGet(StrUtil.format("/cgi-bin/user/getuserinfo?access_token={}&code={}", getAccessToken(), code));
        String userId = obj.getStr("UserId");
        JSONObject user = sdtGet(StrUtil.format("/cgi-bin/user/get?access_token={}&userid={}", getAccessToken(), userId));
        //手机号
        String mobile = user.getStr("mobile");
        //姓名
        String name = user.getStr("name");
        JSONObject externalProfile = user.getJSONObject("external_profile");
        String externalCorpName = "";
        if (ObjectUtil.isNotEmpty(externalProfile)) {
            externalCorpName = externalProfile.getStr("external_corp_name");
        }
        return smsAuthStrategy.loginByPhone(mobile,name,externalCorpName);
    }

    /**
     * 接口请求
     * @return
     */
    private JSONObject sdtGet(String url) {
        String pushUrl = StrUtil.format("{}{}",sdtProperties.getUrl(),url);
        ResponseEntity<String> responseEntity = restTemplate.exchange(pushUrl, HttpMethod.GET, null, String.class);
        String body = responseEntity.getBody();
        log.info("********************************* sdt api req: {},resp:{}",url, body);
        if (ObjectUtil.isEmpty(body)) throw new ServiceException("未获取到登录信息，请退出应用重试");
        JSONObject obj = JSONUtil.parseObj(body);
        if (!"0".equals(obj.getStr("errcode"))) throw new ServiceException("未获取到登录信息，请退出应用重试");
        return obj;
    }

    /**
     * 获取access_token
     * @return
     */
    private String getAccessToken() {
        String key = StrUtil.format("{}:{}", sdtProperties.getRedisPrefix(), "access_token");
        String accessToken = RedisUtils.getCacheObject(key);
        if (ObjectUtil.isEmpty(accessToken)) {
            JSONObject obj = sdtGet(StrUtil.format("/cgi-bin/gettoken?corpid={}&corpsecret={}",sdtProperties.getCorpid(),sdtProperties.getSecret()));
            accessToken = obj.getStr("access_token");
            RedisUtils.setCacheObject(key,accessToken, Duration.ofSeconds(obj.getLong("expires_in")-100));
        }
        return accessToken;
    }

}
