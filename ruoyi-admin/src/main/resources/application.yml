# 项目相关配置
ruoyi:
  # 名称
  name: LLM
  # 版本
  version: ${revision}
  # 版权年份
  copyrightYear: 2025

# 图像验证码
captcha:
  # 页面 <参数设置> 可开启关闭 验证码校验
  enable: true
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 运行环境
server:
  # 服务器的HTTP端口
  port: 9002
  # 应用的访问路径
  servlet:
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理，每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# 日志配置
logging:
  level:
    org.dromara: @logging.level@
    org.springframework: warn
    org.mybatis.spring.mapper: debug
  config: classpath:logback-plus.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: ${ruoyi.name}
  threads:
    # 开启虚拟线程 仅jdk21可用
    virtual:
      enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 300MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**

# 多租户配置
tenant:
  # 是否开启
  enable: true
  # 排除表
  excludes:
    - sys_menu
    - sys_tenant
    - sys_tenant_package
    - sys_role_dept
    - sys_role_menu
    - sys_user_post
    - sys_user_role
    - sys_client
    - sys_oss_config
    - bs_review_issues
    - bs_review_staff
    - bs_project_accept
    - bs_project_accept_statistics

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper
  mapperPackage: org.dromara.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.dromara.**.domain
  global-config:
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: false
  # AES 加密头标识
  headerFlag: encrypt-key
  # publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3xXmP10q/9C4qocB/VX319ZksEhEPxOxDKPfvEqozXqjda/cdNsC620q0qE8m+DxYgkrxSdBhbvgJyL7Uzgg4aCc/vTCpwGWSZqLDVthUzF+YAYjG0HvjOJL8v4vbYKjirXyXP1m8QtMgtuvNzaIfVt5r/Z17iQOVNT8mlhHzRQIDAQAB
  # privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJjSQKKLIoZApyhRdCsS7TlF0W/y6OeEhQoEwrqmune79C0yTTSk8T1anHDeFXXJ6hLNtslFP6r4XhsopadwVmW/8m+1ooJo1vwx/6i7mJgxSKGjzrCnOSEqFnZxCKN2BUYGxnFkA43D5ErF6URKiAI+XgCb31SR0L70v/I1kUuNAgMBAAECgYAYw+XWkX6gTATquzx8qH4KDH7yyeYlcqvOolPeRJuuQKbC1kZ9tAcmHrOAdPT8WCLXNkAbYN3++m3hr9Ycis9tQKSEobO1EM3Hya9jxMhjQJBAZgZW2tKwRLmIbK5B9tugF8bIXR7JT0jleEuRrTlJh8p2dUjX1+/ETM3Ry3S9DwJBAM2m3TZ1cgO7LN4wF+2tun/dvX+UmPjC9bHXULGIZT41+homvVUHfvudkupKDq7pcLwmwIl8HG7k6cY8Mj/1HhsCQQC+PEJnpFypKKrVknluWiF2qcSqqb8kc4QgpO+5oI0oKfRCXa2gATGTQPmoeLo/N2I55BWYEzCY2VbJ+P2sAbd3AkBrNHVVElxhPY3iNcgDivER/xHuFBS6eUeDce8K3UOJ7rF2OIOyc7nqiPYxVHHbFK5EJx9vjFd2J7cGNBMt7CXJAkEAhIy4OD7t0W/kmKtWsO1cI7JmtgNQi7Vxes1xu4WC1VlmOFfNgo9SW6gKTLjSDPoqFuw1n6C/W8EUF+bF2fPYQQJAQ9951ZEzNexiQBw43JUa+iXjk3CxXxuKK2JXR3QodDAy6moAdzN07xE5RdaqQK87bJiuMzWdbnf1FP7AHLJNIw==
  # 放行目录
  excludeUrl:
    - /auth/code
    - /resource/sse/close
    - /resource/sse
    - /resource/sms/code
    - /system/dataItem/export
    - /resource/oss/upload
    - /resource/oss/download
    - /resource/oss/getFileInfo
    - /business/modelAuth/getUserByKey
    - /system/user/export
    - /system/role/export
    - /system/user/importTemplate
    - /system/user/importData
    - /auth/loginByPs


springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: false
  swagger-ui:
    # 持久化认证数据
    persistAuthorization: true
  info:
    # 标题
    title: '接口文档'
    # 描述
    description: '所有模块说明'
    # 版本
    version: '版本号: ${ruoyi.version}'
    # 作者信息
    contact:
      name: Lion Li
      email: <EMAIL>
      url: https://gitee.com/dromara/RuoYi-Vue-Plus
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: 1.演示模块
      packages-to-scan: org.dromara.demo
    - group: 2.通用模块
      packages-to-scan: org.dromara.web
    - group: 3.系统模块
      packages-to-scan: org.dromara.system
    - group: 4.代码生成模块
      packages-to-scan: org.dromara.generator
    - group: 5.业务模块
      packages-to-scan: org.dromara.business
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludeUrls:
    - /system/notice
    - /workflow/model/save
    - /workflow/model/editModelXml

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: true
  # 队列最大长度
  queueCapacity: 256
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log

--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse

--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'

# 山东通
sdt:
  # 请求地址
  url: http://15.209.0.3:9080/
  agentid: 1008212
  secret: c4sA5oUtyYCnP2Dmrdpri3d4yoIxhrLWCqa9P5T5pSo
  corpid: ww2389532372dc6805
  # 缓存前缀
  redis-prefix: sdt
#  url: http://15.209.0.3:80/
#  agentid: 1010430
#  secret: FbEg64Dm7MpeTGdJVOVzRkYWKdeJi8shZJ7VjYJ7Z5k
#  corpid: wwafa1a3005a15a672
#  url: http://59.206.205.195:9080/
#  agentid: 1008212
#  secret: c4sA5oUtyYCnP2Dmrdpri3d4yoIxhrLWCqa9P5T5pSo
#  corpid: ww2389532372dc6805
#  # 缓存前缀
#  redis-prefix: sdt
#  url: http://59.206.205.195:80/
#  agentid: 1010430
#  secret: FbEg64Dm7MpeTGdJVOVzRkYWKdeJi8shZJ7VjYJ7Z5k
#  corpid: wwafa1a3005a15a672
#  # 缓存前缀
#  redis-prefix: sdt

