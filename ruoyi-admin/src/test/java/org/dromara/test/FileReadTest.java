package org.dromara.test;

import cn.hutool.core.io.resource.ClassPathResource;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

public class FileReadTest {



    private static String template1;
    private String template2;
    private String template3;

    private static String parseFile(Tika tika, String fileName) throws IOException, TikaException {
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream inputStream = resource.getStream()) {
            File tempFile = File.createTempFile("temp-resource", getFileExtension(fileName));
            Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            String content = tika.parseToString(tempFile);
            tempFile.deleteOnExit();
            return content;
        }
    }

    private static String getFileExtension(String fileName) {
        int lastIndex = fileName.lastIndexOf(".");
        return lastIndex != -1 ? fileName.substring(lastIndex) : "";
    }

    public static void main(String[] args) {
        try {
            Tika tika = new Tika();
            //交通模板
            template1 = parseFile(tika, "test1.xlsx");
            System.out.printf("template1: %s\n", template1);
        } catch (IOException | TikaException e) {
            throw new RuntimeException(e);
        }
    }



}
