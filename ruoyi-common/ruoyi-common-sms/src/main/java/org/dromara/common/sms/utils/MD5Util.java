package org.dromara.common.sms.utils;


import org.dromara.common.core.utils.StringUtils;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/** MD5加密工具类
 * <AUTHOR>
 */
public class MD5Util {
    /** MD5 */
    private static final String hexDigIts[] = { "0", "1", "2", "3", "4", "5",
        "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

    /** MD5加密 */
    public static String MD5Encode(String origin, String charsetname) {
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (null == charsetname || "".equals(charsetname)) {
                resultString = byteArrayToHexString(md.digest(resultString
                    .getBytes()));
            } else {
                resultString = byteArrayToHexString(md.digest(resultString
                    .getBytes(charsetname)));
            }
        } catch (Exception e) {
        }
        return resultString;
    }

    public static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    public static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n += 256;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigIts[d1] + hexDigIts[d2];
    }

    public static void main(String[] args) {
        String sendUrl = "http://address:port/sms/api/sendMessage";
        String userName = "联信征信";
        String content = "测试内容";
        Long timestamp = System.currentTimeMillis();
        String password = "8rpN4g";
        String strPassword = MD5Util.MD5Encode(password, "utf8");
        //群发接口sign
        String sign1 = MD5Util.MD5Encode(userName + content + timestamp + strPassword, "utf8");
        //单发接口sign
        String sign2 = MD5Util.MD5Encode(userName + timestamp + strPassword, "utf8");
        System.out.println(sign1);
        System.out.println(sign2);
    }

    /**
     * BASE64解密关键字
     */
    public static String decode(String string) {
        if (StringUtils.isEmpty(string)) {
            return "";
        }
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(string);
            String resultString = new String(decodedBytes, StandardCharsets.UTF_8);
            resultString = URLDecoder.decode(resultString, StandardCharsets.UTF_8.toString());
            return resultString;
        } catch (IOException e) {
            // 记录日志而不是打印堆栈跟踪
            System.err.println("Error decoding string: " + e.getMessage());
            return "";
        }
    }

}
