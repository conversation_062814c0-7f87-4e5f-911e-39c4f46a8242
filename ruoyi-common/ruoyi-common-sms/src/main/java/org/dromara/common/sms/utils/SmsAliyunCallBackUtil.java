package org.dromara.common.sms.utils;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponseBody;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.tea.*;
import org.dromara.common.core.utils.DateUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.teautil.Common.toJSONString;

public class SmsAliyunCallBackUtil {

    /**
     * <b>description</b> :
     * <p>使用AK&amp;SK初始化账号Client</p>
     * @return Client
     *
     * @throws Exception
     */
    public static Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        Config config = new Config()
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            .setAccessKeyId("LTAI5tKU7CN4DFdQDwrT9r2E")
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            .setAccessKeySecret("******************************");
        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    public static void main(String[] args_) throws Exception {
        java.util.List<String> args = java.util.Arrays.asList(args_);
        Client client = SmsAliyunCallBackUtil.createClient();
        com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest querySendDetailsRequest = new com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest()
            .setPhoneNumber("17806187510")
            .setSendDate("20250304")
            .setPageSize(1L)
            .setBizId("122015941070808205^0")
            .setCurrentPage(1L);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            QuerySendDetailsResponse querySendDetailsResponse = client.querySendDetailsWithOptions(querySendDetailsRequest, runtime);
            Long sendStatus = querySendDetailsResponse.getBody().getSmsSendDetailDTOs().getSmsSendDetailDTO().get(0).getSendStatus();
            System.out.println(querySendDetailsResponse);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

    public static String cheSendStatus(String bizId,String phoneNumber) throws Exception{
        Client client = SmsAliyunCallBackUtil.createClient();
        String sendDate = DateUtils.dateTime();
        com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest querySendDetailsRequest = new com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest()
            .setPhoneNumber(phoneNumber)
            .setSendDate(sendDate)
            .setPageSize(1L)
            .setBizId(bizId)
            .setCurrentPage(1L);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            QuerySendDetailsResponse querySendDetailsResponse = client.querySendDetailsWithOptions(querySendDetailsRequest, runtime);
            System.out.println(JSON.toJSONString(querySendDetailsResponse));
            System.out.println(JSON.toJSONString(querySendDetailsResponse.getBody()));
            List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> smsSendDetailDTOs = querySendDetailsResponse.getBody().getSmsSendDetailDTOs().getSmsSendDetailDTO();
            Long sendStatus = 1L;
            if(smsSendDetailDTOs != null && !smsSendDetailDTOs.isEmpty()){
                sendStatus = querySendDetailsResponse.getBody().getSmsSendDetailDTOs().getSmsSendDetailDTO().get(0).getSendStatus();
            }
            return sendStatus.toString();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            return "error.message";
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            return "error.message";
        }
    }
}



