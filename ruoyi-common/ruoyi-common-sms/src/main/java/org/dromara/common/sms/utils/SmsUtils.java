package org.dromara.common.sms.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;

import java.io.IOException;
import java.time.Duration;

/**
 * 短信工具类
 * <AUTHOR> 2020年9月23日
 */
public class SmsUtils {

    /**
     * 新版短信平台对接接口
     * @param yzm
     * @param phone
     * @return
     */
	public static String sendSms(String yzm,String phone){
        String url="http://smsapi.lianluxinxi.com:8001/sms/api/sendMessageOne";
        //创建连接对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //创建请求
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Accept","application/json");
        httpPost.setHeader("Content-Type","application/json;charset=utf-8");
        String userName = "联信征信";
        Long timestamp = System.currentTimeMillis();
        String password = "8rpN4g";
        String strPassword = MD5Util.MD5Encode(password, "utf8");
        String content = "【联信数科】您本次的验证码为" + yzm + " ，如非本人操作，请忽略本短信。";
        JSONObject message = new JSONObject();
        message.put("phone", phone);
        message.put("content", "【联信数科】您本次的验证码为" + yzm + "，如非本人操作，请忽略本短信。");
        JSONArray messageList = new JSONArray();
        messageList.add(message);
        String sign = MD5Util.MD5Encode(userName + timestamp + strPassword, "utf8");
        JSONObject json = new JSONObject();
        json.put("userName",userName);
        json.put("content",content);
        json.put("messageList",messageList);
        json.put("timestamp",timestamp);
        json.put("sign",sign);
        StringEntity entity = new StringEntity(json.toJSONString(), "UTF-8");
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if(state == HttpStatus.SC_OK){
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity, "UTF-8");
                System.out.println(jsonString);
                if(!StringUtils.isEmpty(jsonString)){
                    JSONObject smsResponse = JSONObject.parseObject(jsonString);
                    if (smsResponse != null && smsResponse.getInteger("code") == 0) {
                        return "success";
                    }
                }
                return "error";
            }
        }catch (ClientProtocolException e){
            e.printStackTrace();
        }catch (IOException e){
            e.printStackTrace();
        }
        finally {
            try {
                if(response != null){
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "error";
    }




}
