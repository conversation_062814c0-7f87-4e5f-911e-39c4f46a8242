package com.lianxin.common.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;

/**
 * 联麓短信工具类
 * @date 2020/4/17 16:09
 */
@Slf4j
public class SmsLianLuUtils {
    /**
     * url
     */
    private static String url = "https://apis.shlianlu.com/sms/trade/template/send";

    /**
     * 企业ID
     */
    private final static String mchId = "1033232";

    /**
     * appID
     */
    private final static String appId = "10011692930095242";

    /**
     * key
     */
    private final static String key = "081f6d788e40452ea336d4bf7cddf87e";


    /**
     * 发送短信验证码
     * @param phoneNumber 手机号
     * @param sms 短信验证码
     */
    public static String sendCaptcha(String phoneNumber,String sms) {
        return sendSms(phoneNumber,sms,"70226936");
    }

    /**
     * 发送短信审核结果
     * @param phoneNumber 手机号
     */
    public static void sendApprvoalResult(String phoneNumber) {
        sendSms(phoneNumber,"","70226937");
    }


    /**
     * 发送短信
     * @param phoneNumber 手机号
     * @param sms 短信验证码
     * @param templeteId 模板ID
     *    70226937  【联信数科】您的企业认证审核失败，请登录平台查看具体原因，重新认证审核。
     *    70226936  【联信数科】您本次的验证码为{%@6%}，如非本人操作，请忽略本短信。
     */
    public static String sendSms(String phoneNumber,String sms,String templeteId){
        // 封装请求参数
        JSONObject params = new JSONObject();
        params.set("MchId",mchId);
        params.set("AppId",appId);
        // 固定值
        params.set("Version","1.2.0");
        // 固定值
        params.set("Type","3");
        params.set("PhoneNumberSet",new String[]{phoneNumber});
        params.set("TemplateId",templeteId);
        params.set("TemplateParamSet",new String[]{sms});
        params.set("SessionContext","");
        params.set("TimeStamp",String.valueOf(System.currentTimeMillis()));
        // 加密方式
        params.set("SignType","MD5");
        // 数字签名的加密前字符串
        String SignatureStr = "AppId=" + appId
            + "&MchId=" + mchId
            + "&SignType=MD5"
            + "&TemplateId=" + templeteId
            + "&TimeStamp=" + params.get("TimeStamp")
            + "&Type=3"
            + "&Version=1.2.0"
            + "&key="+ key;
        params.set("Signature", SecureUtil.md5(SignatureStr).toUpperCase());

        // 发起http请求
        String resultStr = HttpUtil.createPost(url)
            .header("Accept", "application/json")
            .header("Content-Type", "application/json;charset=utf-8")
            .body(params.toString())
            .execute().body();
        // 短信反参
        // {"count":1,"tag":"","message":"success","taskId":"202503251010183587400","timestamp":1742869114368,"status":"00"}
//        log.info("短信发送结果：" + resultStr);
        if(StringUtils.isNotEmpty(resultStr) && resultStr.contains("success")){
            return "success";
        }
        return "error";
    }
}
