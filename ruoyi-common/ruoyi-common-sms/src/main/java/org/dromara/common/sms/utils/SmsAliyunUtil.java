package org.dromara.common.sms.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import org.dromara.common.core.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.teautil.Common.toJSONString;

public class SmsAliyunUtil {

    public static Client createClient() throws Exception {
        Config config = new Config()
            // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
//            .setAccessKeyId(System.getenv("LTAI5tKU7CN4DFdQDwrT9r2E"))
            .setAccessKeyId("LTAI5tKU7CN4DFdQDwrT9r2E")
            // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
//            .setAccessKeySecret(System.getenv("******************************"));
            .setAccessKeySecret("******************************");

        // 配置 Endpoint
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client(config);
    }


    public static String sendSms(String yzm,String phone) throws Exception{
        Client client = SmsAliyunUtil.createClient();
        Map templateParam = new HashMap<>();
        templateParam.put("code", yzm);
        // 构造请求对象，请替换请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
            .setPhoneNumbers(phone)
            .setSignName("联信数科")
            .setTemplateCode("SMS_479840085")
            .setTemplateParam(JSON.toJSONString(templateParam)); // TemplateParam为序列化后的JSON字符串
        // 获取响应对象
        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
        // 响应包含服务端响应的 body 和 headers
        System.out.println(toJSONString(sendSmsResponse));
        // 响应包含服务端响应的 body 和 headers
        if (sendSmsResponse != null && sendSmsResponse.getBody().getCode().equals("OK")){
            String bizId = sendSmsResponse.getBody().getBizId();
            if(StringUtils.isEmpty(bizId)){
                return "error";
            }
            //调用状态查询接口,根据回执状态确认短信是否发送成功
            String status = SmsAliyunCallBackUtil.cheSendStatus(bizId,phone);
            if(ObjectUtil.equals(status,"1")){
                //等待回执，执行三次轮训，每次间隔三秒钟
                int i = 20;
                while (i > 0){
                    Thread.sleep(3000);
                    //调用状态查询接口
                    status = SmsAliyunCallBackUtil.cheSendStatus(bizId,phone);
                    //回执状态为成功时跳出
                    if(ObjectUtil.equals(status,"3")){
                        return "success";
                    }else if(ObjectUtil.equals(status,"2")){
                        return "error";
                    }else {
                        i--;
                    }
                }
                return "error";
            }else if (ObjectUtil.equals(status,"3")){
                return "success";
            }else {
                return "error";
            }
        }else {
            return "error";
        }
    }
}



