package org.dromara.common.core.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ChatMessageSendRespVO {

    /**
     * 发送消息
     */
    private Message send;

    /**
     * 接收消息
     */
    private Message receive;

    /**
     * 消息
     */
    @Data
    public static class Message {

        /**
         * 编号
         */
        private Long id;

        /**
         * 消息类型
         */
        private String type; // 参见 MessageType 枚举类

        /**
         * 聊天内容
         */
        private String content;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 上传附件信息(Json)
         */
        private String fileInfo;

        /**
         * 引用文本
         */
        private String quoteInfo;

    }

}
