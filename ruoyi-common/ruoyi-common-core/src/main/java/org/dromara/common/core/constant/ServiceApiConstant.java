package org.dromara.common.core.constant;



/**
 * 业务常量接口
 */
public interface ServiceApiConstant {


    //各县区画像统计
    static String getAreaHuaCount = "/business/company/getAreaHuaCount";

    //累计为全市（x家）家企业"画像
    static String getAllComCount = "/business/company/getAllComCount";

    //中小企业（x）家
    static String getUsccComCount = "/business/company/getUsccComCount";

    //规上企业（x）家
    static String getRuleComCount = "/business/company/getRuleComCount";

    //查询XX企业信息
    static String getComDetails = "/business/company/getComDetails";

    //生成XX企业精准画像
    static String businessPortraits = "/business/comcharts/businessPortraits";

    //统计县区企业数量
    static String getComCount = "/business/company/getComCount";

    //查询企业等级数量
    static String getComRank = "/business/company/getComRank";

    //查询企业评分前十排名
    static String getComScore = "/business/company/getComScore";







}
