package org.dromara.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * AI模型code
     */
    String AI_MODEL_CODE = "global:ai_chat_modle#2m";

    /**
     * AI模型key
     */
    String AI_MODEL_KEY = "global:ai_chat_modle_key:";

    /**
     * AI模型key的URL
     */
    String AI_MODEL_KEY_URL = "global:ai_chat_modle_url:";




}
