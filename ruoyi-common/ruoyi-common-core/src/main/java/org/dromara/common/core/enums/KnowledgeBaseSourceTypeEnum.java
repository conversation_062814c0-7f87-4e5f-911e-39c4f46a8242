package org.dromara.common.core.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 知识库来源的数据类型
 */

public enum KnowledgeBaseSourceTypeEnum {
    /**
     * 个人
     */
    PERSONAL("personal", "个人"),

    /**
     * 部门
     */
    DEPARTMENT("department", "部门"),

    /**
     * 院
     */
    INSTITUTE("institute", "院"),

    /**
     * 分享
     */
    SHARED("shared", "分享");

    private final String code;
    private final String info;

    KnowledgeBaseSourceTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 根据code获取枚举
     */
    public static KnowledgeBaseSourceTypeEnum getByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (KnowledgeBaseSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
