package org.dromara.common.core.utils;


import java.util.UUID;

/**
 * 获取唯一编码工具栏  24位编码（模型key）
 *
 * <AUTHOR>
 */
public class GenerateCodeUtils {


    private static final String ALPHA_NUMERIC_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int STRING_LENGTH = 24;
    private static final String PREFIX = "Lx-";


    /**
     * 生成一个24位的字符串，包括字母和UUID的部分字符。
     * @return 生成的字符串
     */
    public static String generateAlphaNumeric() {
        StringBuilder sb = new StringBuilder();
        // 获取UUID并截取部分字符
        String uuidPart = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 12);
        sb.append(uuidPart);

        // 剩余部分用字母填充
        for (int i = 0; i < STRING_LENGTH - uuidPart.length(); i++) {
            int index = (int) (ALPHA_NUMERIC_STRING.length() * Math.random());
            sb.append(ALPHA_NUMERIC_STRING.charAt(index));
        }

        return sb.toString();
    }


}
