package org.dromara.common.core.utils.ip;

import cn.hutool.core.net.NetUtil;
import cn.hutool.http.HtmlUtil;
import org.dromara.common.core.utils.StringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取地址类
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AddressUtils {

    // 未知地址
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIP(String ip) {
//        try {
//            if (StringUtils.isBlank(ip)) {
//                return UNKNOWN;
//            }
//            // 内网不查询
//            ip = StringUtils.contains(ip, "0:0:0:0:0:0:0:1") ? "127.0.0.1" : HtmlUtil.cleanHtmlTag(ip);
//            if (NetUtil.isInnerIP(ip)) {
//                return "内网IP";
//            }
//            return RegionUtils.getCityInfo(ip);
//        }catch (Exception e){
//            log.error("ip查询异常 {}", ip);
//            return "";
//        }
        return "";
    }
}
