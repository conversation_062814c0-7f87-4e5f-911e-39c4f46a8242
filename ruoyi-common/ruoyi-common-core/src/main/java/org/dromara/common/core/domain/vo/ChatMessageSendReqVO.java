package org.dromara.common.core.domain.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
public class ChatMessageSendReqVO {

    /**
     * 聊天对话编号
     */
    private Long conversationId;

    /**
     * 用户提示词
     */
    @NotEmpty(message = "用户提示词不能为空")
    private String userMessage;

    /**
     * 系统消息
     */
    private String systemMessage;

    /**
     * 知识库
     */
    private String knowledgeBaseId;

    /**
     * 多选知识库id
     */
    private List<String> knowledgeBaseIdList;

    /**
     * 在线搜索 0否 1是
     */
    private Integer onlineSearch = 0;

    /**
     * 文件ossId集合
     */
    private List<Long> fileIdList;

    /**
     * 文件文本内容
     */
    private List<ContentEntity> referMessage;

    /**
     * 知识库检索配置
     */
    private RetrieveConfig retrieveConfig;


    /**
     * 是否携带参考文件引用 0-不携带 1-携带
     */
    private Integer referenceFlag = 1;


    /**
     * 文本内容实体
     */
    @Data
    public static class ContentEntity {

        /**
         * 文件名.后缀
         */
        private String fileName;

        /**
         * 参考内容
         */
        private String referContent;

        /**
         * 文件大小
         */
        private String fileSize;

        /**
         * 文件地址
         */
        private String fileUrl;

    }

    /**
     * 知识库检索配置
     */
    @Data
    public static class RetrieveConfig {

        /**
         * 检索片段上限（默认4）
         */
        private Integer topK = 4;

        /**
         * 相似度（默认0.25）
         */
        private Double similarityThreshold = 0.25;

        /**
         * 文件字符召回（默认5000）
         */
        private Integer fileRecall = 5000;
    }
}
