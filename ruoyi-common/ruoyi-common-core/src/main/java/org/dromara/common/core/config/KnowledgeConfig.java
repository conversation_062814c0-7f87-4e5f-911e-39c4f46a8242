package org.dromara.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 知识库配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge")
public class KnowledgeConfig {

    /**
     * 每个知识库最大文件数量
     */
    private Integer maxFileCount = 100;

    /**
     * 个人知识库最大文件数量
     */
    private Integer maxPersonalFileCount = 10;

}
