package org.dromara.common.encrypt.filter;

import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.core.constant.HttpStatus;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.encrypt.properties.ApiDecryptProperties;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;


/**
 * Crypto 过滤器
 *
 * <AUTHOR>
 */
public class CryptoFilter implements Filter {
    private final ApiDecryptProperties properties;
    private final List<String> exculteUrl = Arrays.asList(
        "/ai/generateStreamChat",
        "/ai/generateStream",
        "/v2/ai/generateStream",
        "/v2/ai/generateStreamChat"
    );

    public CryptoFilter(ApiDecryptProperties properties) {
        this.properties = properties;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest servletRequest = (HttpServletRequest) request;
        HttpServletResponse servletResponse = (HttpServletResponse) response;

        ServletRequest requestWrapper = null;
        ServletResponse responseWrapper = null;
        EncryptResponseBodyWrapper responseBodyWrapper = null;

        boolean flag = false;
        // 是否为 put 或者 post 请求
        if (!"OPTIONS".equals(servletRequest.getMethod())) {
            // 不在放行白名单内，均需要做解密处理
            flag = Arrays.stream(properties.getExcludeUrl()).noneMatch(
                url -> url.equals(servletRequest.getRequestURI()) || servletRequest.getRequestURI().startsWith(url)
            );
            if (flag) {
                // 是否存在加密标头
                String headerValue = servletRequest.getHeader(properties.getHeaderFlag());
                if (StringUtils.isNotBlank(headerValue)) {
                    // 请求解密
                    requestWrapper = new DecryptRequestBodyWrapper(servletRequest, properties.getPrivateKey(), properties.getHeaderFlag());
                } else {
                    // 是否有注解，有就报错，没有放行
                    HandlerExceptionResolver exceptionResolver = SpringUtils.getBean("handlerExceptionResolver", HandlerExceptionResolver.class);
                    exceptionResolver.resolveException(
                        servletRequest, servletResponse, null,
                        new ServiceException("没有访问权限，请联系管理员授权", HttpStatus.FORBIDDEN));
                    return;
                }
            } else {
                // 是否存在加密标头
                String headerValue = servletRequest.getHeader(properties.getHeaderFlag());
                if (StringUtils.isNotBlank(headerValue)) {
                    // 请求解密
                    requestWrapper = new DecryptRequestBodyWrapper(servletRequest, properties.getPrivateKey(), properties.getHeaderFlag());
                }
            }
        }
        //流式会话不做响应加密
        boolean flagTwo = exculteUrl.stream().noneMatch(
            url -> url.equals(servletRequest.getRequestURI()) || servletRequest.getRequestURI().startsWith(url)
        );

        //如果是BS端上传文件，请求不做加密，响应需要
        if (!"OPTIONS".equals(servletRequest.getMethod())) {
            if (servletRequest.getRequestURI().contains("uploadMinio")) {
                flag = true;
                flagTwo = true;
            }
        }

        // 判断是否响应加密
        if (flag && flagTwo) {
            responseBodyWrapper = new EncryptResponseBodyWrapper(servletResponse);
            responseWrapper = responseBodyWrapper;
        }

        chain.doFilter(
            ObjectUtil.defaultIfNull(requestWrapper, request),
            ObjectUtil.defaultIfNull(responseWrapper, response));

        if (flag && flagTwo) {
            servletResponse.reset();
            // 对原始内容加密
            String encryptContent = responseBodyWrapper.getEncryptContent(
                servletResponse, properties.getPublicKey(), properties.getHeaderFlag());
            // 对加密后的内容写出
            servletResponse.getWriter().write(encryptContent);
        }
    }


    @Override
    public void destroy() {
    }
}
