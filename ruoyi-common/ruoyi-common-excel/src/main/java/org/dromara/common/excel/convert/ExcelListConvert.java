package org.dromara.common.excel.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * List 转换
 * Excel 数值长度位15位 大于15位的数值转换位字符串
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelListConvert implements Converter<List<String>> {

    @Override
    public Class<List> supportJavaTypeKey() {
        return List.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public List<String> convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return Arrays.asList(String.valueOf(cellData).split(","));
    }

    @Override
    public WriteCellData<String> convertToExcelData(List<String> list, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String str = "";
        if (CollUtil.isNotEmpty(list)) {
            str = StrUtil.join(",", list);
        }
        WriteCellData<String> cellData = new WriteCellData<>(str);
        cellData.setType(CellDataTypeEnum.STRING);
        return cellData;
    }

}
