package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.BsReviewIssues;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 审查问题记录视图对象 bs_review_issues
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BsReviewIssues.class)
public class BsReviewIssuesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 审查意见
     */
    @ExcelProperty(value = "审查意见")
    private String reviewComments;

    /**
     * 规范条文
     */
    @ExcelProperty(value = "规范条文")
    private String specificationClause;

    /**
     * 提出次数
     */
    @ExcelProperty(value = "提出次数")
    private Long submissionCount;

    /**
     * 意见类型
     */
    @ExcelProperty(value = "意见类型")
    private String commentType;

    /**
     * 所属类型
     */
    @ExcelProperty(value = "所属类型")
    private String categorys;
    /**
     * 提出人
     */
    @ExcelProperty(value = "提出人")
    private String proposers;
     /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectNames;

    /**
     * 设计单位
     */
    @ExcelProperty(value = "设计单位")
    private String designUnits;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;


}
