package org.dromara.business.domain.bo;

import org.dromara.business.domain.BsReviewIssues;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 审查问题记录业务对象 bs_review_issues
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BsReviewIssues.class, reverseConvertGenerate = false)
public class BsReviewIssuesBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 审查意见
     */
    @NotBlank(message = "审查意见不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reviewComments;

    /**
     * 规范条文
     */
    @NotBlank(message = "规范条文不能为空", groups = { AddGroup.class, EditGroup.class })
    private String specificationClause;

    /**
     * 提出次数
     */
    @NotNull(message = "提出次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long submissionCount;

    /**
     * 意见类型
     */
    @NotBlank(message = "意见类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String commentType;

    /**
     * 所属类型
     */
    @NotBlank(message = "所属类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String categorys;

    /**
     * 提出人
     */
    @NotBlank(message = "提出人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proposers;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectNames;

    /**
     * 设计单位
     */
    @NotBlank(message = "设计单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String designUnits;


}
