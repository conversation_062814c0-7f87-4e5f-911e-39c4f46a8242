package org.dromara.business.utils;

import org.dromara.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

/**
 * 知识库分享工具类
 */
public class KnowledgeShareUtils {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeShareUtils.class);

    /**
     * 加密密钥 (原始密钥)
     */
    private static final String SECRET_KEY = "RuoYi-Vue-Plus-Knowledge";

    /**
     * 密钥长度 (AES-128需要16字节)
     */
    private static final int KEY_LENGTH = 16;

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * 获取固定长度的密钥
     *
     * @return 16字节的密钥
     */
    private static byte[] getFixedLengthKey() {
        byte[] originalKey = SECRET_KEY.getBytes(StandardCharsets.UTF_8);
        byte[] fixedKey = new byte[KEY_LENGTH];
        
        if (originalKey.length >= KEY_LENGTH) {
            // 如果原始密钥长度大于等于16，只取前16个字节
            System.arraycopy(originalKey, 0, fixedKey, 0, KEY_LENGTH);
        } else {
            // 如果原始密钥长度小于16，填充至16字节
            Arrays.fill(fixedKey, (byte) 0); // 初始填充0
            System.arraycopy(originalKey, 0, fixedKey, 0, originalKey.length);
        }
        
        return fixedKey;
    }

    /**
     * AES加密知识库ID
     *
     * @param knowledgeId 知识库ID
     * @return 加密后的字符串
     */
    public static String encryptKnowledgeId(String knowledgeId) {
        if (StringUtils.isEmpty(knowledgeId)) {
            log.warn("知识库ID为空，无法加密");
            return null;
        }
        try {
            // 获取固定长度的16字节密钥
            byte[] keyBytes = getFixedLengthKey();
            
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);

            byte[] encryptedBytes = cipher.doFinal(knowledgeId.getBytes(StandardCharsets.UTF_8));
            String result = Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);

            log.debug("知识库ID加密成功，原始ID：{}，加密后：{}", knowledgeId, result);
            return result;
        } catch (Exception e) {
            log.error("知识库ID加密失败，原始ID：{}，错误信息：{}", knowledgeId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * AES解密知识库ID
     *
     * @param encryptedId 加密后的ID
     * @return 原始知识库ID
     */
    public static String decryptKnowledgeId(String encryptedId) {
        if (StringUtils.isEmpty(encryptedId)) {
            log.warn("加密ID为空，无法解密");
            return null;
        }
        try {
            // 获取固定长度的16字节密钥
            byte[] keyBytes = getFixedLengthKey();
            
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);

            byte[] decryptedBytes = cipher.doFinal(Base64.getUrlDecoder().decode(encryptedId));
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);

            log.debug("知识库ID解密成功，加密ID：{}，解密后：{}", encryptedId, result);
            return result;
        } catch (Exception e) {
            log.error("知识库ID解密失败，加密ID：{}，错误信息：{}", encryptedId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成分享链接
     *
     * @param pagePath 页面路径
     * @param knowledgeId 知识库ID
     * @return 分享链接
     */
    public static String generateShareUrl(String pagePath, String knowledgeId) {
        if (StringUtils.isEmpty(pagePath)) {
            log.warn("页面路径为空，无法生成分享链接");
            return null;
        }
        if (StringUtils.isEmpty(knowledgeId)) {
            log.warn("知识库ID为空，无法生成分享链接");
            return null;
        }
        
        String encryptedId = encryptKnowledgeId(knowledgeId);
        if (StringUtils.isEmpty(encryptedId)) {
            log.error("知识库ID加密失败，无法生成分享链接");
            return null;
        }
        
        String shareUrl = pagePath + "?id=" + encryptedId;
        log.info("生成分享链接成功，页面路径：{}，知识库ID：{}，分享链接：{}", pagePath, knowledgeId, shareUrl);
        return shareUrl;
    }
    
    /**
     * 生成分享链接（包含用户ID）
     *
     * @param pagePath 页面路径
     * @param knowledgeId 知识库ID
     * @param userId 用户ID
     * @return 分享链接
     */
    public static String generateShareUrl(String pagePath, String knowledgeId, String userId) {
        if (StringUtils.isEmpty(pagePath)) {
            log.warn("页面路径为空，无法生成分享链接");
            return null;
        }
        if (StringUtils.isEmpty(knowledgeId)) {
            log.warn("知识库ID为空，无法生成分享链接");
            return null;
        }
        if (StringUtils.isEmpty(userId)) {
            log.warn("用户ID为空，无法生成分享链接");
            return null;
        }
        
        String encryptedId = encryptKnowledgeId(knowledgeId);
        if (StringUtils.isEmpty(encryptedId)) {
            log.error("知识库ID加密失败，无法生成分享链接");
            return null;
        }
        
        String encryptedUserId = encryptKnowledgeId(userId);
        if (StringUtils.isEmpty(encryptedUserId)) {
            log.error("用户ID加密失败，无法生成分享链接");
            return null;
        }
        
        String shareUrl = pagePath + "?id=" + encryptedId + "&uid=" + encryptedUserId;
        log.info("生成包含用户ID的分享链接成功，页面路径：{}，知识库ID：{}，用户ID：{}，分享链接：{}", 
                pagePath, knowledgeId, userId, shareUrl);
        return shareUrl;
    }
}
