package org.dromara.business.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.business.domain.AiModelAuth;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 模型授权视图对象 ai_model_auth
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiModelAuth.class)
public class AiModelAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 模型id
     */
    @ExcelProperty(value = "模型id")
    private Long modelId;

    /**
     * 模型编码
     */
    @ExcelProperty(value = "模型编码")
    private String modelCode;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称")
    private String modelName;

    /**
     * 使用单位
     */
    @ExcelProperty(value = "使用单位")
    private String userCom;

    /**
     * 使用项目
     */
    @ExcelProperty(value = "使用项目")
    private String project;

    /**
     * 分配key
     */
    @ExcelProperty(value = "分配key")
    private String modelKey;

    /**
     * 到期时间
     */
    @ExcelProperty(value = "到期时间")
    private Date expirationDate;

    /**
     * 温度参数
     */
    @ExcelProperty(value = "温度参数")
    private Double temperature;

    /**
     * 最大 Token 数量
     */
    @ExcelProperty(value = "最大 Token 数量")
    private Long maxTokens;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

}
