package org.dromara.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.domain.bo.KnowledgeBaseBo;
import org.dromara.business.domain.dto.KnowledgeBaseMyListDTO;
import org.dromara.business.domain.vo.KnowledgeBaseListVo;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.utils.baseEntity.PageResult;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 知识库服务接口
 */
public interface IKnowledgeBaseService {

    /**
     * 查询知识库
     *
     * @param knowledgeId 知识库主键
     * @return 知识库
     */
    KnowledgeBaseVo queryById(Long knowledgeId);

    /**
     * 查询知识库列表
     *
     * @param bo                  知识库
     * @param knowledgeBaseListVo
     * @return 知识库集合
     */
    TableDataInfo<KnowledgeBaseVo> queryPageList(KnowledgeBaseBo bo, PageQuery pageQuery, KnowledgeBaseListVo knowledgeBaseListVo);

    /**
     * 设置知识库来源类型和部门名称
     *
     * @param knowledgeBaseVoList 知识库VO列表
     */
    void fillKnowledgeBaseSourceType(List<KnowledgeBaseVo> knowledgeBaseVoList);

    /**
     * 查询知识库列表
     *
     * @param bo 知识库
     * @return 知识库集合
     */
    List<KnowledgeBaseVo> queryList(KnowledgeBaseBo bo);

    /**
     * 查询部门知识库列表
     *
     * @param bo 知识库
     * @param deptId 部门ID
     * @param sortType 排序方式
     * @return 知识库集合
     */
    IPage<KnowledgeBaseVo> queryListDepartment(KnowledgeBaseBo bo, String deptId, String sortType,KnowledgeBaseMyListDTO dto);

    /**
     * 查询用户知识库列表
     *
     * @param bo 知识库
     * @param sortType 排序方式
     * @return 知识库集合
     */
    IPage<KnowledgeBaseVo> queryListUser(KnowledgeBaseBo bo, String sortType, KnowledgeBaseMyListDTO dto);

    /**
     * 查询院级知识库列表
     *
     * @param bo 知识库
     * @param sortType 排序方式
     * @return 知识库集合
     */
    IPage<KnowledgeBaseVo> queryInstituteList(KnowledgeBaseBo bo, String sortType,KnowledgeBaseMyListDTO dto);


    /**
     * 新增知识库
     *
     * @param bo 知识库
     * @return 结果
     */
    Boolean insertByBo(KnowledgeBaseBo bo);

    /**
     * 修改知识库
     *
     * @param bo 知识库
     * @return 结果
     */
    Boolean updateByBo(KnowledgeBaseBo bo);

    /**
     * 修改知识库校验权限，同级的部门管理员，以及上级的管理员都可以修改
     */
    void checkUpdatePermission(KnowledgeBaseBo bo);

    /**
     * 校验并批量删除知识库信息
     *
     * @param ids 需要删除的知识库主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    List<KnowledgeBaseVo> queryInstituteListDept(KnowledgeBaseBo bo);

    /**
     * 根据分类ID查询院级知识库列表
     *
     * @param bo 知识库
     * @param categoryId 分类ID
     * @return 知识库集合
     */
    List<KnowledgeBaseVo> queryInstituteListDeptByCategory(KnowledgeBaseBo bo, Long categoryId);

    /**
     * 创建知识库并建立用户关联
     *
     * @param bo 知识库信息
     * @return 结果
     */
    Boolean createKnowledgeBase(KnowledgeBaseBo bo);

    /**
     * 获取知识库检索信息
     * @param knowledgeBaseId 知识库id
     * @param userMessage 用户提示词
     * @param loginUser 登录用户
     * @return 检索结果
     */
    Map<String, String> getDocumentInfos(String knowledgeBaseId, String userMessage, LoginUser loginUser) throws Exception ;

    /**
     * 获取知识库检索信息（带检索配置）
     * @param knowledgeBaseId 知识库id
     * @param userMessage 用户提示词
     * @param loginUser 登录用户
     * @param retrieveConfig 检索配置
     * @return 检索结果
     */
    Map<String, String> getDocumentInfos(String knowledgeBaseId, String userMessage, LoginUser loginUser,
                                        org.dromara.common.core.domain.vo.ChatMessageSendReqVO.RetrieveConfig retrieveConfig) throws ServiceException ;

    List<KnowledgeBase> listByDept(KnowledgeBaseBo bo);

    /**
     * 知识库-并行检索
     * @param knowledgeBaseIdList 选中知识库id
     * @param userMessage 用户提示词
     * @return 检索结果
     */
    Map<String, Object> getDocumentInfosParallel(List<String> knowledgeBaseIdList, String userMessage);

    /**
     * 知识库-并行检索（带检索配置）
     * @param knowledgeBaseIdList 选中知识库id
     * @param userMessage 用户提示词
     * @param retrieveConfig 检索配置
     * @return 检索结果
     */
    Map<String, Object> getDocumentInfosParallel(List<String> knowledgeBaseIdList, String userMessage,
                                                org.dromara.common.core.domain.vo.ChatMessageSendReqVO.RetrieveConfig retrieveConfig) throws ServiceException;
}
