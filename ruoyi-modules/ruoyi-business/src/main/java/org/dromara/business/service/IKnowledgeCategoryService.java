package org.dromara.business.service;

import org.dromara.business.domain.KnowledgeCategory;
import org.dromara.business.dto.KnowledgeCategoryDTO;
import org.dromara.business.vo.KnowledgeCategoryVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 知识库分类Service接口
 */
public interface IKnowledgeCategoryService extends IService<KnowledgeCategory> {

    /**
     * 分页查询知识库分类
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    TableDataInfo<KnowledgeCategoryVO> pageKnowledgeCategories(KnowledgeCategoryDTO dto);

    /**
     * 查询所有知识库分类列表
     *
     * @return 知识库分类列表
     */
    List<KnowledgeCategoryVO> listAllCategories();

    /**
     * 根据ID获取知识库分类详情
     *
     * @param id 知识库分类ID
     * @return 知识库分类信息
     */
    KnowledgeCategoryVO getKnowledgeCategoryById(Long id);

    /**
     * 新增知识库分类
     *
     * @param dto 知识库分类信息
     * @return 结果
     */
    boolean addKnowledgeCategory(KnowledgeCategoryDTO dto);

    /**
     * 修改知识库分类
     *
     * @param dto 知识库分类信息
     * @return 结果
     */
    boolean updateKnowledgeCategory(KnowledgeCategoryDTO dto);

    /**
     * 删除知识库分类
     *
     * @param id 知识库分类ID
     * @return 结果
     */
    boolean deleteKnowledgeCategory(Long id);

    /**
     * 根据多个部门ID查询分类列表
     *
     * @param deptIds 部门ID列表
     * @return 知识库分类列表
     */
    List<KnowledgeCategoryVO> listCategoriesByDeptIds(List<Long> deptIds);

    Long topDeptId();
}
