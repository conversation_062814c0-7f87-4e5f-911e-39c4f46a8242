package org.dromara.business.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.KbDatabaseConfigVo;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.service.IKbDatabaseConfigService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 知识库-数据库配置信息
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/databaseConfig")
public class KbDatabaseConfigController extends BaseController {

    private final IKbDatabaseConfigService kbDatabaseConfigService;

    /**
     * 查询知识库-数据库配置信息列表
     */
    @SaCheckPermission("business:databaseConfig:list")
    @GetMapping("/list")
    public TableDataInfo<KbDatabaseConfigVo> list(KbDatabaseConfigBo bo, PageQuery pageQuery) {
        return kbDatabaseConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出知识库-数据库配置信息列表
     */
    @SaCheckPermission("business:databaseConfig:export")
    @Log(title = "知识库-数据库配置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(KbDatabaseConfigBo bo, HttpServletResponse response) {
        List<KbDatabaseConfigVo> list = kbDatabaseConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "知识库-数据库配置信息", KbDatabaseConfigVo.class, response);
    }

    /**
     * 获取知识库-数据库配置信息详细信息
     * @param knowledgeId 知识库id
     */
    @GetMapping("/getInfoById")
    public R<KbDatabaseConfigVo> getInfoById(@RequestParam(value = "knowledgeId") Long knowledgeId) {
        return R.ok(kbDatabaseConfigService.getInfoById(knowledgeId));
    }

    /**
     * 新增知识库-数据库配置信息
     */
    @SaCheckPermission("business:databaseConfig:add")
    @Log(title = "知识库-数据库配置信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody KbDatabaseConfigBo bo) {
        return toAjax(kbDatabaseConfigService.insertByBo(bo));
    }

    /**
     * 修改知识库-数据库配置信息
     */
    @SaCheckPermission("business:databaseConfig:edit")
    @Log(title = "知识库-数据库配置信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody KbDatabaseConfigBo bo) {
        return toAjax(kbDatabaseConfigService.updateByBo(bo));
    }

    /**
     * 删除知识库-数据库配置信息
     *
     * @param databaseConfigIds 主键串
     */
    @SaCheckPermission("business:databaseConfig:remove")
    @Log(title = "知识库-数据库配置信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{databaseConfigIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] databaseConfigIds) {
        return toAjax(kbDatabaseConfigService.deleteWithValidByIds(List.of(databaseConfigIds), true));
    }
}
