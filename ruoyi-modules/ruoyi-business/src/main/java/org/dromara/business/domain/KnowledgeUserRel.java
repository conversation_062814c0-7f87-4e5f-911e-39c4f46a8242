package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 知识库用户关联实体类
 */
@Data
@TableName("bs_knowledge_user_rel")
public class KnowledgeUserRel extends BaseEntity {

    /**
     * 关联ID
     */
    @TableId
    private Long relId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 权限类型（0查看 1编辑 2管理）
     */
    private String permissionType;


    /**
     * 来源类型
     */
    private String sourceType;
}
