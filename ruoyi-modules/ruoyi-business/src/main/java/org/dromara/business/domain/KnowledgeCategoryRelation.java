package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库分类关联对象
 */
@TableName(value = "knowledge_category_relation")
@Data
public class KnowledgeCategoryRelation extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long categoryId;

    private Long knowledgeId;
}
