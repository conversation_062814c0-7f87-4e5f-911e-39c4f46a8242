package org.dromara.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.dromara.business.domain.*;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.mapper.AiChatMessageMapper;
import org.dromara.business.service.IAiModelAuthService;
import org.dromara.business.service.IAiModelService;
import org.dromara.business.service.IChatV2Service;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendReqVO;
import org.dromara.common.core.domain.vo.ChatMessageSendRespVO;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysOssVo;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatV2ServiceImpl implements IChatV2Service {

    //前端支持的图片类型
    private final List<String> imgType = Arrays.asList(".png", ".jpg", ".jpeg");
    @Resource
    private AiChatConversationServiceImpl aiChatConversationService;
    @Resource
    private AiChatMessageMapper aiChatMessageMapper;
    @Resource
    private StatisticsServiceImpl statisticsService;
    @Resource
    private IAiModelService aiModelService;
    @Resource
    private IAiModelAuthService aiModelAuthService;
    @Resource
    private AiModelChildServiceImpl aiModelChildService;
    @Resource
    private DataSaveServiceImpl dataSaveService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    //提取文件内容
    private String parseFile(Tika tika, String fileName) throws IOException, TikaException {
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream inputStream = resource.getStream()) {
            File tempFile = File.createTempFile("temp-resource", getFileExtension(fileName));
            Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            String content = tika.parseToString(tempFile);
            tempFile.deleteOnExit();
            return content;
        }
    }

    //提取文件扩展名
    private String getFileExtension(String fileName) {
        int lastIndex = fileName.lastIndexOf(".");
        return lastIndex != -1 ? fileName.substring(lastIndex) : "";
    }


    /**
     * 流式生成会话（公共）
     */
    @Override
    public Flux<R<ChatMessageSendRespVO>> generateStreamChat(ChatMessageSendReqVO chatMessageSendReqVO, String lockKey) {
        try {
            //前置条件，校验模型
            HttpServletRequest request = ServletUtils.getRequest();
            if (request != null) {
                String key = request.getHeader("Model-Key");
                if (StringUtils.isEmpty(key)) {
                    return Flux.just(R.fail("令牌缺失"));
                }

                //根据令牌查询授权信息
                AiModelAuth aiModelAuth = aiModelAuthService.getAuthInChat(key);
                if (aiModelAuth == null) {
                    return Flux.just(R.fail("无效令牌"));
                }
                if (aiModelAuth.getExpirationDate().getTime() < System.currentTimeMillis()) {
                    return Flux.just(R.fail("令牌已过期"));
                }
                //查询并判断模型信息
                AiModel aiModel = aiModelService.getModelInfoInChat(aiModelAuth.getModelId());
                if (aiModel == null) {
                    return Flux.just(R.fail("未找到令牌对应的模型，请联系管理员"));
                }
                if (aiModel.getStatus() == 0) {
                    return Flux.just(R.fail("令牌对应的模型未开启，请联系管理员"));
                }
                //校验会话是否存在
                AiChatConversation aiChatConversation = aiChatConversationService.getChatConversation(chatMessageSendReqVO.getConversationId());
                if (null == aiChatConversation) {
                    return Flux.just(R.fail("请开启一个新对话"));
                }

                //上传文件和联网搜索互斥。此处做校验
                if (chatMessageSendReqVO.getOnlineSearch() == 1 && CollUtil.isNotEmpty(chatMessageSendReqVO.getFileIdList())) {
                    return Flux.just(R.fail("联网模式下请不要上传参考文件哦"));
                }

                //定义上传的信息
                Map<String, Object> files = new HashMap<>();
                //知识库信息
                Map<String, Object> knowledge;
                //定义基础提示词
                String allPrompt = """
                    {0}
                    用户的问题: {1}
                    """;

                //判断联网搜索，或者提供的文档
                if (chatMessageSendReqVO.getOnlineSearch() == 1) {
                    log.info("联网搜索暂未开发");
                } else if (CollUtil.isNotEmpty(chatMessageSendReqVO.getFileIdList())) {
                    if (chatMessageSendReqVO.getFileIdList().size() == 1) {
                        SysOssVo content = dataSaveService.getFileContent(chatMessageSendReqVO.getFileIdList().get(0));
                        if (null != content) {
                            if (imgType.contains(content.getFileSuffix())) {
                                files.put("上传图片1", content.getFileContent());
                            } else {
                                files.put("上传文件1", content.getFileContent());
                            }
                        }
                    } else {
                        files = doThread(chatMessageSendReqVO.getFileIdList());
                    }
                } else if (CollUtil.isNotEmpty(chatMessageSendReqVO.getReferMessage())) {
                    JSONArray jsonArray = new JSONArray();
                    for (int i = 1; i <= chatMessageSendReqVO.getReferMessage().size(); i++) {
                        ChatMessageSendReqVO.ContentEntity contentEntity = chatMessageSendReqVO.getReferMessage().get(i - 1);
                        jsonArray.add(contentEntity.getReferContent());
                    }
                    files.put("用户文档", jsonArray);
                }
                //处理单选，多选兼容问题，后续全部适配后删除
                if (StringUtils.isNotBlank(chatMessageSendReqVO.getKnowledgeBaseId())) {
                    List<String> knowledgeBaseIdList = chatMessageSendReqVO.getKnowledgeBaseIdList();
                    if (knowledgeBaseIdList == null) {
                        knowledgeBaseIdList = new ArrayList<>();
                    }
                    if (!knowledgeBaseIdList.contains(chatMessageSendReqVO.getKnowledgeBaseId())) {
                        knowledgeBaseIdList.add(chatMessageSendReqVO.getKnowledgeBaseId());
                    }
                    chatMessageSendReqVO.setKnowledgeBaseIdList(knowledgeBaseIdList);
                }

                //增加系统提示词
                String prompt = """
                    1.根据用户提供的信息对用户的问题进行回答，如果提供的信息为空则不参考。
                    """;

                //如果知识库不为空
                if (chatMessageSendReqVO.getKnowledgeBaseIdList() != null && !chatMessageSendReqVO.getKnowledgeBaseIdList().isEmpty()) {
                    //判断是否登录
                    if (!LoginHelper.isLogin()) {
                        return Flux.just(R.fail("知识库功能需要登录后使用哦"));
                    }
                    //并行 检索知识库
                    knowledge = knowledgeBaseService.getDocumentInfosParallel(chatMessageSendReqVO.getKnowledgeBaseIdList(), chatMessageSendReqVO.getUserMessage(), chatMessageSendReqVO.getRetrieveConfig());
                    if (knowledge.get("查询结果") != null) {
                        String chaxunResult = JSON.toJSONString(knowledge.get("查询结果"));
                        //判断查询结果中是否包含知识库信息
                        if (chaxunResult.contains("知识库:") && chatMessageSendReqVO.getReferenceFlag()==1) {
                            prompt = """
                            1.根据用户提供的信息对用户的问题进行回答，如果提供的信息为空则不参考。
                            2.回答过程中如果参考了知识库中的内容,则追加以下内容:
                            <files>
                                引用文件：
                                @[filedId]{文件Id1}{文件名称1}
                                @[filedId]{文件Id2}{文件名称2}
                            </files>
                            """;
                        }else if(chaxunResult.contains("数据库:")){
                            prompt = """
                            1.根据用户提供的信息对用户的问题进行回答，如果提供的信息为空则不参考。
                            2.回答中去除json格式，用直白的语言描述查询结果。
                            """;
                        }
                        files.put("参考内容", chaxunResult);
                    }
                }
                //浏览器标识
                String browserId = request.getHeader("Browser");
                //获取历史消息
                List<AiChatMessage> aiChatMessages;
                aiChatMessages = aiChatMessageMapper.selectListByConversationId(chatMessageSendReqVO.getConversationId());
                //插入用户发送消息
                AiChatMessageVo userMessage = createChatMessage(aiChatConversation, "user", chatMessageSendReqVO, browserId);
                //插入assistant接收消息
                AiChatMessageVo assistantMessage = createChatMessage(aiChatConversation, "assistant", chatMessageSendReqVO, browserId);
                //调用模型 初始化构建模型，并调用
                StreamingChatModel chatModel = getChatModel(aiModel, key, browserId);
                //构建会话的消息 1加入历史消息
                List<ChatMessage> chatMessage = buildBasePrompt(aiChatMessages, false);
                //构建会话的消息 2加入用户消息
                String jsonStr = JSONUtil.toJsonStr(files);
                String userMessageStr = MessageFormat.format(allPrompt, files.isEmpty() ? "" : jsonStr, chatMessageSendReqVO.getUserMessage());
                chatMessage.add(UserMessage.from(userMessageStr));
                //如果对话窗口超限，则提示用户
                if (userMessageStr.length() > aiModel.getMaxTokens()) {
                    String tip = """
                        温馨提示：消息上限为：{0} 字符,用户参考内容占用：{1} 字符,已超限！
                        """;
                    return Flux.just(R.fail(MessageFormat.format(tip, aiModel.getMaxTokens(), jsonStr.length(), chatMessageSendReqVO.getUserMessage().length())));
                }
                String systemMessage = StringUtils.isEmpty(chatMessageSendReqVO.getSystemMessage()) ? prompt : chatMessageSendReqVO.getSystemMessage();
                chatMessage.add(SystemMessage.from(systemMessage));
                // 创建 Sinks.many() 作为响应式流
                Sinks.Many<String> sink = Sinks.many().unicast().onBackpressureBuffer();
                // 创建流式响应处理器
                StreamingChatResponseHandler handler = new StreamingChatResponseHandler() {
                    @Override
                    public void onPartialResponse(String partialResponse) {
                        //响应成功
                        sink.tryEmitNext(partialResponse);
                    }

                    @Override
                    public void onCompleteResponse(ChatResponse completeResponse) {
                        sink.tryEmitComplete(); // 关闭流
                        //处理成功后操作
                        if (completeResponse != null) {
                            String answer = completeResponse.aiMessage().text();
                            AiChatMessage aiChatMessage = processingChat(chatMessageSendReqVO, aiModelAuth, aiModel, aiChatConversation, assistantMessage, answer);
                            aiChatMessage.setModelUrl(aiModel.getModelUrl());
                            aiChatMessage.setCreateTime(new Date());
                            aiChatMessage.setUpdateTime(new Date());
                            aiChatMessageMapper.updateById(aiChatMessage);
                        } else {
                            // 处理响应为空的情况，例如记录日志、进行重试或者返回默认值
                            log.error("链接被中断，模型无响应");
                        }
                        RedisUtils.deleteObject(lockKey);
                    }

                    @Override
                    public void onError(Throwable error) {
                        // 处理错误，完成CompletableFuture并抛出异常
                        sink.tryEmitError(error); // 传递错误信息
                        if (LoginHelper.getUserId() != null) {
                            RedisUtils.deleteObject(lockKey);
                        }
                    }
                };
                //执行会话
                chatModel.chat(chatMessage, handler);
                //封装返回结果
                ChatMessageSendRespVO chatMessageSendRespVO = new ChatMessageSendRespVO();
                ChatMessageSendRespVO.Message send = new ChatMessageSendRespVO.Message();
                ChatMessageSendRespVO.Message receive = new ChatMessageSendRespVO.Message();
                send.setId(userMessage.getId());
                send.setType(userMessage.getType());
                send.setContent(userMessage.getContent());
                send.setCreateTime(userMessage.getCreateTime());
                send.setFileInfo(userMessage.getFileInfo());
                chatMessageSendRespVO.setSend(send);
                receive.setId(assistantMessage.getId());
                receive.setType(assistantMessage.getType());
                receive.setCreateTime(new Date());
                chatMessageSendRespVO.setReceive(receive);
                return sink.asFlux().map(aiAnswer -> {
                    String word = StrUtil.nullToDefault(aiAnswer, StrUtil.EMPTY);
                    receive.setContent(word);
                    return R.ok(chatMessageSendRespVO);
                });
            } else {
                return Flux.just(R.fail("服务器繁忙,请稍后重试"));
            }
        } catch (RuntimeException e){
            log.error("流式会话异常：{}", e.getMessage());
            return Flux.just(R.fail(e.getMessage()));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("流式会话异常：{}", e.getMessage());
            return Flux.just(R.fail("服务器繁忙,请稍后重试 " + e.getCause().getMessage()));
        }
    }


    //提取重复方法
    private AiChatMessage processingChat(ChatMessageSendReqVO chatMessageSendReqVO, AiModelAuth aiModelAuth, AiModel aiModel, AiChatConversation aiChatConversation, AiChatMessageVo assistantMessage, String answer) {
        if (ObjectUtil.equals(aiChatConversation.getTitle(), "新对话")) {
            String title = StrUtil.sub(chatMessageSendReqVO.getUserMessage(), 0, 10);
            aiChatConversation.setModelId(aiModel.getId());
            aiChatConversation.setModelKey(aiModelAuth.getModelKey());
            aiChatConversation.setModelName(aiModel.getModelName());
            aiChatConversation.setTitle(title);
            aiChatConversationService.updateBy(aiChatConversation);
        }
        AiChatMessage aiChatMessage = new AiChatMessage();
        aiChatMessage.setId(assistantMessage.getId());
        aiChatMessage.setContent(answer);
        return aiChatMessage;
    }

    /**
     * 多线程执行读取文件
     */
    private Map<String, Object> doThread(List<Long> fileIdList) throws Exception {
        Map<String, Object> files = new ConcurrentHashMap<>();
        CountDownLatch countDownLatch = new CountDownLatch(fileIdList.size());
        for (int i = 0; i < fileIdList.size(); i++) {
            int finalI = i;
            threadPoolTaskExecutor.execute(() -> {
                SysOssVo content = null;
                try {
                    content = dataSaveService.getFileContent(fileIdList.get(finalI));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (content.getFileContent() != null) {
                    String key = (imgType.contains(content.getFileSuffix())) ?
                        "图片" + (finalI + 1) :
                        "文件" + (finalI + 1);
                    files.put(key, content.getFileContent());
                }
                countDownLatch.countDown();
            });
        }
        countDownLatch.await();

        // 提取并分类
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> imgMap = new LinkedHashMap<>();
        Map<String, Object> fileMap = new LinkedHashMap<>();
        List<Map.Entry<String, Object>> imgEntries = new ArrayList<>();
        List<Map.Entry<String, Object>> fileEntries = new ArrayList<>();

        for (Map.Entry<String, Object> entry : files.entrySet()) {
            if (entry.getKey().startsWith("图片")) {
                imgEntries.add(entry);
            } else if (entry.getKey().startsWith("文件")) {
                fileEntries.add(entry);
            }
        }

        // 按编号排序
        Comparator<Map.Entry<String, Object>> comparator = Comparator.comparingInt(e -> Integer.parseInt(e.getKey().replaceAll("[^0-9]", "")));
        imgEntries.sort(comparator);
        fileEntries.sort(comparator);

        // 重新编号并存入 map
        for (int i = 0; i < imgEntries.size(); i++) {
            imgMap.put("图片" + (i + 1), imgEntries.get(i).getValue());
        }
        for (int i = 0; i < fileEntries.size(); i++) {
            fileMap.put("文件" + (i + 1), fileEntries.get(i).getValue());
        }

        if (!imgMap.isEmpty()) {
            result.put("上传图片", imgMap);
        }
        if (!fileMap.isEmpty()) {
            result.put("上传文件", fileMap);
        }
        return result;
    }

    /**
     * 插入响应消息
     */
    private AiChatMessageVo createChatMessage(AiChatConversation conversation, String messageType, ChatMessageSendReqVO chatMessageSendReqVO, String browserId) {
        AiChatMessage message = new AiChatMessage();
        message.setUserId(conversation.getUserId());
        message.setTenantId(conversation.getTenantId());
        message.setCreateDept(conversation.getCreateDept());
        message.setConversationId(conversation.getId());
        message.setModelId(conversation.getModelId());
        message.setModelKey(conversation.getModelKey());
        message.setModelName(conversation.getModelName());
        message.setType(messageType);
        message.setCreateTime(new Date());
        message.setContent(chatMessageSendReqVO.getUserMessage());
        message.setBrowserId(browserId);
        message.setUseContext(1);
        if ("assistant".equals(messageType)) {
            message.setContent("服务器繁忙,请稍后重试");
        }
        //用户如果发送文件，则保存文件json信息
        if ("user".equals(messageType) && CollUtil.isNotEmpty(chatMessageSendReqVO.getReferMessage())) {
            message.setFileInfo(JSONUtil.toJsonStr(chatMessageSendReqVO.getReferMessage()));
        }
        aiChatMessageMapper.insert(message);
        return BeanUtil.toBean(message, AiChatMessageVo.class);
    }

    /**
     * 构建消息
     */
    private List<ChatMessage> buildBasePrompt(List<AiChatMessage> aiChatMessages, boolean isHy) {
        //构建 Prompt Message 列表
        List<ChatMessage> chatMessages = new ArrayList<>();
        if (isHy) {
            loadSystemMessageByYMHY(chatMessages);
        }
        //1.加入历史消息
        Collections.reverse(aiChatMessages);
        Map<String, List<AiChatMessage>> map = aiChatMessages.stream().collect(Collectors.groupingBy(AiChatMessage::getType));
        List<AiChatMessage> userMessages = map.get("user");
        List<AiChatMessage> assistantMessages = map.get("assistant");
        if (CollUtil.isNotEmpty(userMessages)) {
            for (int i = 0; i < userMessages.size(); i++) {
                chatMessages.add(new UserMessage(userMessages.get(i).getContent()));
                chatMessages.add(new AiMessage(assistantMessages.get(i).getContent()));
            }
        }
        //2.合并所有历史消息
        return chatMessages;
    }

    /**
     * 加载沂蒙慧眼的特殊数据
     */
    private void loadSystemMessageByYMHY(List<ChatMessage> chatMessages) {
        //画像统计
        String companyInfo = statisticsService.loadData();
        String companyInfoSum = statisticsService.loadDataSum();
        chatMessages.add(new UserMessage("已为整个临沂市多少家企业进行了画像?"));
        chatMessages.add(new AiMessage(companyInfoSum));
        chatMessages.add(new UserMessage("每个县区下画像的数量是多少?"));
        chatMessages.add(new AiMessage(companyInfo));
        //企业统计
        String companyCount = statisticsService.loadCompanyCount();
        String companyCountSum = statisticsService.loadCompanyCountSum();
        chatMessages.add(new UserMessage("临沂市目前有多少家企业?"));
        chatMessages.add(new AiMessage(companyCountSum));
        chatMessages.add(new UserMessage("临沂市各县区下分别有多少家企业?"));
        chatMessages.add(new AiMessage(companyCount));
        //统计所有评分前十
        String scoreCount = statisticsService.loadCompanyRank();
        chatMessages.add(new UserMessage("临沂市不同等级企业的大概分布情况"));
        chatMessages.add(new AiMessage(scoreCount));
        //合并系统对话
    }

    /**
     * 获取模型负载地址
     */
    private StreamingChatModel getChatModel(AiModel aiModel, String key, String browserId) {
        //算力地址
        String returnUrl = "";
        //前缀 + 模型编码 + 算力密钥 + 设备唯一标识
        String redisKey = CacheConstants.AI_MODEL_KEY + aiModel.getModelCode() + ":" + key + ":" + browserId;
        if (RedisUtils.hasKey(redisKey)) {
            //当前设备分配的通道
            returnUrl = RedisUtils.getCacheObject(redisKey);
        } else {
            //查询库内所有通道
            List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelId(aiModel.getId());
            if (CollUtil.isNotEmpty(aiModelChildList)) {
                //仅一个通道，直接设置
                if (aiModelChildList.size() == 1) {
                    returnUrl = aiModelChildList.get(0).getModelUrl();
                    RedisUtils.setCacheObject(redisKey, returnUrl);
                } else {
                    //没有负载，从redis中获取一个当前模型code负载最少的模型地址
                    List<String> urlCountList = new ArrayList<>();
                    Set<String> allKeys = new HashSet<>(RedisUtils.keys(CacheConstants.AI_MODEL_KEY + aiModel.getModelCode() + ":*"));
                    for (String userKey : allKeys) {
                        String userUrl = RedisUtils.getCacheObject(userKey);
                        if (StringUtils.isNotEmpty(userUrl)) {
                            urlCountList.add(userUrl);
                        }
                    }
                    //分组统计每个通道多少人
                    Map<String, Long> urlGroup = urlCountList.stream().collect(Collectors.groupingBy(e -> e, Collectors.counting()));
                    //将数据库中最新的添加到此分组内
                    aiModelChildList.forEach(child -> {
                        if (!urlGroup.containsKey(child.getModelUrl())) {
                            urlGroup.put(child.getModelUrl(), 0L);
                        }
                    });
                    //找到最小值
                    Optional<Map.Entry<String, Long>> minEntry = urlGroup.entrySet().stream().min(Map.Entry.comparingByValue());
                    if (minEntry.isPresent()) {
                        returnUrl = minEntry.get().getKey();
                    }
                    //存入redis中
                    RedisUtils.setCacheObject(redisKey, returnUrl);
                }
            } else {
                throw new RuntimeException("当前模型令牌，暂未提供算力通道");
            }
        }
        //当前设备分配的通道
        aiModel.setModelUrl(returnUrl);
        //增加10m停留时间
        RedisUtils.expire(redisKey, Duration.ofMinutes(10));
        return OpenAiStreamingChatModel.builder()
            .apiKey("key")
            .baseUrl(aiModel.getModelUrl())
            .modelName(aiModel.getModelCode())
            .temperature(aiModel.getTemperature())
            .timeout(Duration.ofSeconds(300))
            .build();
    }


    /**
     * 图片识别
     * @param imageEntity
     * @return
     */
//    @Override
//    public String extractImgInfo(ImageEntity imageEntity) {
//        List<ChatMessage> userAllMessage = CollUtil.newArrayList();
//        Assert.notEmpty(imageEntity.getUserImage(), "请上传图片");
//        ImageContent imageContent = ImageContent.from(imageEntity.getUserImage(), "image/png");
//        userAllMessage.add(UserMessage.from(imageContent));
//        userAllMessage.add(new UserMessage(imageEntity.getUserPrompt()));
//        ChatResponse chat = chatLanguageModel.chat(userAllMessage);
//        return chat.aiMessage().text();
//    }


}
