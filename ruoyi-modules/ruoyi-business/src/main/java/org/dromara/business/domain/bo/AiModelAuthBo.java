package org.dromara.business.domain.bo;

import org.dromara.business.domain.AiModelAuth;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 模型授权业务对象 ai_model_auth
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiModelAuth.class, reverseConvertGenerate = false)
public class AiModelAuthBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 使用单位
     */
    private String userCom;

    /**
     * 使用项目
     */
    private String project;

    /**
     * 分配key
     */
    private String modelKey;

    /**
     * 到期时间
     */
    private Date expirationDate;

    /**
     * 用户id
     */
    private Long userId;


    /**
     * 绑定操作（1-绑定。2-解绑）
     */
    private Integer bindType;


}
