package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 审查问题记录对象 bs_review_issues
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bs_review_issues")
public class BsReviewIssues extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审查意见
     */
    private String reviewComments;

    /**
     * 规范条文
     */
    private String specificationClause;

    /**
     * 提出次数
     */
    private Long submissionCount;

    /**
     * 意见类型
     */
    private String commentType;

    /**
     * 所属类型
     */
    private String categorys;

    /**
     * 提出人
     */
    private String proposers;

    /**
     * 项目名称
     */
    private String projectNames;

    /**
     * 设计单位
     */
    private String designUnits;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
