package org.dromara.business.service;

import org.dromara.business.domain.vo.BsReviewStaffVo;
import org.dromara.business.domain.bo.BsReviewStaffBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 审查人员信息Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IBsReviewStaffService {

    /**
     * 查询审查人员信息
     *
     * @param id 主键
     * @return 审查人员信息
     */
    BsReviewStaffVo queryById(Long id);

    /**
     * 分页查询审查人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审查人员信息分页列表
     */
    TableDataInfo<BsReviewStaffVo> queryPageList(BsReviewStaffBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的审查人员信息列表
     *
     * @param bo 查询条件
     * @return 审查人员信息列表
     */
    List<BsReviewStaffVo> queryList(BsReviewStaffBo bo);

    /**
     * 新增审查人员信息
     *
     * @param bo 审查人员信息
     * @return 是否新增成功
     */
    Boolean insertByBo(BsReviewStaffBo bo);

    /**
     * 修改审查人员信息
     *
     * @param bo 审查人员信息
     * @return 是否修改成功
     */
    Boolean updateByBo(BsReviewStaffBo bo);

    /**
     * 校验并批量删除审查人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据人员姓名查询类目标识
     *
     * @param personName 人员姓名
     * @return 类目标识
     */
    String getCategoryKeyByPersonName(String personName);
}
