package org.dromara.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.domain.vo.KbDatabaseConfigVo;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.business.service.IKbDatabaseConfigService;
import org.dromara.business.service.IKnowledgeBaseV2Service;
import org.dromara.business.utils.SemanticSqlUtils;
import org.dromara.common.core.constant.KBDataBaseTypeConstants;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;


/**
 * 知识库服务 数据库直连模式实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeBaseV2ServiceImpl implements IKnowledgeBaseV2Service {

    private final IKbDatabaseConfigService kbDatabaseConfigService;
    private final DataSaveServiceImpl dataSaveService;
    private final KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 数据库连接测试
     * @param bo
     */
    @Override
    public void testConnection(KbDatabaseConfigBo bo) {
        switch (bo.getDatabaseType()){
            case KBDataBaseTypeConstants.MYSQL -> testConnectionMySql(bo);
            default -> {
                log.error("该数据库类型暂未开放" + bo.getDatabaseType());
                throw new ServiceException("该数据库类型暂未开放");
            }
        }
    }


    /**
     * 测试mysql连接
     *
     * @param bo
     */
    private void testConnectionMySql(KbDatabaseConfigBo bo) {
        String url = StrUtil.format(KBDataBaseTypeConstants.Mysql_url, bo.getDatabaseUrl(), bo.getDatabasePort(), bo.getDatabaseName());
        Properties properties = new Properties();
        properties.setProperty("user", bo.getUsername());
        properties.setProperty("password", bo.getPassword());

        try (Connection conn = DriverManager.getConnection(url, properties)){
            if (conn == null) {
                throw new ServiceException("连接失败");
            }
        } catch (SQLException e) {
            throw new ServiceException("连接失败" + e.getMessage());
        }
    }


    /**
     * 保存数据库连接
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConnection(KbDatabaseConfigBo bo) {
        if(null == bo.getKnowledgeId()){
            throw new ServiceException("知识库id不能为空");
        }
        //查询知识库信息，更新知识库信息
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(bo.getKnowledgeId());
        if(null == knowledgeBase){
            throw new ServiceException("知识库不存在");
        }
        if (!ObjectUtil.equals(knowledgeBase.getDataType(), "0")){
            throw new ServiceException("非进阶知识库无法使用该功能");
        }
        if (knowledgeBase.getLearningStyle()==1){
            throw new ServiceException("数据库学习方式已选定，无法更改");
        }
        // 先检查数据库连接信息
        testConnection(bo);
        // 保存数据库连接信息
        if(bo.getDatabaseConfigId()==null){
            kbDatabaseConfigService.insertByBo(bo);
        }else {
            kbDatabaseConfigService.updateByBo(bo);
        }
        knowledgeBaseMapper.update(null,new LambdaUpdateWrapper<KnowledgeBase>().set(KnowledgeBase::getLearningStyle,2)
                .eq(KnowledgeBase::getKnowledgeId,bo.getKnowledgeId()));
        // 异步从数据库中获取数据库表结构信息，放入缓存中
        CompletableFuture.runAsync(() -> dataSaveService.cacheDatabase(bo));
    }


    /**
     * 刷新数据库连接
     * @param bo
     */
    @Override
    @CacheEvict(cacheNames = "global:ai_database_info:#10m", key = "#bo.databaseConfigId")
    public void refreshConnection(KbDatabaseConfigBo bo) {
    }


}
