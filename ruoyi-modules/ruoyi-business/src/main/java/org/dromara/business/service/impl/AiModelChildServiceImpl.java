package org.dromara.business.service.impl;

import org.dromara.business.domain.AiModel;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.AiModelChildBo;
import org.dromara.business.domain.vo.AiModelChildVo;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.mapper.AiModelChildMapper;
import org.dromara.business.service.IAiModelChildService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 模型算力地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
public class AiModelChildServiceImpl implements IAiModelChildService {

    private final AiModelChildMapper baseMapper;
    private final AiModelMapper aiModelMapper;

    /**
     * 查询模型算力地址
     *
     * @param id 主键
     * @return 模型算力地址
     */
    @Override
    public AiModelChildVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询模型算力地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模型算力地址分页列表
     */
    @Override
    public TableDataInfo<AiModelChildVo> queryPageList(AiModelChildBo bo, PageQuery pageQuery) {
        Page<AiModelChildVo> result = baseMapper.queryPageList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的模型算力地址列表
     *
     * @param bo 查询条件
     * @return 模型算力地址列表
     */
    @Override
    public List<AiModelChildVo> queryList(AiModelChildBo bo) {
        LambdaQueryWrapper<AiModelChild> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiModelChild> buildQueryWrapper(AiModelChildBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiModelChild> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getModelId() != null, AiModelChild::getModelId, bo.getModelId());
        lqw.eq(StringUtils.isNotBlank(bo.getModelUrl()), AiModelChild::getModelUrl, bo.getModelUrl());
        return lqw;
    }

    /**
     * 新增模型算力地址
     *
     * @param bo 模型算力地址
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiModelChildBo bo) {
        //获取当前登录人
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("未获取到登录人信息");
        }
        AiModelChild add = MapstructUtils.convert(bo, AiModelChild.class);
        if(null == add){
            throw new ServiceException("新增内容不能为空");
        }
        add.setCreateDept(loginUser.getDeptId());
        add.setTenantId(loginUser.getTenantId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改模型算力地址
     *
     * @param bo 模型算力地址
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiModelChildBo bo) {
        AiModelChild update = MapstructUtils.convert(bo, AiModelChild.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiModelChild entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除模型算力地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<AiModelChild> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 通过模型查询地址列表
     * @param id
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstants.AI_MODEL_CODE, key = "#id" ,condition = "#id != null")
    public List<AiModelChild> selectListByModelId(Long id) {
        return baseMapper.selectListByModelId(id);
    }


    @Override
    @Cacheable(cacheNames = CacheConstants.AI_MODEL_CODE, key = "#modelType")
    public List<AiModelChild> selectListByModelIds(List<Long> ids,String modelType) {
        LambdaQueryWrapper<AiModelChild> lqw = new LambdaQueryWrapper<>();
        lqw.in(AiModelChild::getModelId, ids);
        lqw.eq(AiModelChild::getDelFlag, 0);
        return baseMapper.selectList(lqw);
    }

    /**
     * 保存模型子地址
     * @param aiModelChildBoList
     */
    @Override
    @Transactional
    public void saveChildUrl(List<AiModelChildBo> aiModelChildBoList,Long modelId) {
        //删除旧的地址
        baseMapper.deleteByModelId(modelId);
        //实体装换
        List<AiModelChild> aiModelChildList = new ArrayList<>();
        for (AiModelChildBo aiModelChildBo : aiModelChildBoList) {
            AiModelChild aiModelChild = new AiModelChild();
            aiModelChild.setModelId(modelId);
            aiModelChild.setModelUrl(aiModelChildBo.getModelUrl());
            aiModelChildList.add(aiModelChild);
        }
        baseMapper.insert(aiModelChildList);
    }
}
