package org.dromara.business.controller;

import java.util.List;
import java.util.Map;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.business.domain.bo.AiModelHistoryMsgBo;
import org.dromara.business.domain.vo.AiModelHistoryMsgVo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.AiModelVo;
import org.dromara.business.domain.bo.AiModelBo;
import org.dromara.business.service.IAiModelService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 大模型配置
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/model")
public class AiModelController extends BaseController {

    private final IAiModelService aiModelService;

    /**
     * 查询大模型配置列表
     */
    @SaIgnore
    @SaCheckPermission("business:model:list")
    @GetMapping("/list")
    public TableDataInfo<AiModelVo> list(AiModelBo bo, PageQuery pageQuery) {
        return aiModelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出大模型配置列表
     */
    @SaCheckPermission("business:model:export")
    @Log(title = "大模型配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiModelBo bo, HttpServletResponse response) {
        List<AiModelVo> list = aiModelService.queryList(bo);
        ExcelUtil.exportExcel(list, "大模型配置", AiModelVo.class, response);
    }

    /**
     * 获取大模型配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:model:query")
    @GetMapping("/{id}")
    public R<AiModelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(aiModelService.queryById(id));
    }

    /**
     * 新增大模型配置
     */
    @SaCheckPermission("business:model:add")
    @Log(title = "大模型配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiModelBo bo) {
        return toAjax(aiModelService.insertByBo(bo));
    }

    /**
     * 修改大模型配置
     */
    @SaCheckPermission("business:model:edit")
    @Log(title = "大模型配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiModelBo bo) {
        return toAjax(aiModelService.updateByBo(bo));
    }


    @SaIgnore
    @Log(title = "修改大模型配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/changeModel")
    public R<Void> changeModel(@RequestBody AiModelBo bo) {
        try {
            aiModelService.changeModel(bo);
            return R.ok("操作成功");
        }catch (Exception e){
            return R.fail("操作失败"+e.getMessage());
        }
    }

    /**
     * 删除大模型配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:model:remove")
    @Log(title = "大模型配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(aiModelService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取模型信息
     * @return
     */
    @GetMapping("/getModelInfo")
    public R<Map<String,Object>> getModelInfo()
    {
        try {
            return R.ok(aiModelService.getModelInfo());
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询模型信息资源图表信息
     * @return
     */
    @GetMapping("/getTokensCount")
    public R<Map<String,Object>> getTokensCount()
    {
        try {
            return R.ok(aiModelService.getTokensCount());
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 重置缓存（限Ai模型及会话相关缓存）
     * @return
     */
    @RepeatSubmit()
    @PostMapping("/resetCache")
    public R<Void> resetCache() {
        aiModelService.resetChannel();
        return R.ok("重置成功");
    }


    /**
     * 获取模型历史信息
     */
    @GetMapping("/getModelHistoryInfo")
    public R<Map<String,Object>> getModelHistoryInfo(AiModelHistoryMsgBo aiModelHistoryMsgBo)
    {
        try {
            return R.ok(aiModelService.getModelHistoryInfo(aiModelHistoryMsgBo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


}
