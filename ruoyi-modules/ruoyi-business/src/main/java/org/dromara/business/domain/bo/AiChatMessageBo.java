package org.dromara.business.domain.bo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import javax.validation.constraints.*;

import java.util.Date;


/**
 * AI 聊天消息业务对象 ai_chat_message
 *
 * <AUTHOR>
 * @date 2025-01-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AiChatMessageBo extends BaseEntity {

    /**
     * 消息编号
     */
    @NotNull(message = "消息编号不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 对话编号
     */
    @NotNull(message = "对话编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long conversationId;

    /**
     * 回复编号
     */
    @NotNull(message = "回复编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long replyId;

    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 段落编号数组
     */
    @NotBlank(message = "段落编号数组不能为空", groups = { AddGroup.class, EditGroup.class })
    private String segmentIds;

    /**
     * 消息类型
     */
    @NotBlank(message = "消息类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 是否携带上下文
     */
    @NotNull(message = "是否携带上下文不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer useContext;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 上传附件信息(Json)
     */
    private String fileInfo;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 引用信息（json）
     */
    private String quoteInfo;

}
