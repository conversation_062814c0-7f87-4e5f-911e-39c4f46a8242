package org.dromara.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.AiChatConversation;
import org.dromara.business.domain.AiChatMessage;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.bo.AiChatMessageBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.mapper.AiChatConversationMapper;
import org.dromara.business.mapper.AiChatMessageMapper;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.business.service.IAiChatMessageService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;

/**
 * AI 聊天消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RequiredArgsConstructor
@Service
public class AiChatMessageServiceImpl implements IAiChatMessageService {

    private static final Logger log = LoggerFactory.getLogger(AiChatMessageServiceImpl.class);
    private final AiChatMessageMapper baseMapper;
    private final AiChatConversationMapper conversationMapper;
    private final AiModelMapper aiModelMapper;

    /**
     * 查询AI 聊天消息
     */
    @Override
    public AiChatMessageVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询AI 聊天消息列表
     */
    @Override
    public TableDataInfo<AiChatMessage> queryPageList(AiChatMessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiChatMessage> lqw = buildQueryWrapper(bo);
        List<AiChatMessage> result = baseMapper.selectList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询AI 聊天消息列表
     */
    @Override
    public List<AiChatMessageVo> queryList(AiChatMessageBo bo) {
        LambdaQueryWrapper<AiChatMessage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiChatMessage> buildQueryWrapper(AiChatMessageBo bo) {
        LambdaQueryWrapper<AiChatMessage> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getConversationId() != null, AiChatMessage::getConversationId, bo.getConversationId());
        lqw.eq(bo.getReplyId() != null, AiChatMessage::getReplyId, bo.getReplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getSegmentIds()), AiChatMessage::getSegmentIds, bo.getSegmentIds());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AiChatMessage::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AiChatMessage::getContent, bo.getContent());
        lqw.eq(bo.getUseContext() != null, AiChatMessage::getUseContext, bo.getUseContext());
        return lqw;
    }

    /**
     * 新增AI 聊天消息
     */
    @Override
    public Boolean insertByBo(AiChatMessageBo bo) {
        AiChatMessage add = BeanUtil.toBean(bo, AiChatMessage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改AI 聊天消息
     */
    @Override
    public Boolean updateByBo(AiChatMessageBo bo) {
        AiChatMessage update = BeanUtil.toBean(bo, AiChatMessage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiChatMessage entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除AI 聊天消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获得指定对话的消息列表
     * @param conversationId
     * @return
     */
    @Override
    public List<AiChatMessageVo> getChatMessageListByConversationId(Long conversationId) {
        List<AiChatMessageVo> result = new ArrayList<>();
        //前置条件，校验模型
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String key = request.getHeader("Model-Key");
        if(StringUtils.isEmpty(key)){
            throw new ServiceException("模型令牌不能为空");
        }
        //根据会话id查询会话详情
        AiChatConversation aiChatConversation = conversationMapper.selectById(conversationId);
        if(null == aiChatConversation){
            throw new ServiceException("会话不存在");
        }
        //查询模型信息
        AiModel aiModel = aiModelMapper.selectById(aiChatConversation.getModelId());
        String modelLogo = null;
        if(null != aiModel){
            modelLogo = aiModel.getModelLogoUrl();
        }
        //根据会话查询消息信息
        QueryWrapper<AiChatMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id,conversation_id,reply_id,segment_ids,type,content,use_context,create_time,create_by,update_time,update_by,model_name,model_key,file_info,browser_id");
        queryWrapper.eq("conversation_id", conversationId);
        queryWrapper.eq("del_flag", 0);
        queryWrapper.orderByAsc("id");
        //2025年6月9日 沟通后将此处注释，查全部
//        queryWrapper.last("limit 20");
        List<AiChatMessage> aiChatMessages = baseMapper.selectList(queryWrapper);
        if(!aiChatMessages.isEmpty()){
            result = BeanUtil.copyToList(aiChatMessages, AiChatMessageVo.class);
            for (AiChatMessageVo aiChatMessageVo : result) {
                aiChatMessageVo.setModelLogoUrl(modelLogo);
            }
        }
        return result;
    }

    /**
     * 删除聊天对话
     * @param id
     */
    @Override
    @Transactional
    public void deleteChatMessage(Long id) {
        //前置条件，校验模型
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String key = request.getHeader("Model-Key");
        if(StringUtils.isEmpty(key)){
            throw new ServiceException("模型令牌不能为空");
        }
        AiChatMessage aiChatMessage = baseMapper.selectById(id);
        if(null == aiChatMessage || !ObjectUtil.equals(key, aiChatMessage.getModelKey())){
            throw new ServiceException("消息不存在");
        }
        //删除需要将上下文一组同步删除，（删除创建时间相同的）
        //查询和当前消息创建时间相同的消息集合
        QueryWrapper<AiChatMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_time", aiChatMessage.getCreateTime());
        queryWrapper.eq("del_flag", 0);
        List<AiChatMessage> messages = baseMapper.selectList(queryWrapper);
        if(messages.size() != 2){
            aiChatMessage.setDelFlag(1);
            aiChatMessage.setUpdateTime(new Date());
            baseMapper.updateById(aiChatMessage);
        }else {
            for (AiChatMessage message : messages) {
                message.setDelFlag(1);
                message.setUpdateTime(new Date());
                baseMapper.updateById(message);
            }
        }
    }

    /**
     * 删除指定对话的消息
     * @param conversationId
     */
    @Override
    public void deleteChatMessageByConversationId(Long conversationId) {
        //前置条件，校验模型
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String key = request.getHeader("Model-Key");
        if(StringUtils.isEmpty(key)){
            throw new ServiceException("模型令牌不能为空");
        }
        // 1. 校验消息存在
        List<AiChatMessage> messages = baseMapper.selectListByConversationId(conversationId);
        if (CollUtil.isEmpty(messages)) {
            throw new ServiceException("对话消息不存在");
        }
        for (AiChatMessage message : messages) {
            message.setDelFlag(1);
            message.setUpdateTime(new Date());
        }
        // 2. 执行删除
        baseMapper.updateBatchById(messages);
    }

    /**
     * 编辑AI聊天消息
     * @param id 消息ID
     * @param content 新的消息内容
     * @return 如果编辑成功返回true，否则返回false
     */
    @Override
    public Boolean editChatMessage(Long id, String content) {
        // 校验输入参数
        if (id == null || content == null) {
            throw new IllegalArgumentException("消息ID和内容不能为空");
        }
        try {
            AiChatMessage aiChatMessage = baseMapper.selectById(id);
            if (aiChatMessage != null) {
                String originalContent = aiChatMessage.getContent();
                // 处理内容格式：<think>思考过程</think>思考结果
                // 只更新思考结果部分，保留原始思考过程
                if (originalContent != null && originalContent.contains("<think>")) {
                    // 查找</think>标记的位置
                    int endThinkIndex = originalContent.indexOf("</think>");
                    if (endThinkIndex > 0) {
                        // 提取思考过程部分(包括<think>和</think>标记)
                        String thinkProcess = originalContent.substring(0, endThinkIndex + 8); // +8是为了包含</think>标记本身
                        // 组合新内容：保留原始思考过程 + 新的思考结果
                        String newContent = thinkProcess + content;
                        aiChatMessage.setContent(newContent);
                    } else {
                        // 没有找到格式正确的标记，直接更新全部内容
                        aiChatMessage.setContent(content);
                    }
                } else {
                    // 原内容不符合预期格式，直接更新
                    aiChatMessage.setContent(content);
                }
                int i = baseMapper.updateById(aiChatMessage);
                return i > 0;
            }
            return false;
        } catch (Exception e) {
            // 记录日志，这里简单打印堆栈信息，实际项目中可以使用日志框架
            log.error("编辑AI聊天消息失败：", e);
            return false;
        }
    }


    /**
     * 清空上下文缓存
     * @param conversationId 会话id
     */
    @Override
    @Transactional
    public Boolean clearMessages(Long conversationId) {
        //查询该会话下所有携带上下文的消息，修改为不携带
        LambdaUpdateWrapper<AiChatMessage> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(AiChatMessage::getConversationId, conversationId);
        queryWrapper.eq(AiChatMessage::getUseContext, 1);

        queryWrapper.set(AiChatMessage::getUseContext, 0);
        return baseMapper.update(null, queryWrapper) > 0;
    }

}
