package org.dromara.business.service.impl;

import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.BsReviewStaffBo;
import org.dromara.business.domain.vo.BsReviewStaffVo;
import org.dromara.business.domain.BsReviewStaff;
import org.dromara.business.mapper.BsReviewStaffMapper;
import org.dromara.business.service.IBsReviewStaffService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 审查人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class BsReviewStaffServiceImpl implements IBsReviewStaffService {

    private final BsReviewStaffMapper baseMapper;

    /**
     * 查询审查人员信息
     *
     * @param id 主键
     * @return 审查人员信息
     */
    @Override
    public BsReviewStaffVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询审查人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审查人员信息分页列表
     */
    @Override
    public TableDataInfo<BsReviewStaffVo> queryPageList(BsReviewStaffBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsReviewStaff> lqw = buildQueryWrapper(bo);
        Page<BsReviewStaffVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的审查人员信息列表
     *
     * @param bo 查询条件
     * @return 审查人员信息列表
     */
    @Override
    public List<BsReviewStaffVo> queryList(BsReviewStaffBo bo) {
        LambdaQueryWrapper<BsReviewStaff> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsReviewStaff> buildQueryWrapper(BsReviewStaffBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsReviewStaff> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getPersonName()), BsReviewStaff::getPersonName, bo.getPersonName());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryKey()), BsReviewStaff::getCategoryKey, bo.getCategoryKey());
        lqw.eq(StringUtils.isNotBlank(bo.getOrganizationKey()), BsReviewStaff::getOrganizationKey, bo.getOrganizationKey());
        return lqw;
    }

    /**
     * 新增审查人员信息
     *
     * @param bo 审查人员信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsReviewStaffBo bo) {
        BsReviewStaff add = MapstructUtils.convert(bo, BsReviewStaff.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改审查人员信息
     *
     * @param bo 审查人员信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsReviewStaffBo bo) {
        BsReviewStaff update = MapstructUtils.convert(bo, BsReviewStaff.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsReviewStaff entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除审查人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsReviewStaff> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据人员姓名查询类目标识
     *
     * @param personName 人员姓名
     * @return 类目标识
     */
    @Override
    public String getCategoryKeyByPersonName(String personName) {
        return baseMapper.selectCategoryKeyByPersonName(personName);
    }
}
