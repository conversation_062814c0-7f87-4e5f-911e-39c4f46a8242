package org.dromara.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.AiChatMessage;
import org.dromara.business.domain.bo.AiModelHistoryMsgBo;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.domain.vo.AiModelHistoryMsgVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import java.util.List;
import java.util.Map;

/**
 * AI 聊天消息Mapper接口
 */
public interface AiChatMessageMapper extends BaseMapperPlus<AiChatMessage, AiChatMessageVo> {

    default List<AiChatMessage> selectListByConversationId(Long conversationId) {
        return selectList(
            new LambdaQueryWrapper<AiChatMessage>()
                .eq(AiChatMessage::getConversationId, conversationId)
                .eq(AiChatMessage::getUseContext, 1)
                .orderByDesc(AiChatMessage::getCreateTime)
                .last("limit 20")
        );
    };

    List<Map<String, Object>> getSevenTokensByModel();

    void setGroupConcatMaxLen();


    List<String> getTwelveTokens(@Param("modelId") Long modelId);

    Long getConversationBs(@Param("modelUrl") String modelUrl,@Param("modelId") Long modelId);

    //查询今日访问总量（按人分组查询brw_id数量）
    Long getAllVisit();

//    查询月访问总量（按人分组查询brw_id数量，每天相加）
    Long getAllMonth();

    //查询今日会话次数（查询消息表中type为“user” 的数量）
    Long getTodayMsg();

    //查询当月会话次数（查询消息表中type为“user” 的数量）
    Long getMonthMsg();

    Map<String, Long> getTodayMsgPeak();

    Map<String, Long> getMonthMsgPeak();

    Map<String, Long> getTodayUserPeak();

    Map<String, Long> getMonthUserPeak();

    @InterceptorIgnore(tenantLine = "true")
    List<AiModelHistoryMsgVo> getDayCountInfo(@Param("aiModelHistoryMsgBo") AiModelHistoryMsgBo aiModelHistoryMsgBo);

    @InterceptorIgnore(tenantLine = "true")
    List<AiModelHistoryMsgVo> getMonthCountInfo(@Param("aiModelHistoryMsgBo") AiModelHistoryMsgBo aiModelHistoryMsgBo);
}
