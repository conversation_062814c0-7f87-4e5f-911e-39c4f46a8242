package org.dromara.business.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.BsReviewIssues;
import org.dromara.business.domain.vo.BsReviewIssuesVo;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 审查问题记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface BsReviewIssuesMapper extends BaseMapperPlus<BsReviewIssues, BsReviewIssuesVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsReviewIssues> selectList(IPage<BsReviewIssues> page, @Param(Constants.WRAPPER) Wrapper<BsReviewIssues> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsReviewIssues> selectList(@Param(Constants.WRAPPER) Wrapper<BsReviewIssues> queryWrapper);

    @Override
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    }, joinStr = "AND")
    List<BsReviewIssues> selectByIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    int updateById(@Param(Constants.ENTITY) BsReviewIssues entity);

    /**
     * 查询所有意见类型可选项
     *
     * @return 意见类型列表
     */
    List<String> selectCommentTypeOptions();

    /**
     * 根据审查意见列表查询已存在的记录ID和审查意见的映射
     *
     * @param reviewCommentsList 审查意见列表
     * @return 映射关系列表
     */
    List<BsReviewIssues> selectByReviewComments(@Param("reviewCommentsList") List<String> reviewCommentsList);

}
