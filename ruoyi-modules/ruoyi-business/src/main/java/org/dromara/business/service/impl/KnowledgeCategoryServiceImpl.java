package org.dromara.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.business.domain.KnowledgeCategory;
import org.dromara.business.domain.KnowledgeCategoryRelation;
import org.dromara.business.dto.KnowledgeCategoryDTO;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.business.mapper.KnowledgeCategoryMapper;
import org.dromara.business.mapper.KnowledgeCategoryRelationMapper;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.service.IKnowledgeCategoryService;
import org.dromara.business.vo.KnowledgeCategoryVO;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.service.ISysDeptService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库分类Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class KnowledgeCategoryServiceImpl extends ServiceImpl<KnowledgeCategoryMapper, KnowledgeCategory>
    implements IKnowledgeCategoryService {

    private final KnowledgeCategoryMapper knowledgeCategoryMapper;
    private final KnowledgeCategoryRelationMapper relationMapper;
    private final ISysDeptService deptService;
    @Resource
    private SysDeptMapper sysDeptMapper;
    /**
     * 分页查询知识库分类
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    @Override
    public TableDataInfo<KnowledgeCategoryVO> pageKnowledgeCategories(KnowledgeCategoryDTO dto) {
        // 创建查询条件
        QueryWrapper<KnowledgeCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .like(StringUtils.isNotBlank(dto.getName()), KnowledgeCategory::getName, dto.getName())
            .eq(StringUtils.isNotBlank(dto.getStatus()), KnowledgeCategory::getStatus, dto.getStatus())
            .eq(dto.getDeptId() != null, KnowledgeCategory::getDeptId, dto.getDeptId())
            .in(dto.getDeptIds() != null && !dto.getDeptIds().isEmpty(), KnowledgeCategory::getDeptId, dto.getDeptIds())
            .orderByAsc(KnowledgeCategory::getSort);

        // 创建分页对象
        Page<KnowledgeCategory> page = new Page<>(dto.getPageNum(), dto.getPageSize());

        // 执行分页查询
        page = page(page, queryWrapper);

        // 转换结果
        List<KnowledgeCategoryVO> voList = new ArrayList<>();
        for (KnowledgeCategory entity : page.getRecords()) {
            KnowledgeCategoryVO vo = new KnowledgeCategoryVO();
            BeanUtils.copyProperties(entity, vo);
            voList.add(vo);
        }

        // 构建返回结果
        TableDataInfo<KnowledgeCategoryVO> result = new TableDataInfo<>();
        result.setRows(voList);
        result.setTotal(page.getTotal());
        return result;
    }

    /**
     * 查询所有知识库分类列表
     *
     * @return 知识库分类列表
     */
    @Override
    public List<KnowledgeCategoryVO> listAllCategories() {
        QueryWrapper<KnowledgeCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KnowledgeCategory::getStatus, "0")
            .orderByAsc(KnowledgeCategory::getSort);

        List<KnowledgeCategory> list = list(queryWrapper);
        return list.stream()
            .map(entity -> {
                KnowledgeCategoryVO vo = new KnowledgeCategoryVO();
                BeanUtils.copyProperties(entity, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据ID获取知识库分类详情
     *
     * @param id 知识库分类ID
     * @return 知识库分类信息
     */
    @Override
    public KnowledgeCategoryVO getKnowledgeCategoryById(Long id) {
        KnowledgeCategory category = getById(id);
        if (category == null) {
            return null;
        }
        KnowledgeCategoryVO vo = new KnowledgeCategoryVO();
        BeanUtils.copyProperties(category, vo);
        return vo;
    }

    /**
     * 新增知识库分类
     *
     * @param dto 知识库分类信息
     * @return 结果
     */
    @Override
    public boolean addKnowledgeCategory(KnowledgeCategoryDTO dto) {
        KnowledgeCategory category = new KnowledgeCategory();
        BeanUtils.copyProperties(dto, category);
        if (category.getSort() == null) {
            category.setSort(0);
        }
        if (StringUtils.isBlank(category.getStatus())) {
            category.setStatus("0");
        }
        return save(category);
    }

    /**
     * 修改知识库分类
     *
     * @param dto 知识库分类信息
     * @return 结果
     */
    @Override
    public boolean updateKnowledgeCategory(KnowledgeCategoryDTO dto) {
        KnowledgeCategory category = new KnowledgeCategory();
        BeanUtils.copyProperties(dto, category);
        return updateById(category);
    }

    /**
     * 删除知识库分类
     *
     * @param id 知识库分类ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteKnowledgeCategory(Long id) {
        // 删除分类与知识库的关联
        QueryWrapper<KnowledgeCategoryRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(KnowledgeCategoryRelation::getCategoryId, id);
        relationMapper.delete(queryWrapper);

        // 删除分类
        return removeById(id);
    }

    /**
     * 根据多个部门ID查询分类列表
     *
     * @param deptIds 部门ID列表
     * @return 知识库分类列表
     */
    @Override
    public List<KnowledgeCategoryVO> listCategoriesByDeptIds(List<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return new ArrayList<>();
        }
        // 创建可修改的副本，避免修改原始集合
        List<Long> mutableDeptIds = new ArrayList<>(deptIds);
        
        // 判断部门是不是单位,单位不需要展示分类
        List<SysDeptVo> sysDeptVos = sysDeptMapper.selectVoByIds(mutableDeptIds);
        sysDeptVos.forEach(sysDeptVo -> {
            if (StringUtils.isNotEmpty(sysDeptVo.getDeptCategory())){
                mutableDeptIds.remove(sysDeptVo.getDeptId());
            }
        });
        if (mutableDeptIds.isEmpty()) {
            return new ArrayList<>();
        }
        // 查询分类
        QueryWrapper<KnowledgeCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(KnowledgeCategory::getDeptId, mutableDeptIds)
            .eq(KnowledgeCategory::getStatus, "0")
            .orderByAsc(KnowledgeCategory::getSort);

        List<KnowledgeCategory> list = list(queryWrapper);
        return list.stream()
            .map(entity -> {
                KnowledgeCategoryVO vo = new KnowledgeCategoryVO();
                BeanUtils.copyProperties(entity, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }

    @Override
    public Long topDeptId() {
        LoginUser loginUser = LoginHelper.getLoginUser();

        if (ObjectUtils.isEmpty(loginUser) || loginUser.getDeptId() == null){
            return null;
        }
        // 父部门
        Long deptId = loginUser.getDeptId();
        SysDeptVo sysDeptVo = deptService.selectDeptById(deptId);

        // 添加空值检查，防止空指针异常
        List<Long> ancestorsList = new ArrayList<>();
        if (sysDeptVo != null && org.dromara.common.core.utils.StringUtils.isNotEmpty(sysDeptVo.getAncestors())) {
            String ancestors = sysDeptVo.getAncestors();
            // ancestors存放所有的父部门id,转为List<Lone>
            ancestorsList = Arrays.stream(ancestors.split(",")).map(Long::parseLong).toList();
        }
        // 倒序遍历ancestorsList
        for (int i = ancestorsList.size() - 1; i >= 0; i--) {
            SysDeptVo sysDeptVo2 = sysDeptMapper.selectVoById(ancestorsList.get(i));
            if (ObjectUtils.isNotEmpty(sysDeptVo2)){
                if (StringUtils.isNotEmpty(sysDeptVo2.getDeptCategory())){
                    return ancestorsList.get(i);
                }
            }
        }
        return null;
    }
}
