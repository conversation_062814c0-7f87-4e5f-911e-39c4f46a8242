package org.dromara.business.service;

import org.dromara.business.domain.BsReviewIssues;
import org.dromara.business.domain.vo.BsReviewIssuesVo;
import org.dromara.business.domain.bo.BsReviewIssuesBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 审查问题记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IBsReviewIssuesService {

    /**
     * 查询审查问题记录
     *
     * @param id 主键
     * @return 审查问题记录
     */
    BsReviewIssuesVo queryById(Long id);

    /**
     * 分页查询审查问题记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审查问题记录分页列表
     */
    TableDataInfo<BsReviewIssuesVo> queryPageList(BsReviewIssuesBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的审查问题记录列表
     *
     * @param bo 查询条件
     * @return 审查问题记录列表
     */
    List<BsReviewIssuesVo> queryList(BsReviewIssuesBo bo);

    /**
     * 新增审查问题记录
     *
     * @param bo 审查问题记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BsReviewIssuesBo bo);

    /**
     * 修改审查问题记录
     *
     * @param bo 审查问题记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BsReviewIssuesBo bo);

    /**
     * 校验并批量删除审查问题记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 异步导入审查问题。
     * @param file 要导入的 Excel 文件。
     * @param importNo 导入流水号
     */
    void importIssuesAsync(MultipartFile file, String importNo);

    /**
     * 异步导入审查问题（不初始化进度）。
     * @param file 要导入的 Excel 文件。
     * @param importNo 导入流水号
     */
    void importIssuesAsyncWithoutInit(MultipartFile file, String importNo);

    /**
     * 在Controller线程中初始化导入进度。
     * @param importNo 导入流水号
     */
    void initImportProgressInController(String importNo);

    /**
     * 查询所有意见类型可选项
     *
     * @return 意见类型列表
     */
    List<String> getCommentTypeOptions();

    /**
     * 查询导入进度
     *
     * @param importNo 导入流水号
     * @return 进度信息
     */
    Map<String, Object> getImportProgress(String importNo);
    
    /**
     * 从监听器更新导入进度（仅更新百分比和消息，不更新行数）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     */
    void updateImportProgressFromListener(String importNo, int percentage, String message);
    
    /**
     * 从监听器更新导入进度（包含行数信息）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     * @param totalRows 总行数
     * @param processedRows 已处理行数
     */
    void updateImportProgressWithRowsFromListener(String importNo, int percentage, String message, int totalRows, int processedRows);
}
