package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 施工图项目受理清单对象 bs_project_accept
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bs_project_accept")
public class BsProjectAccept extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 县区
     */
    private String district;

    /**
     * 图审机构
     */
    private String drawingReviewAgency;

    /**
     * 政审机构
     */
    private String politicalReviewAgency;

    /**
     * 编号
     */
    private String projectNumber;

    /**
     * 建设单位
     */
    private String constructionCompany;

    /**
     * 设计单位
     */
    private String designCompany;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 建筑类型
     */
    private String buildingType;

    /**
     * 建筑面积（㎡）
     */
    private BigDecimal constructionAreaSqm;

    /**
     * 规划面积（㎡）
     */
    private BigDecimal plannedAreaSqm;

    /**
     * 机构遴选日期
     */
    private Date agencySelectionDate;

    /**
     * 是否自费
     */
    private String isSelfFunded;

    /**
     * 合格书盖章日期
     */
    private Date qualifiedStampDate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
