package org.dromara.business.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 模型算力地址对象 ai_model_child
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_model_child")
public class AiModelChild extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型编码
     */
    private String modelUrl;

    @TableField(exist = false)
    private Long onlineNum;

    /**
     * 模型速度
     */
    private String modelSpeed;

}
