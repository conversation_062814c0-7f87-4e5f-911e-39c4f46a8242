package org.dromara.business.domain.bo;

import org.dromara.business.domain.AiModel;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 大模型配置业务对象 ai_model
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiModel.class, reverseConvertGenerate = false)
public class AiModelBo extends BaseEntity {

    /**
     * 模型id
     */
    @NotNull(message = "模型id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 模型编码
     */
    @NotBlank(message = "模型编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String modelCode;

    /**
     * 模型名称
     */
    @NotBlank(message = "模型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String modelName;

    /**
     * 知识库编号
     */
    private Long knowledgeId;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 单条回复的最大 Token 数量
     */
    private Long maxTokens;

    /**
     * 上下文的最大 Message 数量
     */
    private Long maxContexts;

    /**
     * 开关状态（0-关闭，1-开启）
     */
    private Integer status;

    /**
     * 模型描述
     */
    private String remark;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 子模型信息
     */
    List<AiModelChildBo> modelUrlList;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 模型类型
     */
    private String modelType;


}
