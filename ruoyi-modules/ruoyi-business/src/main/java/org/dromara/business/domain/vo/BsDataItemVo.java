package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.BsDataItem;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.excel.convert.ExcelListConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 数据归纳视图对象 bs_data_item
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BsDataItem.class)
public class BsDataItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private Long id;

    /**
     * 数据名称
     */
    @ExcelProperty(value = "数据名称")
    private String dataName;

    /**
     * 数据字段
     */
    @ExcelProperty(value = "数据字段")
    private String dataField;

    /**
     * 数据状态
     */
    @ExcelProperty(value = "数据状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "data_state")
    private Long dataState;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "data_source")
    private Long dataSource;

    /**
     * 数据分类
     */
    @ExcelProperty(value = "数据分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "data_class")
    private Long dataClass;

    /**
     * 应用项目
     */
    @ExcelProperty(value = "应用项目", converter = ExcelListConvert.class)
    private List<String> dataProject;

    /**
     * 创建人
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME)
    private Long createBy;

}
