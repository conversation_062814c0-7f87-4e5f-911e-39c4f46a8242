package org.dromara.business.service;

import org.dromara.business.domain.KnowledgeFile;
import org.dromara.common.core.domain.model.LoginUser;

/**
 * 数据治理服务接口
 */
public interface IDataGovernanceService {

    /**
     * 向量存储接口
     *
     * @param knowledgeId 知识库ID
     * @param fileId 文件ID
     * @param content 文件内容
     * @return 向量ID
     */
    String vectorStore(Long knowledgeId, Long fileId, String content);

    /**
     * 向量删除接口
     *
     * @return 结果
     */
    Boolean vectorDelete(KnowledgeFile vectorId, LoginUser loginUser);

    /**
     * 向量检索接口
     *
     * @param knowledgeId 知识库ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @param keyword 检索关键词
     * @return 检索结果
     */
    String vectorSearch(Long knowledgeId, Long userId, String userType, String keyword);
}
