package org.dromara.business.controller;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.business.domain.AiChatConversation;
import org.dromara.business.domain.bo.AiChatConversationBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.service.IAiChatConversationService;
import org.dromara.business.service.IAiChatMessageService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

/**
 * AI 聊天对话
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@SaIgnore
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/chatConversation")
public class AiChatConversationController extends BaseController {

    private final IAiChatConversationService iAiChatConversationService;

    private final IAiChatMessageService chatMessageService;

    /**
     * 查询AI 聊天对话列表
     */
    @SaCheckPermission("system:chatConversation:list")
    @GetMapping("/list")
    public TableDataInfo<AiChatConversationVo> list(AiChatConversationBo bo, PageQuery pageQuery) {
        return iAiChatConversationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出AI 聊天对话列表
     */
    @SaCheckPermission("system:chatConversation:export")
    @Log(title = "AI 聊天对话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiChatConversationBo bo, HttpServletResponse response) {
        List<AiChatConversationVo> list = iAiChatConversationService.queryList(bo);
        ExcelUtil.exportExcel(list, "AI 聊天对话", AiChatConversationVo.class, response);
    }

    /**
     * 获取AI 聊天对话详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:chatConversation:query")
    @GetMapping("/{id}")
    public R<AiChatConversationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAiChatConversationService.queryById(id));
    }

    /**
     * 新增AI 聊天对话
     */
    @SaCheckPermission("system:chatConversation:add")
    @Log(title = "AI 聊天对话", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiChatConversationBo bo) {
        return toAjax(iAiChatConversationService.insertByBo(bo));
    }

    /**
     * 修改AI 聊天对话
     */
    @SaCheckPermission("system:chatConversation:edit")
    @Log(title = "AI 聊天对话", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiChatConversationBo bo) {
        return toAjax(iAiChatConversationService.updateByBo(bo));
    }

    /**
     * 删除AI 聊天对话
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:chatConversation:remove")
    @Log(title = "AI 聊天对话", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAiChatConversationService.deleteWithValidByIds(Arrays.asList(ids), true));
    }


    @SaIgnore
    @PostMapping("/create-my")
//    @Log(title = "创建【我的】聊天对话", businessType = BusinessType.INSERT)
    public R<Long> createChatConversationMy(@RequestBody @Valid AiChatConversationBo createReqVO) {
        return R.ok(iAiChatConversationService.createChatConversationMy(createReqVO));
    }

    @SaIgnore
    @PutMapping("/update-my")
//    @Log(title = "更新【我的】聊天对话", businessType = BusinessType.UPDATE)
    public R<Boolean> updateChatConversationMy(@RequestBody @Valid AiChatConversationBo updateReqVO) {
        iAiChatConversationService.updateChatConversationMy(updateReqVO);
        return R.ok();
    }

    @SaIgnore
    @GetMapping("/my-list")
//    @Log(title = "获得【我的】聊天对话列表", businessType = BusinessType.OTHER)
    public R<List<AiChatConversationVo>> getChatConversationMyList(@RequestParam(value = "projectId",defaultValue = "-1") String projectId) {
        List<AiChatConversationVo> list = iAiChatConversationService.getChatConversationListByUserId(projectId);
        return R.ok(list);
    }

    @GetMapping("/get-my")
//    @Log(title = "获得【我的】聊天对话", businessType = BusinessType.OTHER)
    public R<AiChatConversation> getChatConversationMy(@RequestParam("id") Long id) {
        AiChatConversation conversation = iAiChatConversationService.getChatConversation(id);
        return R.ok(conversation);
    }

    @SaIgnore
    @DeleteMapping("/delete-my")
//    @Log(title = "删除聊天对话", businessType = BusinessType.DELETE)
    public R<Boolean> deleteChatConversationMy(@RequestParam("id") Long id) {
        iAiChatConversationService.deleteChatConversationMy(id);
        return R.ok();
    }

    @DeleteMapping("/delete-by-unpinned")
//    @Log(title = "删除未置顶的聊天对话", businessType = BusinessType.DELETE)
    public R<Boolean> deleteChatConversationMyByUnpinned() {
        iAiChatConversationService.deleteChatConversationMyByUnpinned();
        return R.ok();
    }


}
