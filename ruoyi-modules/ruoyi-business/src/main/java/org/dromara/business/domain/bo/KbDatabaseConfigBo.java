package org.dromara.business.domain.bo;

import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 知识库-数据库配置信息业务对象 kb_database_config
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = KbDatabaseConfig.class, reverseConvertGenerate = false)
public class KbDatabaseConfigBo extends BaseEntity {

    /**
     * 数据库配置id
     */
    private Long databaseConfigId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 数据库类型
     */
    @NotBlank(message = "数据库类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String databaseType;

    /**
     * 数据库地址
     */
    @NotBlank(message = "数据库地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String databaseUrl;

    /**
     * 端口
     */
    @NotBlank(message = "数据库端口不能为空", groups = { AddGroup.class, EditGroup.class })
    private String databasePort;

    /**
     * 库名
     */
    @NotBlank(message = "数据库库名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String databaseName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;


    public String getDatabaseName() {
        if (!databaseName.matches("^[a-zA-Z0-9_]+$")) {
            throw new IllegalArgumentException("非法的数据库名！");
        }
        return databaseName;
    }

}
