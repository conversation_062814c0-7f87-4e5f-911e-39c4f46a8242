package org.dromara.business.mapper;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.AiChatConversation;
import org.dromara.business.domain.bo.AiChatConversationBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * AI 聊天对话Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface AiChatConversationMapper extends BaseMapperPlus<AiChatConversation, AiChatConversationVo> {

    List<AiChatConversationVo> getChatConversationListByUserId(@Param("bo") AiChatConversationBo bo);

    Page<AiChatConversationVo> queryPageList(@Param("page") Page<Object> build, @Param("bo") AiChatConversationBo bo);
}
