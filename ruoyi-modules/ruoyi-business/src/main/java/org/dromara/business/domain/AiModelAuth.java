package org.dromara.business.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 模型授权对象 ai_model_auth
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_model_auth")
public class AiModelAuth extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 使用单位
     */
    private String userCom;

    /**
     * 使用项目
     */
    private String project;

    /**
     * 分配key
     */
    private String modelKey;

    /**
     * 到期时间
     */
    private Date expirationDate;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;


    /**
     * 用户id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long userId;


}
