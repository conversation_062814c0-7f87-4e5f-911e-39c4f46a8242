package org.dromara.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识库分类关联 DTO
 */
@Data
@AllArgsConstructor
public class KnowledgeCategoryRelationDTO {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    /**
     * 知识库ID列表
     */
    @NotNull(message = "知识库ID不能为空")
    private List<Long> knowledgeIds;
}
