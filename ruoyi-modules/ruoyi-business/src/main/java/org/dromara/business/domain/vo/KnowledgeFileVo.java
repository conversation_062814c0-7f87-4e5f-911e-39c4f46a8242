package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeFile;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serializable;
import java.util.Date;

/**
 * 知识库文件视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeFile.class)
public class KnowledgeFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @ExcelProperty(value = "文件ID")
    private Long fileId;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * OSS文件ID
     */
    private Long ossId;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String fileType;

    /**
     * 文件描述
     */
    @ExcelProperty(value = "文件描述")
    private String remark;

    /**
     * 文件大小（KB）
     */
    @ExcelProperty(value = "文件大小(KB)")
    private Long fileSize;

    /**
     * 文件状态（0处理中 1已完成 2处理失败）
     */
    @ExcelProperty(value = "文件状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "knowledge_file_status")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
