package org.dromara.business.domain.vo;

import org.dromara.business.domain.KbDatabaseConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 知识库-数据库配置信息视图对象 kb_database_config
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KbDatabaseConfig.class)
public class KbDatabaseConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据库配置id
     */
    @ExcelProperty(value = "数据库配置id")
    private Long databaseConfigId;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * 数据库类型
     */
    @ExcelProperty(value = "数据库类型")
    private String databaseType;

    /**
     * 数据库地址
     */
    @ExcelProperty(value = "数据库地址")
    private String databaseUrl;

    /**
     * 端口
     */
    @ExcelProperty(value = "端口")
    private String databasePort;

    /**
     * 库名
     */
    @ExcelProperty(value = "库名")
    private String databaseName;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String username;

    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    private String password;


}
