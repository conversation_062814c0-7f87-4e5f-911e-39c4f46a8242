package org.dromara.business.support.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.BsReviewIssues;
import org.dromara.business.domain.vo.BsReviewIssuesImportVO;
import org.dromara.business.service.IBsReviewIssuesService;
import org.dromara.business.service.IBsReviewStaffService;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自定义监听器，用于处理 BsReviewIssues 的多行数据。
 */
@Getter
@RequiredArgsConstructor
@Slf4j
public class ReviewIssuesImportListener extends AnalysisEventListener<BsReviewIssuesImportVO> {

    private final IBsReviewStaffService reviewStaffService;
    private final String importNo;
    // 添加Service引用以便更新进度
    private final IBsReviewIssuesService reviewIssuesService;


    // 存储最终导入成功的数据
    private final List<BsReviewIssues> importedIssues = new ArrayList<>();

    // 当前正在处理的审查问题对象
    private BsReviewIssues currentIssue;

    // 用于收集多行数据的临时列表
    private final List<String> currentProposers = new ArrayList<>();
    private final List<String> currentProjectNames = new ArrayList<>();
    private final List<String> currentDesignUnits = new ArrayList<>();

    // 进度跟踪相关变量
    private int totalRowCount = 0;
    private int processedRowCount = 0;
    
    // 进度更新频率控制：每处理多少行更新一次进度
    private static final int PROGRESS_UPDATE_INTERVAL = 10;
    
    // 上次更新进度的时间，避免频繁更新
    private long lastProgressUpdateTime = 0;

    @Override
    public void invoke(BsReviewIssuesImportVO data, AnalysisContext context) {
        processedRowCount++;
        
        log.debug("处理第{}行数据，流水号：{}，审查意见：{}", processedRowCount, importNo, 
                data.getReviewComments() != null ? data.getReviewComments().substring(0, Math.min(50, data.getReviewComments().length())) : "空");

        // 尝试获取总行数（但可能不准确，最终在doAfterAllAnalysed中确定）
        if (totalRowCount == 0) {
            try {
                if (context.readSheetHolder() != null) {
                    int approximateTotal = context.readSheetHolder().getApproximateTotalRowNumber();
                    if (approximateTotal > 0) {
                        totalRowCount = approximateTotal;
                        log.info("从readSheetHolder获取到Excel预估总行数：{}，流水号：{}", totalRowCount, importNo);
                    }
                }
                
                // 如果上面的方法失败，尝试从readRowHolder获取
                if (totalRowCount == 0 && context.readRowHolder() != null) {
                    int currentRowIndex = context.readRowHolder().getRowIndex();
                    log.debug("当前行索引：{}，流水号：{}", currentRowIndex, importNo);
                }
            } catch (Exception e) {
                log.debug("无法获取Excel总行数，将在解析完成后确定，流水号：{}，错误：{}", importNo, e.getMessage());
            }
        }

        // 如果一行数据中"审查意见"列有内容，视其为一个新记录的开始
        if (StringUtils.hasText(data.getReviewComments())) {
            // 如果上一个问题对象还存在，说明它已经处理完毕，需要先进行收尾
            if (currentIssue != null) {
                finalizeCurrentIssue();
            }
            // 开始处理一个新的问题记录
            startNewIssue(data);
        } else {
            // 如果是连续行（"审查意见"为空），则聚合相关字段
            if (currentIssue != null) {
                if (StringUtils.hasText(data.getProposers())) {
                    currentProposers.add(data.getProposers().trim());
                }
                if (StringUtils.hasText(data.getProjectNames())) {
                    currentProjectNames.add(data.getProjectNames().trim());
                }
                if (StringUtils.hasText(data.getDesignUnits())) {
                    currentDesignUnits.add(data.getDesignUnits().trim());
                }
            }
        }

        // 实时更新进度
        updateProgress();
    }
    
    /**
     * 更新解析进度
     */
    private void updateProgress() {
        long currentTime = System.currentTimeMillis();
        
        // 控制更新频率：每处理PROGRESS_UPDATE_INTERVAL行数据或每隔1秒更新一次进度
        boolean shouldUpdate = (processedRowCount % PROGRESS_UPDATE_INTERVAL == 0) || 
                               (currentTime - lastProgressUpdateTime > 1000);
        
        if (shouldUpdate) {
            lastProgressUpdateTime = currentTime;
            
            int parseProgress;
            String message;
            
            if (totalRowCount > 0) {
                // 有总行数时，计算解析阶段的进度：10%-70%
                parseProgress = (int) (10 + (processedRowCount * 60.0 / totalRowCount));
                parseProgress = Math.min(parseProgress, 70); // 解析阶段最多到70%
                message = String.format("正在解析Excel数据... (%d/%d)", processedRowCount, totalRowCount);
            } else {
                // 没有总行数时，只更新已处理行数，进度保持在10%-30%之间动态变化
                parseProgress = 10 + (processedRowCount % 20); // 10%-30%之间变化
                message = String.format("正在解析Excel数据... (已处理%d行)", processedRowCount);
            }
            
            try {
                // 调用Service层更新进度，同时更新行数信息
                reviewIssuesService.updateImportProgressWithRowsFromListener(importNo, parseProgress, message, 
                        totalRowCount, processedRowCount);
                
                log.debug("更新解析进度：{}%，已处理：{}/{}行，流水号：{}", 
                        parseProgress, processedRowCount, totalRowCount > 0 ? totalRowCount : "未知", importNo);
            } catch (Exception e) {
                log.warn("更新解析进度失败，流水号：{}，错误：{}", importNo, e.getMessage());
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("开始doAfterAllAnalysed，流水号：{}，当前已处理行数：{}，当前总行数：{}", 
                importNo, processedRowCount, totalRowCount);
        
        // 确保总行数被正确设置
        if (totalRowCount == 0 && context.readRowHolder() != null) {
            try {
                totalRowCount = context.readRowHolder().getRowIndex() + 1;
                log.info("在doAfterAllAnalysed中通过readRowHolder获取到Excel总行数：{}，流水号：{}", totalRowCount, importNo);
            } catch (Exception e) {
                log.warn("在doAfterAllAnalysed中通过readRowHolder获取总行数失败，流水号：{}，错误：{}", importNo, e.getMessage());
            }
        }
        
        // 如果总行数仍然为0，使用已处理行数
        if (totalRowCount == 0) {
            totalRowCount = processedRowCount;
            log.info("使用已处理行数作为总行数：{}，流水号：{}", totalRowCount, importNo);
        }

        // 所有行都解析完毕后，确保文件中的最后一个问题记录也被正确地收尾
        if (currentIssue != null) {
            finalizeCurrentIssue();
            log.debug("完成最后一个记录的收尾，流水号：{}", importNo);
        }

        // 解析完成，更新进度到70%，同时更新最终的行数信息
        try {
            reviewIssuesService.updateImportProgressWithRowsFromListener(importNo, 70, 
                    String.format("Excel解析完成，共处理%d行数据，生成%d条记录", processedRowCount, importedIssues.size()),
                    totalRowCount, processedRowCount);
        } catch (Exception e) {
            log.warn("更新解析完成进度失败，流水号：{}，错误：{}", importNo, e.getMessage());
        }
        
        // Excel解析完成，日志记录
        log.info("Excel解析完成，流水号：{}，总计处理：{}行，最终总行数：{}，解析记录数：{}", 
                importNo, processedRowCount, totalRowCount, importedIssues.size());
    }

    /**
     * 在解析开始前尝试获取总行数
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("开始解析Excel，流水号：{}，表头：{}", importNo, headMap);
        
        // 尝试在开始时获取总行数
        try {
            if (context.readSheetHolder() != null) {
                int approximateTotal = context.readSheetHolder().getApproximateTotalRowNumber();
                if (approximateTotal > 0) {
                    totalRowCount = approximateTotal;
                    log.info("在invokeHeadMap中获取到Excel预估总行数：{}，流水号：{}", totalRowCount, importNo);
                    
                    // 获取到总行数后，立即更新一次进度显示
                    try {
                        reviewIssuesService.updateImportProgressWithRowsFromListener(importNo, 10, 
                                String.format("开始解析Excel文件，预计%d行数据...", totalRowCount), 
                                totalRowCount, 0);
                    } catch (Exception ex) {
                        log.debug("初始化解析进度失败，流水号：{}，错误：{}", importNo, ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("在invokeHeadMap中无法获取Excel总行数，流水号：{}，错误：{}", importNo, e.getMessage());
        }
    }

    /**
     * 开始处理一个新的问题记录
     */
    private void startNewIssue(BsReviewIssuesImportVO data) {
        currentIssue = new BsReviewIssues();
        currentIssue.setReviewComments(data.getReviewComments());
        currentIssue.setSpecificationClause(data.getSpecificationClause());
        currentIssue.setSubmissionCount(data.getSubmissionCount());
        currentIssue.setCommentType(data.getCommentType());

        // 为新记录清理并初始化数据收集器
        currentProposers.clear();
        if (StringUtils.hasText(data.getProposers())) {
            currentProposers.add(data.getProposers().trim());
        }

        currentProjectNames.clear();
        if (StringUtils.hasText(data.getProjectNames())) {
            currentProjectNames.add(data.getProjectNames().trim());
        }

        currentDesignUnits.clear();
        if (StringUtils.hasText(data.getDesignUnits())) {
            currentDesignUnits.add(data.getDesignUnits().trim());
        }
    }

    /**
     * 对当前处理的问题记录进行收尾，拼接多行数据
     */
    private void finalizeCurrentIssue() {
        // 使用分号连接收集到的多行字符串
        currentIssue.setProposers(String.join(";", currentProposers));
        currentIssue.setProjectNames(String.join(";", currentProjectNames));
        currentIssue.setDesignUnits(String.join(";", currentDesignUnits));

        // 根据 proposers 查询对应的 categoryKey 并设置到 categorys 字段
        setCategorysFromProposers();

        // 将构建完成的对象添加到最终结果列表中
        importedIssues.add(currentIssue);

        // 重置状态，为下一个记录做准备
        currentIssue = null;
        currentProposers.clear();
        currentProjectNames.clear();
        currentDesignUnits.clear();
    }

    /**
     * 根据 proposers 字段查询对应的 categoryKey 并设置到 categorys 字段
     */
    private void setCategorysFromProposers() {
        if (currentProposers.isEmpty()) {
            return;
        }

        List<String> categoryKeys = currentProposers.stream()
                .map(this::extractPersonName)
                .filter(StringUtils::hasText)
                .map(reviewStaffService::getCategoryKeyByPersonName)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        if (!categoryKeys.isEmpty()) {
            currentIssue.setCategorys(String.join(";", categoryKeys));
        }
    }

    /**
     * 从 proposers 字段中提取人员姓名，去除括号及其内容
     * 例如：吕效军(1) -> 吕效军
     */
    private String extractPersonName(String proposer) {
        if (!StringUtils.hasText(proposer)) {
            return null;
        }

        int bracketIndex = proposer.indexOf('(');
        if (bracketIndex > 0) {
            return proposer.substring(0, bracketIndex).trim();
        }

        return proposer.trim();
    }

    /**
     * 获取处理的总行数
     */
    public int getTotalRowCount() {
        return totalRowCount;
    }
    
    /**
     * 获取已处理的行数
     */
    public int getProcessedRowCount() {
        return processedRowCount;
    }
}
