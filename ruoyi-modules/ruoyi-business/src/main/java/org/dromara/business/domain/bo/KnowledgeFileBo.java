package org.dromara.business.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeFile;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 知识库文件业务对象
 */
@Data
@AutoMapper(target = KnowledgeFile.class)
public class KnowledgeFileBo extends BaseEntity {

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    /**
     * OSS文件ID
     */
    @NotNull(message = "OSS文件ID不能为空")
    private Long ossId;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空")
    @Size(max = 100, message = "文件名称长度不能超过100个字符")
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小（KB）
     */
    private Long fileSize;

    /**
     * 文件状态（0处理中 1已完成 2处理失败）
     */
    private String status;

    /**
     * 向量ID（数据治理服务返回）
     */
    private String vectorId;

    /**
     * 文件描述
     */
    private String remark;
}
