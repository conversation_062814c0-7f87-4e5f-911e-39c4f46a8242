package org.dromara.business.domain.vo;

import org.dromara.business.domain.AiModelChild;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 模型算力地址视图对象 ai_model_child
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiModelChild.class)
public class AiModelChildVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 模型id
     */
    @ExcelProperty(value = "模型id")
    private Long modelId;

    /**
     * 模型编码
     */
    @ExcelProperty(value = "模型编码")
    private String modelUrl;

    /**
     * 模型编码
     */
    @ExcelProperty(value = "模型编码")
    private String modelCode;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称")
    private String modelName;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 模型速度
     */
    private String modelSpeed;




}
