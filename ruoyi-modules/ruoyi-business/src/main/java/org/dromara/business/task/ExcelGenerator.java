package org.dromara.business.task;


import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ExcelGenerator {

    // 使用Map来存储和复用样式
    private static Map<String, CellStyle> styles;

    public static void main(String[] args) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            styles = createStyles(workbook);

            // --- 创建 Sheet 1: 数据统计 ---
            Sheet sheet1 = workbook.createSheet("施工图审查业务工作量统计");
            createTitle(sheet1);
            createHeader(sheet1);
            createDataRows(sheet1);
            createTotalRow(sheet1);
            createFooterSummary(sheet1);
            setColumnWidths(sheet1);

            // --- 创建 Sheet 2: 柱状图 ---
            Sheet sheet2 = workbook.createSheet("施工图柱状图");
            createChartSheet(sheet2);


            // 写入文件
            String filePath = "施工图审查统计（含图表）.xlsx";
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
                System.out.println("Excel文件已成功生成: " + filePath);
            }

        } catch (IOException e) {
            System.err.println("Error generating Excel file: " + e.getMessage());
        }
    }

    /**
     * 创建图表页
     */
    private static void createChartSheet(Sheet sheet) {
        // 首先在sheet中写入一些示例数据用于图表
        createChartData(sheet);

        // 创建绘图区域
        XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, 7, 15, 25);
        XSSFChart chart = drawing.createChart(anchor);
        chart.setTitleText("施工图审查业务工作量统计 - 柱状图");
        chart.setTitleOverlay(false);

        // 分类轴（横轴）
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("地区");

        // 数值轴（纵轴）
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        leftAxis.setTitle("百分比");
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setMinimum(0.0);
        leftAxis.setMaximum(1.0);

        // 数据源 - 假设数据在前几行
        XDDFDataSource<String> categories = XDDFDataSourcesFactory.fromStringCellRange(
                (XSSFSheet) sheet, new CellRangeAddress(1, 5, 0, 0));

        XDDFChartData data = chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
        ((XDDFBarChartData) data).setBarDirection(BarDirection.COL);

        // 添加数据系列
        for (int col = 1; col <= 4; col++) {
            XDDFNumericalDataSource<Double> values = XDDFDataSourcesFactory.fromNumericCellRange(
                    (XSSFSheet) sheet, new CellRangeAddress(1, 5, col, col));
            XDDFChartData.Series series = data.addSeries(categories, values);
            series.setTitle("系列" + col, null);
        }

        chart.plot(data);
    }

    /**
     * 创建图表数据
     */
    private static void createChartData(Sheet sheet) {
        // 表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("地区");
        headerRow.createCell(1).setCellValue("A机构");
        headerRow.createCell(2).setCellValue("B机构");
        headerRow.createCell(3).setCellValue("C机构");
        headerRow.createCell(4).setCellValue("D机构");

        // 示例数据
        String[] regions = {"兰山区", "罗庄", "河东", "沂南县", "兰陵"};
        double[][] values = {
            {0.31, 0.14, 0.19, 0.18},
            {0.53, 0.00, 0.14, 0.00},
            {0.24, 0.00, 0.21, 0.54},
            {0.29, 0.00, 0.61, 0.10},
            {0.54, 0.24, 0.00, 0.23}
        };

        for (int i = 0; i < regions.length; i++) {
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(regions[i]);
            for (int j = 0; j < values[i].length; j++) {
                row.createCell(j + 1).setCellValue(values[i][j]);
            }
        }
    }


    // ======================================================================
    // 以下是第一个Sheet所需的方法 (与上次提交相同，此处为保持完整性而包含)
    // ======================================================================

    private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // --- 字体 ---
        Font titleFont = wb.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);

        Font headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());

        Font boldFont = wb.createFont();
        boldFont.setBold(true);

        // --- 样式 ---
        // 标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.put("title", titleStyle);

        // 表头样式 (蓝色背景)
        XSSFCellStyle headerStyle = (XSSFCellStyle) wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(68, 114, 196), null));
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorders(headerStyle);
        styles.put("header", headerStyle);

        // 子表头样式 (灰色背景)
        XSSFCellStyle subHeaderStyle = (XSSFCellStyle) wb.createCellStyle();
        subHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        subHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        subHeaderStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(217, 217, 217), null));
        subHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorders(subHeaderStyle);
        styles.put("subheader", subHeaderStyle);

        // 子表头样式-粗体 (灰色背景)
        XSSFCellStyle subHeaderBoldStyle = (XSSFCellStyle) wb.createCellStyle();
        subHeaderBoldStyle.setAlignment(HorizontalAlignment.CENTER);
        subHeaderBoldStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        subHeaderBoldStyle.setFont(boldFont);
        subHeaderBoldStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(217, 217, 217), null));
        subHeaderBoldStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorders(subHeaderBoldStyle);
        styles.put("subheader_bold", subHeaderBoldStyle);

        // 数据单元格样式 (居中)
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        applyBorders(dataStyle);
        styles.put("data", dataStyle);

        // 数据单元格样式 (居中, 粗体)
        CellStyle dataBoldStyle = wb.createCellStyle();
        dataBoldStyle.setAlignment(HorizontalAlignment.CENTER);
        dataBoldStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataBoldStyle.setFont(boldFont);
        applyBorders(dataBoldStyle);
        styles.put("data_bold", dataBoldStyle);

        // 百分比格式样式
        CellStyle percentStyle = wb.createCellStyle();
        percentStyle.setAlignment(HorizontalAlignment.CENTER);
        percentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        percentStyle.setDataFormat(wb.createDataFormat().getFormat("0.00%"));
        applyBorders(percentStyle);
        styles.put("percent", percentStyle);

        return styles;
    }

    private static void applyBorders(CellStyle style) {
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
    }

    private static void createTitle(Sheet sheet) {
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("20250101-20250430施工图审查业务工作量统计");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
    }

    /**
     * 创建表头
     */
    private static void createHeader(Sheet sheet) {
        String[] headers1 = {"县区", "年度总面积", "同期总面积", "年度总项目个数", "同期总项目个数", "周新增项目总面积", "图审机构", "周分配项目个数", "周新增项目面积占比", "年度累计项目数量", "年度累计面积", "年度累计面积占比"};

        // 第1-3行表头
        Row row1 = sheet.createRow(1);
        Row row2 = sheet.createRow(2);
        Row row3 = sheet.createRow(3);

        for (int i = 0; i < headers1.length; i++) {
            Cell cell1 = row1.createCell(i);
            cell1.setCellValue(headers1[i]);
            cell1.setCellStyle(styles.get("header"));

            Cell cell2 = row2.createCell(i);
            cell2.setCellStyle(styles.get("header"));

            Cell cell3 = row3.createCell(i);
            cell3.setCellStyle(styles.get("header"));
        }

        // --- 处理合并 ---
        // 纵向合并
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 0, 0)); // 县区
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 1, 1)); // 年度总面积
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 2, 2)); // 同期总面积
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 3, 3)); // 年度总项目个数
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 4, 4)); // 同期总项目个数
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 8, 8)); // 周新增项目面积占比
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 9, 9)); // 年度累计项目数量
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 10, 10)); // 年度累计面积
        sheet.addMergedRegion(new CellRangeAddress(1, 3, 11, 11)); // 年度累计面积占比

        // 横向合并
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 6)); // 周新增项目总面积 & 图审机构

        // 子表头
        String[] subHeaders = {"周新增项目总面积", "图审机构", "周分配项目个数"};
        for (int i = 0; i < 3; i++) {
            row3.getCell(i + 5).setCellValue(subHeaders[i]);
            row3.getCell(i + 5).setCellStyle(styles.get("subheader"));
        }
    }

    /**
     * 填充数据行
     */
    private static void createDataRows(Sheet sheet) {
        Object[][] data = {
            {"平邑(2家)", 15.09, 24.06, 13, 1, "A", 1, 4.97, 1.00, 5, 7.08, 0.4692},
            {null, null, null, null, null, "D", null, null, null, 8, 8.01, 0.5308},
            {"蒙阴(2家)", 19.11, 12.38, 10, 0, "A", null, null, null, 7, 17.78, 0.9304},
            {null, null, null, null, null, "D", null, null, null, 3, 1.33, 0.0696},
            {"高新(2家)", 35.45, 34.59, 18, 0, "A", null, null, null, 14, 24.38, 0.6877},
            {null, null, null, null, null, "B", null, null, null, 4, 11.07, 0.3123},
            {"费县(2家)", 9.27, 30.84, 3, 2, "A", 2, 8.82, 1.00, 3, 9.27, 1.00},
            {null, null, null, null, null, "B", null, null, null, 0, 0.00, null},
            {"莒南(2家)", 15.73, 30.70, 10, 1, "A", null, null, null, 8, 15.04, 0.9561},
            {null, null, null, null, null, "B", 1, 0.49, 1.00, 2, 0.69, 0.0439},
            {"临沭(2家)", 16.25, 24.29, 12, 2, "A", 1, 0.49, 0.1546, 5, 6.64, 0.4086},
            {null, null, null, null, null, "B", 1, 2.68, 0.8454, 7, 9.61, 0.5914},
            {"郯城(2家)", 47.52, 22.23, 13, 1, "A", 1, 4.51, 1.00, 5, 14.84, 0.3123},
            {null, null, null, null, null, "B", null, null, null, 8, 32.68, 0.6877},
            {"郯城经开区(2家)", 3.42, 15.84, 3, 0, "A", null, null, null, 3, 3.42, 1.00},
            {null, null, null, null, null, "B", null, null, null, 0, 0.00, null},
            {"沂水管委会(2家)", 10.86, 23.49, 11, 0, "A", null, null, null, 5, 7.12, 0.6556},
            {null, null, null, null, null, "B", null, null, null, 6, 3.74, 0.3444},
        };

        int rowNum = 4;
        for (Object[] rowData : data) {
            Row row = sheet.createRow(rowNum++);
            int cellNum = 0;
            for (Object field : rowData) {
                Cell cell = row.createCell(cellNum++);
                if (field instanceof String) {
                    cell.setCellValue((String) field);
                } else if (field instanceof Number) {
                    // Check for percentage columns
                    if (cellNum == 9 || cellNum == 12) {
                        cell.setCellValue(((Number) field).doubleValue());
                        cell.setCellStyle(styles.get("percent"));
                        continue;
                    }
                    cell.setCellValue(((Number) field).doubleValue());
                }
                cell.setCellStyle(styles.get("data"));
            }
        }

        // --- 合并数据单元格 ---
        for (int i = 4; i < 22; i += 2) {
            sheet.addMergedRegion(new CellRangeAddress(i, i + 1, 0, 0)); // 县区
            sheet.addMergedRegion(new CellRangeAddress(i, i + 1, 1, 1)); // 年度总面积
            sheet.addMergedRegion(new CellRangeAddress(i, i + 1, 2, 2)); // 同期总面积
            sheet.addMergedRegion(new CellRangeAddress(i, i + 1, 3, 3)); // 年度总项目个数
            sheet.addMergedRegion(new CellRangeAddress(i, i + 1, 4, 4)); // 同期总项目个数
        }
    }

    /**
     * 创建合计行
     */
    private static void createTotalRow(Sheet sheet) {
        Row row = sheet.createRow(22);
        Row subRow = sheet.createRow(23);

        Cell cell = row.createCell(0);
        cell.setCellValue("合计");
        cell.setCellStyle(styles.get("data_bold"));

        double[] totals = {660.94, 747.28, 277, 26, 126.13};
        for (int i = 0; i < totals.length; i++) {
            cell = row.createCell(i + 1);
            cell.setCellValue(totals[i]);
            cell.setCellStyle(styles.get("data_bold"));
        }

        // --- “本期分配面积及占比”部分 ---
        cell = row.createCell(6);
        cell.setCellValue("本期分配面积及占比");
        cell.setCellStyle(styles.get("subheader_bold"));

        Object[][] totalData = {
                {"A", "面积", 64.26, "占比", 0.5095},
                {"B", "面积", 36.1, "占比", 0.2862},
                {"C", "面积", 25.77, "占比", 0.2043},
                {"D", "面积", 0.00, "占比", 0.00}
        };

        int startCol = 7;
        for (Object[] dataSet : totalData) {
            // 设置机构标题（A, B, C, D）
            cell = row.createCell(startCol);
            cell.setCellValue((String) dataSet[0]);
            cell.setCellStyle(styles.get("subheader_bold"));

            // 设置面积数据
            cell = row.createCell(startCol + 1);
            cell.setCellValue((Double) dataSet[2]);
            cell.setCellStyle(styles.get("data"));

            // 设置下一行的“面积”标签
            cell = subRow.createCell(startCol);
            cell.setCellValue((String) dataSet[1]);
            cell.setCellStyle(styles.get("subheader"));

            // 设置下一行的“占比”数据
            cell = subRow.createCell(startCol + 1);
            cell.setCellValue((Double)dataSet[4]); // 百分比值
            cell.setCellStyle(styles.get("percent"));

            // 为每个机构的数据块进行合并 (例如, H23:I23)
            sheet.addMergedRegion(new CellRangeAddress(22, 22, startCol, startCol + 1));
            startCol += 2;
        }

        // --- 最后的合并操作 ---
        sheet.addMergedRegion(new CellRangeAddress(22, 23, 0, 0)); // 合并“合计”标签
        sheet.addMergedRegion(new CellRangeAddress(22, 23, 1, 5)); // 合并“合计”旁边的空白区域以绘制边框

        // 填充空白单元格，以确保边框能够正确显示
        for (int i = 1; i < startCol; i++) {
            if (row.getCell(i) == null) row.createCell(i).setCellStyle(styles.get("data_bold"));
            if (subRow.getCell(i) == null) subRow.createCell(i).setCellStyle(styles.get("data"));
        }
    }

    /**
     * 创建底部汇总区域
     */
    private static void createFooterSummary(Sheet sheet) {
        Row row1 = sheet.createRow(24);
        Row row2 = sheet.createRow(25);

        Cell titleCell = row1.createCell(0);
        titleCell.setCellValue("各单位年度累计面积及占比");
        titleCell.setCellStyle(styles.get("data_bold"));
        sheet.addMergedRegion(new CellRangeAddress(24, 25, 0, 0));

        String[] headers = {"A", "B", "C", "D"};
        String[] subHeaders = {"本年度累计面积", "占比", "同期累计面积", "占比", "本年度累计面积", "占比", "本年度累计面积", "占比"};
        Object[] data = {283.86, 0.4295, 388.44, 0.5198, 193.65, 0.2930, 112.21, 0.1698, 71.22, null, 10.78, null};

        int cellNum = 1;
        for (int i = 0; i < headers.length; i++) {
            Cell hCell = row1.createCell(cellNum);
            hCell.setCellValue(headers[i]);
            hCell.setCellStyle(styles.get("subheader_bold"));
            sheet.addMergedRegion(new CellRangeAddress(24, 24, cellNum, cellNum + 1));

            Cell shCell1 = row2.createCell(cellNum);
            shCell1.setCellValue(subHeaders[i * 2]);
            shCell1.setCellStyle(styles.get("subheader"));

            Cell shCell2 = row2.createCell(cellNum + 1);
            shCell2.setCellValue(subHeaders[i * 2 + 1]);
            shCell2.setCellStyle(styles.get("subheader"));
            cellNum += 2;
        }

        // 底部数据
        Row dataRow = sheet.createRow(26);
        for (int i = 0; i < data.length; i++) {
            Cell cell = dataRow.createCell(i + 1);
            if (data[i] instanceof Number) {
                cell.setCellValue(((Number) data[i]).doubleValue());
                // 奇数列是百分比
                if ((i + 1) % 2 == 0) {
                    cell.setCellStyle(styles.get("percent"));
                } else {
                    cell.setCellStyle(styles.get("data"));
                }
            } else {
                cell.setCellStyle(styles.get("data"));
            }
        }

        // 合并底部数据行单元格
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 1, 2));
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 3, 4));
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 5, 6));
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 7, 8));
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 9, 10));
        sheet.addMergedRegion(new CellRangeAddress(26, 26, 11, 12));
    }

    /**
     * 设置列宽
     */
    private static void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 16 * 256); // 县区
        for (int i = 1; i <= 15; i++) {
            sheet.setColumnWidth(i, 12 * 256);
        }
    }
}
