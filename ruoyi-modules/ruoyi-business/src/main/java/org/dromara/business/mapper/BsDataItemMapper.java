package org.dromara.business.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.BsDataItem;
import org.dromara.business.domain.vo.BsDataItemVo;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 数据归纳Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface BsDataItemMapper extends BaseMapperPlus<BsDataItem, BsDataItemVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsDataItem> selectList(IPage<BsDataItem> page, @Param(Constants.WRAPPER) Wrapper<BsDataItem> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsDataItem> selectList(@Param(Constants.WRAPPER) Wrapper<BsDataItem> queryWrapper);

    @Override
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    }, joinStr = "AND")
    List<BsDataItem> selectByIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    int updateById(@Param(Constants.ENTITY) BsDataItem entity);

}
