package org.dromara.business.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeUserRel;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;

/**
 * 知识库用户关联业务对象
 */
@Data
@AutoMapper(target = KnowledgeUserRel.class)
public class KnowledgeUserRelBo extends BaseEntity {

    /**
     * 关联ID
     */
    private Long relId;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 权限类型（0查看 1编辑 2管理）
     */
    private String permissionType;

    /**
     * 来源类型
     */
    private String sourceType;
}
