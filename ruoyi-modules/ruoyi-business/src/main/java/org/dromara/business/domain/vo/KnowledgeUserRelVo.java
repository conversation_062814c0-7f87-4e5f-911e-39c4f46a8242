package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeUserRel;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serializable;
import java.util.Date;

/**
 * 知识库用户关联视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeUserRel.class)
public class KnowledgeUserRelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联ID
     */
    @ExcelProperty(value = "关联ID")
    private Long relId;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用户昵称
     */
    @ExcelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 权限类型
     */
    @ExcelProperty(value = "权限类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "knowledge_permission_type")
    private String permissionType;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
