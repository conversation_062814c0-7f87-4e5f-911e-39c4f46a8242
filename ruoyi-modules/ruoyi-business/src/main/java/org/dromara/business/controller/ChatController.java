package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.service.IChatService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendReqVO;
import org.dromara.common.core.domain.vo.ChatMessageSendRespVO;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.time.Duration;

@Slf4j
@SaIgnore
@RestController
@RequiredArgsConstructor
@RequestMapping("/v2/ai")
public class ChatController {

    private final IChatService chatService;

    @RateLimiter
    @RepeatSubmit
    @PostMapping(value = "/generateStream", produces = {MediaType.TEXT_EVENT_STREAM_VALUE + "; charset=UTF-8"})
    public Flux<R<ChatMessageSendRespVO>> generateStream(@RequestBody @Validated ChatMessageSendReqVO chatMessageSendReqVO) {
        return chatService.generate(chatMessageSendReqVO);
    }

    /**
     * 流式生成会话
     */
    @RepeatSubmit
    @PostMapping(value = "/generateStreamChat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE + "; charset=UTF-8"})
    public Flux<R<ChatMessageSendRespVO>> generateStreamChat(@RequestBody @Validated ChatMessageSendReqVO chatMessageSendReqVO) {
        //锁前缀
        String lockKey = "conversation:";
        //当前请求
        HttpServletRequest request = ServletUtils.getRequest();
        if (request == null) {
            return Flux.just(R.fail("检测到非法攻击,拒绝响应"));
        } else {
            String browserId = request.getHeader("Browser");
            if (StringUtils.isEmpty(browserId)) {
                return Flux.just(R.fail("检测到非法攻击,拒绝响应"));
            } else {
                lockKey = lockKey + "B:" + browserId;
            }
            Long userId = LoginHelper.getUserId();
            if (userId != null) {
                lockKey = lockKey + "U:" + userId;
            }
        }
        if (RedisUtils.hasKey(lockKey)) {
            return Flux.just(R.fail("服务器繁忙,请稍后"));
        } else {
            RedisUtils.setCacheObject(lockKey, 1, Duration.ofSeconds(5));
            return chatService.generateStreamChat(chatMessageSendReqVO, lockKey);
        }
    }
}
