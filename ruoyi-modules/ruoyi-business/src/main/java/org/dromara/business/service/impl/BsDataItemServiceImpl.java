package org.dromara.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Sets;
import org.dromara.business.domain.BsDataItem;
import org.dromara.business.domain.bo.BsDataItemBo;
import org.dromara.business.domain.vo.BsDataItemVo;
import org.dromara.business.mapper.BsDataItemMapper;
import org.dromara.business.service.IBsDataItemService;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据归纳Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@RequiredArgsConstructor
@Service
public class BsDataItemServiceImpl implements IBsDataItemService {

    private final BsDataItemMapper baseMapper;
    private final SysUserMapper userMapper;

    /**
     * 查询数据归纳
     *
     * @param id 主键
     * @return 数据归纳
     */
    @Override
    public BsDataItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询数据归纳列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 数据归纳分页列表
     */
    @Override
    public TableDataInfo<BsDataItemVo> queryPageList(BsDataItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsDataItem> lqw = buildQueryWrapper(bo);
        Page<BsDataItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的数据归纳列表
     *
     * @param bo 查询条件
     * @return 数据归纳列表
     */
    @Override
    public List<BsDataItemVo> queryList(BsDataItemBo bo) {
        LambdaQueryWrapper<BsDataItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsDataItem> buildQueryWrapper(BsDataItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsDataItem> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDataName()), BsDataItem::getDataName, bo.getDataName());
        lqw.eq(bo.getDataSource() != null, BsDataItem::getDataSource, bo.getDataSource());
        lqw.eq(bo.getDataClass() != null, BsDataItem::getDataClass, bo.getDataClass());
        lqw.eq(bo.getDataState() != null, BsDataItem::getDataState, bo.getDataState());
        lqw.like(StringUtils.isNotBlank(bo.getDataField()), BsDataItem::getDataField, bo.getDataField());
        if (bo.getDataProject() != null) {
            lqw.like(BsDataItem::getDataProject, String.join("", bo.getDataProject()));
        }
        return lqw;
    }

    /**
     * 新增数据归纳
     *
     * @param bo 数据归纳
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsDataItemBo bo) {
        BsDataItem add = MapstructUtils.convert(bo, BsDataItem.class);
        validEntityBeforeSave(add);
        //关联权限
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null && add != null) {
            add.setDeptId(loginUser.getDeptId());
            add.setUserId(loginUser.getUserId());
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改数据归纳
     *
     * @param bo 数据归纳
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsDataItemBo bo) {
        BsDataItem update = MapstructUtils.convert(bo, BsDataItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsDataItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除数据归纳信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsDataItem> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
