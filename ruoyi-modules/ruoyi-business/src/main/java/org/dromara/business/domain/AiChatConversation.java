package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * AI 聊天对话对象 ai_chat_conversation
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_chat_conversation")
public class AiChatConversation extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 对话编号
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 知识库编号
     */
    private Long knowledgeId;
    /**
     * 对话标题
     */
    private String title;
    /**
     * 是否置顶（0-否，1-是）
     */
    private Integer pinned;
    /**
     * 置顶时间
     */
    private Date pinnedTime;
    /**
     * 角色设定
     */
    private String systemMessage;
    /**
     * 温度参数
     */
    private Double temperature;
    /**
     * 单条回复的最大 Token 数量
     */
    private Long maxTokens;
    /**
     * 上下文的最大 Message 数量
     */
    private Integer maxContexts;
    /**
     * 删除标记 (0:未删除，1:已删除)
     */
    private Integer delFlag;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 浏览器标志
     */
    private String browserId;
}
