package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库分类对象
 */
@TableName(value = "knowledge_category")
@Data
public class KnowledgeCategory extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String description;

    private Integer sort;

    private String status;

    /**
     * 部门ID
     */
    private Long deptId;
}
