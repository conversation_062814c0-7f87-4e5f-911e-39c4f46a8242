package org.dromara.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.dromara.business.domain.*;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.llm.FunctionTools;
import org.dromara.business.mapper.AiChatMessageMapper;
import org.dromara.business.service.IAiModelAuthService;
import org.dromara.business.service.IAiModelService;
import org.dromara.business.service.IChatService;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.utils.AiUtils;
import org.dromara.common.core.constant.AIMessageConstants;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendReqVO;
import org.dromara.common.core.domain.vo.ChatMessageSendRespVO;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysOssVo;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatServiceImpl implements IChatService {

    @Resource
    private AiChatConversationServiceImpl aiChatConversationService;
    @Resource
    private AiChatMessageMapper aiChatMessageMapper;
    @Resource
    private StatisticsServiceImpl statisticsService;
    @Resource
    private IAiModelService aiModelService;
    @Resource
    private IAiModelAuthService aiModelAuthService;
    @Resource
    private AiModelChildServiceImpl aiModelChildService;
    @Resource
    private DataSaveServiceImpl dataSaveService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    //前端支持的图片类型
    private final List<String> imgType = Arrays.asList(".png", ".jpg", ".jpeg");
    //演示模板
    private String template1;
    private String template2;
    private String template3;

    //提取文件内容
    private String parseFile(Tika tika, String fileName) throws IOException, TikaException {
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream inputStream = resource.getStream()) {
            File tempFile = File.createTempFile("temp-resource", getFileExtension(fileName));
            Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            String content = tika.parseToString(tempFile);
            tempFile.deleteOnExit();
            return content;
        }
    }

    //提取文件扩展名
    private String getFileExtension(String fileName) {
        int lastIndex = fileName.lastIndexOf(".");
        return lastIndex != -1 ? fileName.substring(lastIndex) : "";
    }

    @PostConstruct
    public void init() {
        try {
            Tika tika = new Tika();
            //交通模板
            template1 = parseFile(tika, "test1.docx");
            //放假通知
            template2 = parseFile(tika, "test2.docx");
            //工作报告
            template3 = parseFile(tika, "test3.docx");
        } catch (IOException | TikaException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 普通生成会话（慧眼）
     */
    @Override
    public Flux<R<ChatMessageSendRespVO>> generate(ChatMessageSendReqVO chatMessageSendReqVO) {
        //前置条件，校验模型
        HttpServletRequest request = ServletUtils.getRequest();
        if (request != null) {
            String modelKey = request.getHeader("Model-Key");
            String browserId = request.getHeader("Browser");

            //根据令牌查询授权信息
            AiModelAuth aiModelAuth = aiModelAuthService.getAuthInChat(modelKey);
            if (aiModelAuth == null) {
                return Flux.just(R.fail("模型令牌未授权"));
            }
            if (aiModelAuth.getExpirationDate().getTime() < System.currentTimeMillis()) {
                return Flux.just(R.fail("模型令牌已过期"));
            }
            //查询并判断模型信息
            AiModel aiModel = aiModelService.getModelInfoInChat(aiModelAuth.getModelId());
            if (aiModel == null) {
                return Flux.just(R.fail("模型不存在，请联系管理员"));
            }
            if (aiModel.getStatus() == 0) {
                return Flux.just(R.fail("模型未开启，请联系管理员"));
            }
            //校验会话是否存在
            AiChatConversation aiChatConversation = aiChatConversationService.getChatConversation(chatMessageSendReqVO.getConversationId());
            if (null == aiChatConversation) {
                return Flux.just(R.fail("请开启一个新对话"));
            }

            //构建用户问题
            String userMessageStr = chatMessageSendReqVO.getUserMessage();
            //定义文件内容
            Map<String, String> files = new HashMap<>();
            //联网搜索
            if (chatMessageSendReqVO.getOnlineSearch() == 1) {
                //定义基础提示词
                String allPrompt = """
                    根据用户提供的信息，对用户的问题进行回答，如果参考信息为空则不参考
                    {0}
                    用户的问题: {1}
                    """;
                for (int i = 1; i <= chatMessageSendReqVO.getReferMessage().size(); i++) {
                    ChatMessageSendReqVO.ContentEntity contentEntity = chatMessageSendReqVO.getReferMessage().get(i - 1);
                    files.put("参考信息" + i, contentEntity.getReferContent());
                }
                userMessageStr = MessageFormat.format(allPrompt, files.isEmpty() ? "" : JSONUtil.toJsonStr(files), chatMessageSendReqVO.getUserMessage());
                //如果对话窗口超限，则提示用户
                if (userMessageStr.length() > aiModel.getMaxTokens()) {
                    return Flux.just(R.fail("会话内容超限！请开启一个新会话"));
                }
            }
            //获取历史消息
            List<AiChatMessage> aiChatMessages = aiChatMessageMapper.selectListByConversationId(chatMessageSendReqVO.getConversationId());
            //插入用户发送消息
            AiChatMessageVo userMessage = createChatMessage(aiChatConversation, MessageType.USER, chatMessageSendReqVO, browserId);
            //插入assistant接收消息
            AiChatMessageVo assistantMessage = createChatMessage(aiChatConversation, MessageType.ASSISTANT, chatMessageSendReqVO, browserId);
            //构建Prompt
            Prompt prompt = buildBasePrompt(aiChatMessages, aiModel, true);
            //初始化构建模型，并调用
//            OllamaChatModel chatModel = getChatModel(aiModel, modelKey, null);
            OllamaApi ollamaApi = OllamaApi.builder().baseUrl("http://172.20.147.85:30000").build();
            OllamaChatModel chatModel = OllamaChatModel.builder().ollamaApi(ollamaApi).build();

            Flux<ChatResponse> streamResponse;
            if (chatMessageSendReqVO.getOnlineSearch() == 1) {
                streamResponse = ChatClient.create(chatModel).prompt(prompt).system(AIMessageConstants.Massage2).user(userMessageStr).stream().chatResponse();
            } else {
                streamResponse = ChatClient.create(chatModel).prompt(prompt).system(AIMessageConstants.Massage2).user(userMessageStr).tools(new FunctionTools()).stream().chatResponse();
            }
            //获取结果并返回
            StringBuilder stringBuilder = new StringBuilder();
            return streamResponse.map(response -> {
                stringBuilder.append(StrUtil.nullToDefault(response.getResult() != null ? response.getResult().getOutput().getText() : null, StrUtil.EMPTY));
                //给前端封装返回消息实体
                ChatMessageSendRespVO chatMessageSendRespVO = new ChatMessageSendRespVO();
                ChatMessageSendRespVO.Message send = new ChatMessageSendRespVO.Message();
                send.setId(userMessage.getId());
                send.setType(userMessage.getType());
                send.setContent(userMessage.getContent());
                send.setCreateTime(userMessage.getCreateTime());
                chatMessageSendRespVO.setSend(send);
                ChatMessageSendRespVO.Message receive = new ChatMessageSendRespVO.Message();
                receive.setId(assistantMessage.getId());
                receive.setType(assistantMessage.getType());
                receive.setContent(stringBuilder.toString());
                receive.setCreateTime(assistantMessage.getUpdateTime());
                chatMessageSendRespVO.setReceive(receive);
                return R.ok(chatMessageSendRespVO);
            }).doOnComplete(() -> {
                AiChatMessage aiChatMessage = processingChat(chatMessageSendReqVO, aiModelAuth, aiModel, aiChatConversation, assistantMessage, stringBuilder);
                aiChatMessage.setCreateTime(new Date());
                aiChatMessage.setUpdateTime(new Date());
                aiChatMessageMapper.updateById(aiChatMessage);
            }).doOnError(throwable -> {
                AiChatMessage aiChatMessage = new AiChatMessage();
                aiChatMessage.setId(assistantMessage.getId());
                aiChatMessage.setContent(throwable.getMessage());
                aiChatMessage.setCreateTime(new Date());
                aiChatMessage.setUpdateTime(new Date());
                aiChatMessageMapper.updateById(aiChatMessage);
            }).onErrorResume(error -> Flux.just(R.fail(error.getMessage())));
        }
        return Flux.just(R.fail(" 响应完成 "));
    }

    /**
     * 流式生成会话（公共）
     */
    @Override
    public Flux<R<ChatMessageSendRespVO>> generateStreamChat(ChatMessageSendReqVO chatMessageSendReqVO, String lockKey) {
        try {
            //前置条件，校验模型
            HttpServletRequest request = ServletUtils.getRequest();
            if (request != null) {
                String key = request.getHeader("Model-Key");
                if (StringUtils.isEmpty(key)) {
                    return Flux.just(R.fail("令牌缺失"));
                }

                //根据令牌查询授权信息
                AiModelAuth aiModelAuth = aiModelAuthService.getAuthInChat(key);
                if (aiModelAuth == null) {
                    return Flux.just(R.fail("无效令牌"));
                }
                if (aiModelAuth.getExpirationDate().getTime() < System.currentTimeMillis()) {
                    return Flux.just(R.fail("令牌已过期"));
                }
                //查询并判断模型信息
                AiModel aiModel = aiModelService.getModelInfoInChat(aiModelAuth.getModelId());
                if (aiModel == null) {
                    return Flux.just(R.fail("未找到令牌对应的模型，请联系管理员"));
                }
                if (aiModel.getStatus() == 0) {
                    return Flux.just(R.fail("令牌对应的模型未开启，请联系管理员"));
                }
                //校验会话是否存在
                AiChatConversation aiChatConversation = aiChatConversationService.getChatConversation(chatMessageSendReqVO.getConversationId());
                if (null == aiChatConversation) {
                    return Flux.just(R.fail("请开启一个新对话"));
                }

                //上传文件和联网搜索互斥。此处做校验
                if (chatMessageSendReqVO.getOnlineSearch() == 1 && CollUtil.isNotEmpty(chatMessageSendReqVO.getFileIdList())) {
                    return Flux.just(R.fail("联网模式下请不要上传参考文件哦"));
                }

                //定义上传的信息
                Map<String, String> files = new HashMap<>();
                //知识库信息
                Map<String, String> knowledge;
                //定义基础提示词
                String allPrompt = """
                    {0}
                    用户的问题: {1}
                    """;

                //判断联网搜索，或者提供的文档
                if (chatMessageSendReqVO.getOnlineSearch() == 1) {
                    log.info("联网搜索暂未开发");
                } else if (CollUtil.isNotEmpty(chatMessageSendReqVO.getFileIdList())) {
                    if (chatMessageSendReqVO.getFileIdList().size() == 1) {
                        SysOssVo content = dataSaveService.getFileContent(chatMessageSendReqVO.getFileIdList().get(0));
                        if (null != content) {
                            if(imgType.contains(content.getFileSuffix())){
                                files.put("图片1", content.getFileContent());
                            }else {
                                files.put("文件1", content.getFileContent());
                            }
                        }
                    } else {
                        files = doThread(chatMessageSendReqVO.getFileIdList());
                    }
                } else if (CollUtil.isNotEmpty(chatMessageSendReqVO.getReferMessage())) {
                    for (int i = 1; i <= chatMessageSendReqVO.getReferMessage().size(); i++) {
                        ChatMessageSendReqVO.ContentEntity contentEntity = chatMessageSendReqVO.getReferMessage().get(i - 1);
                        files.put("文档" + i, contentEntity.getReferContent());
                    }
                }

                //如果知识库不为空
                if (chatMessageSendReqVO.getKnowledgeBaseId() != null) {
                    String knowledgeBaseId = chatMessageSendReqVO.getKnowledgeBaseId();
                    switch (knowledgeBaseId) {
                        case "2DC1F243-E6A1-A24B-748E-09BE6EA5D5BB" -> files.put("行政处罚模板", template1);
                        case "02621396-67ED-6E75-35AB-E0074A4AD122" -> files.put("放假通知模板", template2);
                        case "0895a96-67ED-6E75-35AB-E0074A4A5547" -> files.put("政府工作报告", template3);
                        default -> {
                            //判断是否登录
                            if (!LoginHelper.isLogin()) {
                                return Flux.just(R.fail("知识库功能需要登录后使用哦"));
                            }
                            //检索知识库
                            knowledge = knowledgeBaseService.getDocumentInfos(knowledgeBaseId, chatMessageSendReqVO.getUserMessage(),LoginHelper.getLoginUser());
                            for (Map.Entry<String, String> entry : knowledge.entrySet()) {
                                String value = entry.getValue();
                                files.put("查询结果" + (files.size() + 1), value);
                            }
                        }
                    }
                }

                //浏览器标识
                String browserId = request.getHeader("Browser");
                //获取历史消息
                List<AiChatMessage> aiChatMessages;
                aiChatMessages = aiChatMessageMapper.selectListByConversationId(chatMessageSendReqVO.getConversationId());
                //插入用户发送消息
                AiChatMessageVo userMessage = createChatMessage(aiChatConversation, MessageType.USER, chatMessageSendReqVO, browserId);
                //插入assistant接收消息
                AiChatMessageVo assistantMessage = createChatMessage(aiChatConversation, MessageType.ASSISTANT, chatMessageSendReqVO, browserId);
                //构建Prompt
                Prompt prompt = buildBasePrompt(aiChatMessages, aiModel, false);
                //调用模型 初始化构建模型，并调用
                OllamaChatModel chatModel = getChatModel(aiModel, key, browserId);
                //构建用户问题
                String userMessageStr = MessageFormat.format(allPrompt, files.isEmpty() ? "" : JSONUtil.toJsonStr(files), chatMessageSendReqVO.getUserMessage());
                //如果对话窗口超限，则提示用户
                if (userMessageStr.length() > aiModel.getMaxTokens()) {
                    return Flux.just(R.fail("会话内容超限！请开启一个新会话"));
                }
                //发送对话
                Flux<ChatResponse> streamResponse = ChatClient.create(chatModel).prompt(prompt)
                    .system(StringUtils.isEmpty(chatMessageSendReqVO.getSystemMessage()) ? "根据用户提供的信息对用户的问题进行回答，如果提供的信息为空则不参考。" : chatMessageSendReqVO.getSystemMessage())
                    .user(userMessageStr)
                    .stream().chatResponse();
                //封装返回结果
                StringBuilder stringBuilder = new StringBuilder();
                ChatMessageSendRespVO chatMessageSendRespVO = new ChatMessageSendRespVO();
                ChatMessageSendRespVO.Message send = new ChatMessageSendRespVO.Message();
                ChatMessageSendRespVO.Message receive = new ChatMessageSendRespVO.Message();
                send.setId(userMessage.getId());
                send.setType(userMessage.getType());
                send.setContent(userMessage.getContent());
                send.setCreateTime(userMessage.getCreateTime());
                send.setFileInfo(userMessage.getFileInfo());
                chatMessageSendRespVO.setSend(send);
                receive.setId(assistantMessage.getId());
                receive.setType(assistantMessage.getType());
                receive.setCreateTime(new Date());
                chatMessageSendRespVO.setReceive(receive);
                return streamResponse.map(response -> {
                    //提取每次的字符并追加到缓冲区
                    String word = StrUtil.nullToDefault(response.getResult() != null ? response.getResult().getOutput().getText() : null, StrUtil.EMPTY);
                    stringBuilder.append(word);
                    receive.setContent(word);
                    return R.ok(chatMessageSendRespVO);
                }).doOnComplete(() -> {
                    AiChatMessage aiChatMessage = processingChat(chatMessageSendReqVO, aiModelAuth, aiModel, aiChatConversation, assistantMessage, stringBuilder);
                    aiChatMessage.setModelUrl(aiModel.getModelUrl());
                    aiChatMessage.setCreateTime(new Date());
                    aiChatMessage.setUpdateTime(new Date());
                    aiChatMessageMapper.updateById(aiChatMessage);
                    RedisUtils.deleteObject(lockKey);
                }).doOnError(throwable -> {
                    if (LoginHelper.getUserId() != null) {
                        RedisUtils.deleteObject(lockKey);
                    }
                }).onErrorResume(error -> Flux.just(R.fail(error.getMessage())));
            } else {
                return Flux.just(R.fail("服务器繁忙,请稍后重试"));
            }
        } catch (Exception e) {
            log.error("流式会话异常：{}", e.getMessage());
            return Flux.just(R.fail("服务器繁忙,请稍后重试"));
        }
    }

    //提取重复方法
    private AiChatMessage processingChat(ChatMessageSendReqVO chatMessageSendReqVO, AiModelAuth aiModelAuth, AiModel aiModel, AiChatConversation aiChatConversation, AiChatMessageVo assistantMessage, StringBuilder stringBuilder) {
        if (ObjectUtil.equals(aiChatConversation.getTitle(), "新对话")) {
            String title = StrUtil.sub(chatMessageSendReqVO.getUserMessage(), 0, 10);
            aiChatConversation.setModelId(aiModel.getId());
            aiChatConversation.setModelKey(aiModelAuth.getModelKey());
            aiChatConversation.setModelName(aiModel.getModelName());
            aiChatConversation.setTitle(title);
            aiChatConversationService.updateBy(aiChatConversation);
        }
        AiChatMessage aiChatMessage = new AiChatMessage();
        aiChatMessage.setId(assistantMessage.getId());
        aiChatMessage.setContent(stringBuilder.toString());
        return aiChatMessage;
    }

    /**
     * 多线程执行读取文件
     */
    private Map<String, String> doThread(List<Long> fileIdList) throws Exception{
        Map<String, String> files = new ConcurrentHashMap<>();
        CountDownLatch countDownLatch = new CountDownLatch(fileIdList.size());
        AtomicInteger atomicInteger = new AtomicInteger(0);
        fileIdList.forEach(id -> threadPoolTaskExecutor.execute(() -> {
            SysOssVo content = null;
            try {
                content = dataSaveService.getFileContent(id);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            if (content.getFileContent() != null) {
                files.put("文件" + (atomicInteger.get() + 1), content.getFileContent());
            }
            atomicInteger.addAndGet(1);
            countDownLatch.countDown();
        }));
        countDownLatch.await();
        return files;
    }

    /**
     * 插入响应消息
     */
    private AiChatMessageVo createChatMessage(AiChatConversation conversation, MessageType messageType, ChatMessageSendReqVO chatMessageSendReqVO, String browserId) {
        AiChatMessage message = new AiChatMessage();
        message.setUserId(conversation.getUserId());
        message.setTenantId(conversation.getTenantId());
        message.setCreateDept(conversation.getCreateDept());
        message.setConversationId(conversation.getId());
        message.setModelId(conversation.getModelId());
        message.setModelKey(conversation.getModelKey());
        message.setModelName(conversation.getModelName());
        message.setType(messageType.getValue());
        message.setCreateTime(new Date());
        message.setContent(chatMessageSendReqVO.getUserMessage());
        message.setBrowserId(browserId);
        if ("assistant".equals(messageType.getValue())) {
            message.setContent("服务器繁忙,请稍后重试");
        }
        //用户如果发送文件，则保存文件json信息
        if ("user".equals(messageType.getValue()) && CollUtil.isNotEmpty(chatMessageSendReqVO.getReferMessage())) {
            message.setFileInfo(JSONUtil.toJsonStr(chatMessageSendReqVO.getReferMessage()));
        }
        aiChatMessageMapper.insert(message);
        return BeanUtil.toBean(message, AiChatMessageVo.class);
    }

    /**
     * 构建消息
     */
    private Prompt buildBasePrompt(List<AiChatMessage> aiChatMessages, AiModel aiModel, boolean isHy) {
        //构建 Prompt Message 列表
        List<Message> chatMessages = new ArrayList<>();
        if (isHy) {
            loadSystemMessageByYMHY(chatMessages);
        }
        //1.加入历史消息
        Collections.reverse(aiChatMessages);
        Map<String, List<AiChatMessage>> map = aiChatMessages.stream().collect(Collectors.groupingBy(AiChatMessage::getType));
        List<AiChatMessage> userMessages = map.get("user");
        List<AiChatMessage> assistantMessages = map.get("assistant");
        if (CollUtil.isNotEmpty(userMessages)) {
            for (int i = 0; i < userMessages.size(); i++) {
                chatMessages.add(new UserMessage(userMessages.get(i).getContent()));
                chatMessages.add(new AssistantMessage(assistantMessages.get(i).getContent()));
            }
        }
        //2.设置模型参数
        ChatOptions chatOptions = AiUtils.buildChatOptions(aiModel.getModelCode(), aiModel.getTemperature(), Integer.parseInt(aiModel.getMaxTokens().toString()));
        //3.合并所有消息
        return new Prompt(chatMessages, chatOptions);
    }

    /**
     * 加载沂蒙慧眼的特殊数据
     */
    private void loadSystemMessageByYMHY(List<Message> chatMessages) {
        //画像统计
        String companyInfo = statisticsService.loadData();
        String companyInfoSum = statisticsService.loadDataSum();
        chatMessages.add(new UserMessage("已为整个临沂市多少家企业进行了画像?"));
        chatMessages.add(new AssistantMessage(companyInfoSum));
        chatMessages.add(new UserMessage("每个县区下画像的数量是多少?"));
        chatMessages.add(new AssistantMessage(companyInfo));
        //企业统计
        String companyCount = statisticsService.loadCompanyCount();
        String companyCountSum = statisticsService.loadCompanyCountSum();
        chatMessages.add(new UserMessage("临沂市目前有多少家在营企业?"));
        chatMessages.add(new AssistantMessage(companyCountSum));
        chatMessages.add(new UserMessage("临沂市各县区下分别有多少家在营企业?"));
        chatMessages.add(new AssistantMessage(companyCount));
        //统计所有评分前十
        String scoreCount = statisticsService.loadCompanyRank();
        chatMessages.add(new UserMessage("临沂市不同等级企业的大概分布情况"));
        chatMessages.add(new AssistantMessage(scoreCount));
        //合并系统对话
    }

    /**
     * 获取模型负载地址
     */
    private OllamaChatModel getChatModel(AiModel aiModel, String key, String browserId) {
        //算力地址
        String returnUrl = "";
        //前缀 + 模型编码 + 算力密钥 + 设备唯一标识
        String redisKey = CacheConstants.AI_MODEL_KEY + aiModel.getModelCode() + ":" + key + ":" + browserId;
        if (RedisUtils.hasKey(redisKey)) {
            //当前设备分配的通道
            returnUrl = RedisUtils.getCacheObject(redisKey);
        } else {
            //查询库内所有通道
            List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelId(aiModel.getId());
            if (CollUtil.isNotEmpty(aiModelChildList)) {
                //仅一个通道，直接设置
                if (aiModelChildList.size() == 1) {
                    returnUrl = aiModelChildList.get(0).getModelUrl();
                    RedisUtils.setCacheObject(redisKey, returnUrl);
                } else {
                    //没有负载，从redis中获取一个当前模型code负载最少的模型地址
                    List<String> urlCountList = new ArrayList<>();
                    Set<String> allKeys = new HashSet<>(RedisUtils.keys(CacheConstants.AI_MODEL_KEY + aiModel.getModelCode() + ":*"));
                    for (String userKey : allKeys) {
                        String userUrl = RedisUtils.getCacheObject(userKey);
                        if (StringUtils.isNotEmpty(userUrl)) {
                            urlCountList.add(userUrl);
                        }
                    }
                    //分组统计每个通道多少人
                    Map<String, Long> urlGroup = urlCountList.stream().collect(Collectors.groupingBy(e -> e, Collectors.counting()));
                    //将数据库中最新的添加到此分组内
                    aiModelChildList.forEach(child -> {
                        if (!urlGroup.containsKey(child.getModelUrl())) {
                            urlGroup.put(child.getModelUrl(), 0L);
                        }
                    });
                    //找到最小值
                    Optional<Map.Entry<String, Long>> minEntry = urlGroup.entrySet().stream().min(Map.Entry.comparingByValue());
                    if (minEntry.isPresent()) {
                        returnUrl = minEntry.get().getKey();
                    }
                    //存入redis中
                    RedisUtils.setCacheObject(redisKey, returnUrl);
                }
            } else {
                throw new RuntimeException("当前模型令牌，暂未提供算力通道");
            }
        }
        //当前设备分配的通道
        aiModel.setModelUrl(returnUrl);
        //增加10m停留时间
        RedisUtils.expire(redisKey, Duration.ofMinutes(10));
        OllamaApi ollamaApi = OllamaApi.builder().baseUrl(returnUrl).build();
        return OllamaChatModel.builder().ollamaApi(ollamaApi).build();
    }
}
