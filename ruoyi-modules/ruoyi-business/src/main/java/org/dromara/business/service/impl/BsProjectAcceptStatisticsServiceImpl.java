package org.dromara.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.business.domain.BsProjectAccept;
import org.dromara.business.domain.BsProjectAcceptStatistics;
import org.dromara.business.domain.bo.BsProjectAcceptStatisticsBo;
import org.dromara.business.domain.vo.BsProjectAcceptStatisticsVo;
import org.dromara.business.mapper.BsProjectAcceptMapper;
import org.dromara.business.mapper.BsProjectAcceptStatisticsMapper;
import org.dromara.business.service.IBsProjectAcceptStatisticsService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 施工图项目受理清单统计记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class BsProjectAcceptStatisticsServiceImpl implements IBsProjectAcceptStatisticsService {

    private final BsProjectAcceptStatisticsMapper baseMapper;
    private final BsProjectAcceptMapper projectAcceptMapper;

    /**
     * 查询施工图项目受理清单统计记录
     *
     * @param id 主键
     * @return 施工图项目受理清单统计记录
     */
    @Override
    public BsProjectAcceptStatisticsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询施工图项目受理清单统计记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 施工图项目受理清单统计记录分页列表
     */
    @Override
    public TableDataInfo<BsProjectAcceptStatisticsVo> queryPageList(BsProjectAcceptStatisticsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = buildQueryWrapper(bo);
        Page<BsProjectAcceptStatisticsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的施工图项目受理清单统计记录列表
     *
     * @param bo 查询条件
     * @return 施工图项目受理清单统计记录列表
     */
    @Override
    public List<BsProjectAcceptStatisticsVo> queryList(BsProjectAcceptStatisticsBo bo) {
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsProjectAcceptStatistics> buildQueryWrapper(BsProjectAcceptStatisticsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStartDate() != null, BsProjectAcceptStatistics::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, BsProjectAcceptStatistics::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BsProjectAcceptStatistics::getStatus, bo.getStatus());
        lqw.eq(bo.getOssId() != null, BsProjectAcceptStatistics::getOssId, bo.getOssId());
        return lqw;
    }

    /**
     * 新增施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsProjectAcceptStatisticsBo bo) {
        BsProjectAcceptStatistics add = MapstructUtils.convert(bo, BsProjectAcceptStatistics.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsProjectAcceptStatisticsBo bo) {
        BsProjectAcceptStatistics update = MapstructUtils.convert(bo, BsProjectAcceptStatistics.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsProjectAcceptStatistics entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除施工图项目受理清单统计记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsProjectAcceptStatistics> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String executeAnalysis(Long id) {
        // 1. 获取统计记录详情
        BsProjectAcceptStatisticsVo statistics = queryById(id);
        if (statistics == null) {
            throw new ServiceException("统计记录不存在");
        }

        // 2. 根据日期范围查询BsProjectAccept数据
        LambdaQueryWrapper<BsProjectAccept> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.between(BsProjectAccept::getCreateTime, statistics.getStartDate(), statistics.getEndDate());
        List<BsProjectAccept> projectData = projectAcceptMapper.selectList(queryWrapper);

        // 3. 计算周和年度统计范围
        Date[] weekRange = getLastCompleteWeekRange(statistics.getStartDate(), statistics.getEndDate());
        Date[] yearRange = getLastYearRange(statistics.getStartDate(), statistics.getEndDate());

        // 4. 根据计算出的日期范围重新查询周数据
        LambdaQueryWrapper<BsProjectAccept> weekQueryWrapper = Wrappers.lambdaQuery();
        weekQueryWrapper.between(BsProjectAccept::getCreateTime, weekRange[0], weekRange[1]);
        List<BsProjectAccept> weekData = projectAcceptMapper.selectList(weekQueryWrapper);

        // 5. 根据计算出的日期范围重新查询年度数据
        LambdaQueryWrapper<BsProjectAccept> yearQueryWrapper = Wrappers.lambdaQuery();
        yearQueryWrapper.between(BsProjectAccept::getCreateTime, yearRange[0], yearRange[1]);
        List<BsProjectAccept> yearData = projectAcceptMapper.selectList(yearQueryWrapper);

        // 6. 生成Excel报表
        String fileName = generateStatisticsReport(statistics, projectData, weekData, yearData);

        // 7. 更新统计记录状态为完成
        BsProjectAcceptStatistics updateEntity = new BsProjectAcceptStatistics();
        updateEntity.setId(id);
        updateEntity.setStatus("1"); // 1-分析完成
        baseMapper.updateById(updateEntity);

        return fileName;
    }

    /**
     * 获取日期范围内最后一个完整周的起始和结束日期
     * @param startDate 查询起始日期
     * @param endDate 查询结束日期
     * @return 数组[周开始日期, 周结束日期]
     */
    private Date[] getLastCompleteWeekRange(Date startDate, Date endDate) {
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 找到结束日期所在周的周一
        LocalDate endWeekStart = end.with(WeekFields.of(DayOfWeek.MONDAY, 1).dayOfWeek(), 1);
        LocalDate endWeekEnd = endWeekStart.plusDays(6);

        // 如果这一周超出了查询范围，则取前一周
        if (endWeekEnd.isAfter(end)) {
            endWeekStart = endWeekStart.minusWeeks(1);
            endWeekEnd = endWeekStart.plusDays(6);
        }

        // 确保周开始日期不早于查询开始日期
        if (endWeekStart.isBefore(start)) {
            return new Date[]{startDate, endDate}; // 如果没有完整周，返回原始范围
        }

        return new Date[]{
            Date.from(endWeekStart.atStartOfDay(ZoneId.systemDefault()).toInstant()),
            Date.from(endWeekEnd.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
        };
    }

    /**
     * 获取日期范围内最后一个年份的起始和结束日期
     * @param startDate 查询起始日期
     * @param endDate 查询结束日期
     * @return 数组[年度开始日期, 年度结束日期]
     */
    private Date[] getLastYearRange(Date startDate, Date endDate) {
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int lastYear = end.getYear();

        LocalDate yearStart = LocalDate.of(lastYear, 1, 1);
        LocalDate yearEnd = end; // 使用查询结束日期作为年度结束日期

        return new Date[]{
            Date.from(yearStart.atStartOfDay(ZoneId.systemDefault()).toInstant()),
            Date.from(yearEnd.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
        };
    }

    /**
     * 生成统计报表
     */
    private String generateStatisticsReport(BsProjectAcceptStatisticsVo statistics,
                                            List<BsProjectAccept> projectData,
                                            List<BsProjectAccept> weekData,
                                            List<BsProjectAccept> yearData) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建样式
            Map<String, CellStyle> styles = createStyles(workbook);

            // 创建工作表
            Sheet sheet = workbook.createSheet("施工图审查业务工作量统计");

            // 创建标题
            createTitle(sheet, statistics, styles);

            // 创建表头
            createHeader(sheet, styles);

            // 统计数据并填充
            createDataRows(sheet, projectData, weekData, yearData, styles);

            // 设置列宽
            setColumnWidths(sheet);

            // 生成文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
            String timestamp = sdf.format(new Date());
            String fileName = String.format("施工图审查统计报表_%s.xlsx", timestamp);
            String filePath = "/Users/<USER>/Downloads/" + fileName;

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            }

            return fileName;
        } catch (IOException e) {
            throw new ServiceException("生成Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 创建样式
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 字体
        Font titleFont = wb.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);

        Font headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12); // 表头字体增大
        headerFont.setColor(IndexedColors.WHITE.getIndex());

        Font dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short) 11); // 主体字体增大

        Font boldFont = wb.createFont();
        boldFont.setBold(true);
        boldFont.setFontHeightInPoints((short) 11);

        // 标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.put("title", titleStyle);

        // 表头样式
        XSSFCellStyle headerStyle = (XSSFCellStyle) wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFont(headerFont);
        headerStyle.setWrapText(true); // 允许自动换行
        headerStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(68, 114, 196), null));
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorders(headerStyle);
        styles.put("header", headerStyle);

        // 数据样式
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setFont(dataFont);
        dataStyle.setWrapText(true); // 允许自动换行
        applyBorders(dataStyle);
        styles.put("data", dataStyle);

        // 百分比样式
        CellStyle percentStyle = wb.createCellStyle();
        percentStyle.setAlignment(HorizontalAlignment.CENTER);
        percentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        percentStyle.setFont(dataFont);
        percentStyle.setDataFormat(wb.createDataFormat().getFormat("0.00%"));
        applyBorders(percentStyle);
        styles.put("percent", percentStyle);

        // 合并单元格样式
        CellStyle mergedStyle = wb.createCellStyle();
        mergedStyle.setAlignment(HorizontalAlignment.CENTER);
        mergedStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        mergedStyle.setFont(boldFont);
        mergedStyle.setWrapText(true);
        applyBorders(mergedStyle);
        styles.put("merged", mergedStyle);

        return styles;
    }

    /**
     * 应用边框
     */
    private void applyBorders(CellStyle style) {
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * 创建标题
     */
    private void createTitle(Sheet sheet, BsProjectAcceptStatisticsVo statistics, Map<String, CellStyle> styles) {
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String startDate = sdf.format(statistics.getStartDate());
        String endDate = sdf.format(statistics.getEndDate());

        titleCell.setCellValue(String.format("%s-%s施工图审查业务工作量统计", startDate, endDate));
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
    }

    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, Map<String, CellStyle> styles) {
        // 创建第一行表头
        Row headerRow1 = sheet.createRow(1);
        headerRow1.setHeightInPoints(25);

        String[] headers1 = {
            "县区", "年度总面积", "同期总面积", "年度总项目个数", "周新增项目总个数", "周新增项目总面积",
            "图审机构", "周分配项目个数", "周新增项目面积", "周新增项目面积占比", "年度累计项目个数", "年度累计面积", "年度累计面积占比"
        };

        for (int i = 0; i < headers1.length; i++) {
            Cell cell = headerRow1.createCell(i);
            cell.setCellValue(headers1[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // 合并单元格 - 县区列跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        // 年度总面积跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
        // 同期总面积跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
        // 年度总项目个数跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
        // 周新增项目总个数跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4));
        // 周新增项目总面积跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5));
        // 图审机构跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 6, 6));
        // 周分配项目个数跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7));
        // 周新增项目面积跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 8, 8));
        // 周新增项目面积占比跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 9, 9));
        // 年度累计项目个数跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 10, 10));
        // 年度累计面积跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 11, 11));
        // 年度累计面积占比跨两行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 12, 12));

        // 创建第二行表头（空行，用于合并）
        Row headerRow2 = sheet.createRow(2);
        headerRow2.setHeightInPoints(25);
        for (int i = 0; i < headers1.length; i++) {
            Cell cell = headerRow2.createCell(i);
            cell.setCellValue("");
            cell.setCellStyle(styles.get("header"));
        }
    }

    /**
     * 创建数据行
     */
    private void createDataRows(Sheet sheet,
                              List<BsProjectAccept> projectData,
                              List<BsProjectAccept> weekData,
                              List<BsProjectAccept> yearData,
                              Map<String, CellStyle> styles) {
        // 按县区分组统计
        Map<String, List<BsProjectAccept>> districtGroups = projectData.stream()
            .collect(Collectors.groupingBy(p -> p.getDistrict() != null ? p.getDistrict() : "未知"));

        int rowNum = 3; // 从第3行开始，因为表头占了两行

        // 用于存储每个县区的起始行，以便后续合并单元格
        Map<String, Integer> districtStartRows = new HashMap<>();
        Map<String, Integer> districtRowCounts = new HashMap<>();

        // 计算总统计数据
        Map<String, BigDecimal> totalStats = calculateTotalStatistics(projectData, weekData, yearData);

        for (Map.Entry<String, List<BsProjectAccept>> entry : districtGroups.entrySet()) {
            String district = entry.getKey();
            List<BsProjectAccept> projects = entry.getValue();

            districtStartRows.put(district, rowNum);

            // 按图审机构分组
            Map<String, List<BsProjectAccept>> agencyGroups = projects.stream()
                .collect(Collectors.groupingBy(p -> p.getDrawingReviewAgency() != null ? p.getDrawingReviewAgency() : "未知"));

            // 计算县区级统计数据（使用年度和周数据）
            List<BsProjectAccept> districtWeekData = weekData.stream()
                .filter(p -> district.equals(p.getDistrict() != null ? p.getDistrict() : "未知"))
                .collect(Collectors.toList());
            List<BsProjectAccept> districtYearData = yearData.stream()
                .filter(p -> district.equals(p.getDistrict() != null ? p.getDistrict() : "未知"))
                .collect(Collectors.toList());
            Map<String, BigDecimal> districtStats = calculateDistrictStatistics(projects, districtWeekData, districtYearData);

            int agencyCount = agencyGroups.size();
            districtRowCounts.put(district, agencyCount);

            boolean isFirstAgency = true;
            for (Map.Entry<String, List<BsProjectAccept>> agencyEntry : agencyGroups.entrySet()) {
                String agency = agencyEntry.getKey();
                List<BsProjectAccept> agencyProjects = agencyEntry.getValue();

                // 计算机构级统计数据（使用年度和周数据）
                List<BsProjectAccept> agencyWeekData = districtWeekData.stream()
                    .filter(p -> agency.equals(p.getDrawingReviewAgency() != null ? p.getDrawingReviewAgency() : "未知"))
                    .collect(Collectors.toList());
                List<BsProjectAccept> agencyYearData = districtYearData.stream()
                    .filter(p -> agency.equals(p.getDrawingReviewAgency() != null ? p.getDrawingReviewAgency() : "未知"))
                    .collect(Collectors.toList());
                Map<String, BigDecimal> agencyStats = calculateAgencyStatistics(agencyProjects, projects, agencyWeekData, agencyYearData);

                Row row = sheet.createRow(rowNum++);
                row.setHeightInPoints(20);

                // 县区基础数据（只在第一行显示，后续合并）
                if (isFirstAgency) {
                    String districtDisplay = district + "\n（" + agencyCount + "家）";
                    row.createCell(0).setCellValue(districtDisplay);
                    row.createCell(1).setCellValue(districtStats.get("yearTotalArea").doubleValue());
                    row.createCell(2).setCellValue(districtStats.get("sameYearTotalArea").doubleValue());
                    row.createCell(3).setCellValue(districtStats.get("yearTotalProjects").doubleValue());
                    row.createCell(4).setCellValue(districtStats.get("weekNewProjects").doubleValue());
                    row.createCell(5).setCellValue(districtStats.get("weekNewArea").doubleValue());
                } else {
                    // 其他行留空，后续合并
                    for (int i = 0; i <= 5; i++) {
                        row.createCell(i).setCellValue("");
                    }
                }

                // 图审机构详细数据
                row.createCell(6).setCellValue(agency);
                row.createCell(7).setCellValue(agencyStats.get("weekAssignedProjects").doubleValue());
                row.createCell(8).setCellValue(agencyStats.get("weekNewArea").doubleValue());

                // 周新增项目面积占比
                Cell ratioCell = row.createCell(9);
                ratioCell.setCellValue(agencyStats.get("weekNewAreaRatio").doubleValue());
                ratioCell.setCellStyle(styles.get("percent"));

                row.createCell(10).setCellValue(agencyStats.get("yearCumulativeProjects").doubleValue());
                row.createCell(11).setCellValue(agencyStats.get("yearCumulativeArea").doubleValue());

                // 年度累计面积占比
                Cell ratioCell2 = row.createCell(12);
                ratioCell2.setCellValue(agencyStats.get("yearCumulativeAreaRatio").doubleValue());
                ratioCell2.setCellStyle(styles.get("percent"));

                // 应用数据样式
                for (int i = 0; i < 13; i++) {
                    if (i != 9 && i != 12) { // 跳过百分比列
                        Cell cell = row.getCell(i);
                        if (cell != null) {
                            cell.setCellStyle(styles.get("data"));
                        }
                    }
                }

                isFirstAgency = false;
            }
        }

        // 合并分组一的单元格
        mergeDistrictCells(sheet, districtStartRows, districtRowCounts);

        // 添加合计行
        addSummaryRows(sheet, rowNum, totalStats, styles);
    }

    /**
     * 计算县区级统计数据
     */
    private Map<String, BigDecimal> calculateDistrictStatistics(List<BsProjectAccept> projects,
                                                               List<BsProjectAccept> weekData,
                                                               List<BsProjectAccept> yearData) {
        Map<String, BigDecimal> stats = new HashMap<>();

        // 年度总面积（使用年度数据）
        BigDecimal yearTotalArea = yearData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 周新增面积（使用周数据）
        BigDecimal weekNewArea = weekData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.put("yearTotalArea", yearTotalArea);
        stats.put("sameYearTotalArea", BigDecimal.ZERO); // 需要历史数据对比
        stats.put("yearTotalProjects", BigDecimal.valueOf(yearData.size()));
        stats.put("weekNewProjects", BigDecimal.valueOf(weekData.size()));
        stats.put("weekNewArea", weekNewArea);

        return stats;
    }

    /**
     * 计算机构级统计数据
     */
    private Map<String, BigDecimal> calculateAgencyStatistics(List<BsProjectAccept> agencyProjects,
                                                             List<BsProjectAccept> districtProjects,
                                                             List<BsProjectAccept> agencyWeekData,
                                                             List<BsProjectAccept> agencyYearData) {
        Map<String, BigDecimal> stats = new HashMap<>();

        // 周新增面积（使用周数据）
        BigDecimal weekNewArea = agencyWeekData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 年度累计面积（使用年度数据）
        BigDecimal yearCumulativeArea = agencyYearData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 县区周总面积
        BigDecimal districtWeekTotalArea = districtProjects.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 县区年度总面积
        BigDecimal districtYearTotalArea = districtProjects.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算占比
        BigDecimal weekRatio = districtWeekTotalArea.compareTo(BigDecimal.ZERO) > 0
            ? weekNewArea.divide(districtWeekTotalArea, 4, RoundingMode.HALF_UP)
            : BigDecimal.ZERO;

        BigDecimal yearRatio = districtYearTotalArea.compareTo(BigDecimal.ZERO) > 0
            ? yearCumulativeArea.divide(districtYearTotalArea, 4, RoundingMode.HALF_UP)
            : BigDecimal.ZERO;

        stats.put("weekAssignedProjects", BigDecimal.valueOf(agencyWeekData.size()));
        stats.put("weekNewArea", weekNewArea);
        stats.put("weekNewAreaRatio", weekRatio);
        stats.put("yearCumulativeProjects", BigDecimal.valueOf(agencyYearData.size()));
        stats.put("yearCumulativeArea", yearCumulativeArea);
        stats.put("yearCumulativeAreaRatio", yearRatio);

        return stats;
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 18 * 256); // 县区
        sheet.setColumnWidth(1, 14 * 256); // 年度总面积
        sheet.setColumnWidth(2, 14 * 256); // 同期总面积
        sheet.setColumnWidth(3, 16 * 256); // 年度总项目个数
        sheet.setColumnWidth(4, 16 * 256); // 周新增项目总个数
        sheet.setColumnWidth(5, 16 * 256); // 周新增项目总面积
        sheet.setColumnWidth(6, 12 * 256); // 图审机构
        sheet.setColumnWidth(7, 16 * 256); // 周分配项目个数
        sheet.setColumnWidth(8, 16 * 256); // 周新增项目面积
        sheet.setColumnWidth(9, 18 * 256); // 周新增项目面积占比
        sheet.setColumnWidth(10, 16 * 256); // 年度累计项目个数
        sheet.setColumnWidth(11, 16 * 256); // 年度累计面积
        sheet.setColumnWidth(12, 18 * 256); // 年度累计面积占比
    }

    /**
     * 计算总统计数据
     */
    private Map<String, BigDecimal> calculateTotalStatistics(List<BsProjectAccept> projectData,
                                                           List<BsProjectAccept> weekData,
                                                           List<BsProjectAccept> yearData) {
        Map<String, BigDecimal> stats = new HashMap<>();

        // 年度总面积
        BigDecimal yearTotalArea = yearData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 周新增面积
        BigDecimal weekNewArea = weekData.stream()
            .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.put("yearTotalArea", yearTotalArea);
        stats.put("sameYearTotalArea", BigDecimal.ZERO); // 需要历史数据对比
        stats.put("yearTotalProjects", BigDecimal.valueOf(yearData.size()));
        stats.put("weekNewProjects", BigDecimal.valueOf(weekData.size()));
        stats.put("weekNewArea", weekNewArea);

        return stats;
    }

    /**
     * 合并县区相关的单元格
     */
    private void mergeDistrictCells(Sheet sheet, Map<String, Integer> districtStartRows, Map<String, Integer> districtRowCounts) {
        for (Map.Entry<String, Integer> entry : districtStartRows.entrySet()) {
            int startRow = entry.getValue();
            int rowCount = districtRowCounts.get(entry.getKey());

            // 只有当行数大于1时才进行合并，避免合并单个单元格
            if (rowCount > 1) {
                int endRow = startRow + rowCount - 1;
                // 确保startRow != endRow，避免合并单个单元格
                if (startRow != endRow) {
                    // 合并分组一的列（县区到周新增项目总面积）
                    for (int col = 0; col <= 5; col++) {
                        sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, col, col));
                    }
                }
            }
        }
    }

    /**
     * 添加合计行
     */
    private void addSummaryRows(Sheet sheet, int startRow, Map<String, BigDecimal> totalStats, Map<String, CellStyle> styles) {
        // 合计行
        Row summaryRow = sheet.createRow(startRow);
        summaryRow.setHeightInPoints(25);

        // 合计标题
        summaryRow.createCell(0).setCellValue("合计");

        // 合计数据
        summaryRow.createCell(1).setCellValue(totalStats.get("yearTotalArea").doubleValue()); // 年度总面积
        summaryRow.createCell(2).setCellValue(totalStats.get("sameYearTotalArea").doubleValue()); // 同期总面积
        summaryRow.createCell(3).setCellValue(totalStats.get("yearTotalProjects").doubleValue()); // 年度总项目个数
        summaryRow.createCell(4).setCellValue(totalStats.get("weekNewProjects").doubleValue()); // 周新增项目总个数
        summaryRow.createCell(5).setCellValue(totalStats.get("weekNewArea").doubleValue()); // 周新增项目总面积

        // 右侧留空或填入汇总数据
        for (int i = 6; i <= 12; i++) {
            summaryRow.createCell(i).setCellValue("");
        }

        // 应用样式
        for (int i = 0; i <= 12; i++) {
            Cell cell = summaryRow.getCell(i);
            if (cell != null) {
                cell.setCellStyle(styles.get("merged"));
            }
        }

        // 添加各单位年度累计面积及占比统计
        addYearlyAccumulationStats(sheet, startRow + 2, styles);
    }


    /**
     * 添加各单位年度累计面积及占比统计
     */
    private void addYearlyAccumulationStats(Sheet sheet, int startRow, Map<String, CellStyle> styles) {
        // 第一行：标题行
        Row titleRow = sheet.createRow(startRow);
        titleRow.setHeightInPoints(25);
        titleRow.createCell(0).setCellValue("各单位年度累计面积及占比");

        // 机构标题
        titleRow.createCell(1).setCellValue("A");
        titleRow.createCell(3).setCellValue("B");
        titleRow.createCell(5).setCellValue("C");
        titleRow.createCell(7).setCellValue("D");

        // 合并机构标题列
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 1, 2));  // A机构2列
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 3, 4));  // B机构2列
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 5, 6));  // C机构2列
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 7, 8));  // D机构2列

        // 第二行：子标题行
        Row subTitleRow = sheet.createRow(startRow + 1);
        subTitleRow.setHeightInPoints(20);
        subTitleRow.createCell(0).setCellValue("");
        subTitleRow.createCell(1).setCellValue("本年度累计面积");
        subTitleRow.createCell(2).setCellValue("占比");
        subTitleRow.createCell(3).setCellValue("同期累计面积");
        subTitleRow.createCell(4).setCellValue("占比");
        subTitleRow.createCell(5).setCellValue("本年度累计面积");
        subTitleRow.createCell(6).setCellValue("占比");
        subTitleRow.createCell(7).setCellValue("本年度累计面积");
        subTitleRow.createCell(8).setCellValue("占比");

        // 第三行：数据行
        Row dataRow = sheet.createRow(startRow + 2);
        dataRow.setHeightInPoints(20);
        dataRow.createCell(0).setCellValue("");

        // 示例数据（根据设计图）
        dataRow.createCell(1).setCellValue(283.86);   // A机构累计面积
        dataRow.createCell(2).setCellValue(0.4295);   // A机构累计占比（42.95%）
        dataRow.createCell(3).setCellValue(388.44);   // A机构同期累计面积
        dataRow.createCell(4).setCellValue(0.5198);   // A机构同期累计占比（51.98%）
        dataRow.createCell(5).setCellValue(193.65);   // B机构累计面积
        dataRow.createCell(6).setCellValue(0.2930);   // B机构累计占比（29.30%）
        dataRow.createCell(7).setCellValue(112.21);   // C机构累计面积
        dataRow.createCell(8).setCellValue(0.1698);   // C机构累计占比（16.98%）

        // 应用样式
        for (int row = startRow; row <= startRow + 2; row++) {
            Row currentRow = sheet.getRow(row);
            if (currentRow != null) {
                for (int col = 0; col <= 8; col++) {
                    Cell cell = currentRow.getCell(col);
                    if (cell != null) {
                        if (col == 2 || col == 4 || col == 6 || col == 8) {
                            // 百分比列
                            cell.setCellStyle(styles.get("percent"));
                        } else {
                            cell.setCellStyle(styles.get("merged"));
                        }
                    }
                }
            }
        }
    }

}
