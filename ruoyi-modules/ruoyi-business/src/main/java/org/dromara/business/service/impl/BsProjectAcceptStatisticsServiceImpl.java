package org.dromara.business.service.impl;

import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.BsProjectAcceptStatisticsBo;
import org.dromara.business.domain.vo.BsProjectAcceptStatisticsVo;
import org.dromara.business.domain.BsProjectAcceptStatistics;
import org.dromara.business.domain.BsProjectAccept;
import org.dromara.business.mapper.BsProjectAcceptStatisticsMapper;
import org.dromara.business.mapper.BsProjectAcceptMapper;
import org.dromara.business.service.IBsProjectAcceptStatisticsService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 施工图项目受理清单统计记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class BsProjectAcceptStatisticsServiceImpl implements IBsProjectAcceptStatisticsService {

    private final BsProjectAcceptStatisticsMapper baseMapper;
    private final BsProjectAcceptMapper projectAcceptMapper;

    /**
     * 查询施工图项目受理清单统计记录
     *
     * @param id 主键
     * @return 施工图项目受理清单统计记录
     */
    @Override
    public BsProjectAcceptStatisticsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询施工图项目受理清单统计记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 施工图项目受理清单统计记录分页列表
     */
    @Override
    public TableDataInfo<BsProjectAcceptStatisticsVo> queryPageList(BsProjectAcceptStatisticsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = buildQueryWrapper(bo);
        Page<BsProjectAcceptStatisticsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的施工图项目受理清单统计记录列表
     *
     * @param bo 查询条件
     * @return 施工图项目受理清单统计记录列表
     */
    @Override
    public List<BsProjectAcceptStatisticsVo> queryList(BsProjectAcceptStatisticsBo bo) {
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsProjectAcceptStatistics> buildQueryWrapper(BsProjectAcceptStatisticsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsProjectAcceptStatistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStartDate() != null, BsProjectAcceptStatistics::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, BsProjectAcceptStatistics::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BsProjectAcceptStatistics::getStatus, bo.getStatus());
        lqw.eq(bo.getOssId() != null, BsProjectAcceptStatistics::getOssId, bo.getOssId());
        return lqw;
    }

    /**
     * 新增施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsProjectAcceptStatisticsBo bo) {
        BsProjectAcceptStatistics add = MapstructUtils.convert(bo, BsProjectAcceptStatistics.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsProjectAcceptStatisticsBo bo) {
        BsProjectAcceptStatistics update = MapstructUtils.convert(bo, BsProjectAcceptStatistics.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsProjectAcceptStatistics entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除施工图项目受理清单统计记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsProjectAcceptStatistics> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String executeAnalysis(Long id) {
        // 1. 获取统计记录详情
        BsProjectAcceptStatisticsVo statistics = queryById(id);
        if (statistics == null) {
            throw new ServiceException("统计记录不存在");
        }

        // 2. 根据日期范围查询BsProjectAccept数据
        LambdaQueryWrapper<BsProjectAccept> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.between(BsProjectAccept::getCreateTime, statistics.getStartDate(), statistics.getEndDate());
        List<BsProjectAccept> projectData = projectAcceptMapper.selectList(queryWrapper);

        // 3. 生成Excel报表
        String fileName = generateStatisticsReport(statistics, projectData);

        // 4. 更新统计记录状态为完成
        BsProjectAcceptStatistics updateEntity = new BsProjectAcceptStatistics();
        updateEntity.setId(id);
        updateEntity.setStatus("1"); // 1-分析完成
        baseMapper.updateById(updateEntity);

        return fileName;
    }

    /**
     * 生成统计报表
     */
    private String generateStatisticsReport(BsProjectAcceptStatisticsVo statistics, List<BsProjectAccept> projectData) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建样式
            Map<String, CellStyle> styles = createStyles(workbook);

            // 创建工作表
            Sheet sheet = workbook.createSheet("施工图审查业务工作量统计");
            
            // 创建标题
            createTitle(sheet, statistics, styles);
            
            // 创建表头
            createHeader(sheet, styles);
            
            // 统计数据并填充
            createDataRows(sheet, projectData, styles);
            
            // 设置列宽
            setColumnWidths(sheet);

            // 生成文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
            String timestamp = sdf.format(new Date());
            String fileName = String.format("施工图审查统计报表_%s.xlsx", timestamp);
            String filePath = "/Users/<USER>/Downloads/" + fileName;

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            }

            return fileName;
        } catch (IOException e) {
            throw new ServiceException("生成Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 创建样式
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 字体
        Font titleFont = wb.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);

        Font headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());

        Font boldFont = wb.createFont();
        boldFont.setBold(true);

        // 标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.put("title", titleStyle);

        // 表头样式
        XSSFCellStyle headerStyle = (XSSFCellStyle) wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(68, 114, 196), null));
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorders(headerStyle);
        styles.put("header", headerStyle);

        // 数据样式
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        applyBorders(dataStyle);
        styles.put("data", dataStyle);

        // 百分比样式
        CellStyle percentStyle = wb.createCellStyle();
        percentStyle.setAlignment(HorizontalAlignment.CENTER);
        percentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        percentStyle.setDataFormat(wb.createDataFormat().getFormat("0.00%"));
        applyBorders(percentStyle);
        styles.put("percent", percentStyle);

        return styles;
    }

    /**
     * 应用边框
     */
    private void applyBorders(CellStyle style) {
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * 创建标题
     */
    private void createTitle(Sheet sheet, BsProjectAcceptStatisticsVo statistics, Map<String, CellStyle> styles) {
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String startDate = sdf.format(statistics.getStartDate());
        String endDate = sdf.format(statistics.getEndDate());
        
        titleCell.setCellValue(String.format("%s-%s施工图审查业务工作量统计", startDate, endDate));
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
    }

    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, Map<String, CellStyle> styles) {
        String[] headers = {
            "县区", "年度总面积", "同期总面积", "年度总项目个数", "周新增项目总个数", 
            "周新增项目总面积", "图审机构", "周分配项目个数", "周新增项目面积", 
            "周新增项目面积占比", "年度累计项目数量", "年度累计面积", "年度累计面积占比"
        };

        Row headerRow = sheet.createRow(1);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }
    }

    /**
     * 创建数据行
     */
    private void createDataRows(Sheet sheet, List<BsProjectAccept> projectData, Map<String, CellStyle> styles) {
        // 按县区分组统计
        Map<String, List<BsProjectAccept>> districtGroups = projectData.stream()
            .collect(Collectors.groupingBy(p -> p.getDistrict() != null ? p.getDistrict() : "未知"));

        int rowNum = 2;
        for (Map.Entry<String, List<BsProjectAccept>> entry : districtGroups.entrySet()) {
            String district = entry.getKey();
            List<BsProjectAccept> projects = entry.getValue();

            // 统计数据
            long projectCount = projects.size();
            BigDecimal totalArea = projects.stream()
                .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 按图审机构分组
            Map<String, List<BsProjectAccept>> agencyGroups = projects.stream()
                .collect(Collectors.groupingBy(p -> p.getDrawingReviewAgency() != null ? p.getDrawingReviewAgency() : "未知"));

            for (Map.Entry<String, List<BsProjectAccept>> agencyEntry : agencyGroups.entrySet()) {
                String agency = agencyEntry.getKey();
                List<BsProjectAccept> agencyProjects = agencyEntry.getValue();
                
                long agencyProjectCount = agencyProjects.size();
                BigDecimal agencyTotalArea = agencyProjects.stream()
                    .map(p -> p.getConstructionAreaSqm() != null ? p.getConstructionAreaSqm() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                Row row = sheet.createRow(rowNum++);
                
                // 填充数据
                row.createCell(0).setCellValue(district);
                row.createCell(1).setCellValue(totalArea.doubleValue());
                row.createCell(2).setCellValue(0); // 同期总面积 - 需要历史数据对比
                row.createCell(3).setCellValue(projectCount);
                row.createCell(4).setCellValue(agencyProjectCount);
                row.createCell(5).setCellValue(agencyTotalArea.doubleValue());
                row.createCell(6).setCellValue(agency);
                row.createCell(7).setCellValue(agencyProjectCount);
                row.createCell(8).setCellValue(agencyTotalArea.doubleValue());
                
                // 计算占比
                double ratio = totalArea.doubleValue() > 0 ? agencyTotalArea.doubleValue() / totalArea.doubleValue() : 0;
                Cell ratioCell = row.createCell(9);
                ratioCell.setCellValue(ratio);
                ratioCell.setCellStyle(styles.get("percent"));
                
                row.createCell(10).setCellValue(agencyProjectCount);
                row.createCell(11).setCellValue(agencyTotalArea.doubleValue());
                
                Cell ratioCell2 = row.createCell(12);
                ratioCell2.setCellValue(ratio);
                ratioCell2.setCellStyle(styles.get("percent"));

                // 应用数据样式
                for (int i = 0; i < 13; i++) {
                    if (i != 9 && i != 12) { // 百分比列已设置样式
                        Cell cell = row.getCell(i);
                        if (cell != null) {
                            cell.setCellStyle(styles.get("data"));
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 16 * 256); // 县区
        for (int i = 1; i <= 12; i++) {
            sheet.setColumnWidth(i, 12 * 256);
        }
    }
}
