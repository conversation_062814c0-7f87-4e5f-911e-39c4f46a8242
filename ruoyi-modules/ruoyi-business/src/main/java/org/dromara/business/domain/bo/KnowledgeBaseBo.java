package org.dromara.business.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import java.util.List;

/**
 * 知识库业务对象
 */
@Data
@AutoMapper(target = KnowledgeBase.class)
public class KnowledgeBaseBo extends BaseEntity {

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 50, message = "知识库名字超过最大长度，请重新输入", groups = { AddGroup.class, EditGroup.class })
    private String knowledgeName;

    /**
     * 知识库类型
     */
    private String knowledgeType;

    /**
     * 知识库描述
     */
    @Size(max = 500, message = "知识库描述超过最大长度，请重新输入",groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 数据类型（0结构化数据 1非结构化数据）
     */
    private String dataType;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 用户ID列表（用于创建知识库时分配用户）
     */
    private List<Long> userIds;

    /**
     * 默认权限类型（用于创建知识库时分配给用户的默认权限）
     */
    private String defaultPermissionType;

    /**
     * 部门id
     */
    private Long deptId;


    /**
     * 部门关系
     */
    private String deptCategory;

    /**
     * 知识库学习方式(1-文件上传模式，2-数据库直连模式)
     */
    private Integer learningStyle;

    /**
     * 创建类型
     */
    private String buildType;

    /**
     * 排序字段（值越小越靠前）
     */
    private Integer sortOrder;

    /**
     * 分类id
     */
    private Long categoryId;
}
