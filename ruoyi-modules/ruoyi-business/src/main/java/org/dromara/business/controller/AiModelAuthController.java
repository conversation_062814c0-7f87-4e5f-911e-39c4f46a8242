package org.dromara.business.controller;

import java.util.List;
import java.util.Map;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.business.domain.bo.AiModelHistoryMsgBo;
import org.dromara.business.vo.AiModelAuthCountVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.AiModelAuthVo;
import org.dromara.business.domain.bo.AiModelAuthBo;
import org.dromara.business.service.IAiModelAuthService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 模型授权
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/modelAuth")
public class AiModelAuthController extends BaseController {

    private final IAiModelAuthService aiModelAuthService;

    /**
     * 查询模型授权列表
     */
    @SaCheckPermission("business:modelAuth:list")
    @GetMapping("/list")
    public TableDataInfo<AiModelAuthVo> list(AiModelAuthBo bo, PageQuery pageQuery) {
        return aiModelAuthService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出模型授权列表
     */
    @SaCheckPermission("business:modelAuth:export")
    @Log(title = "模型授权", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiModelAuthBo bo, HttpServletResponse response) {
        List<AiModelAuthVo> list = aiModelAuthService.queryList(bo);
        ExcelUtil.exportExcel(list, "模型授权", AiModelAuthVo.class, response);
    }

    /**
     * 获取模型授权详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:modelAuth:query")
    @GetMapping("/{id}")
    public R<AiModelAuthVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(aiModelAuthService.queryById(id));
    }

    /**
     * 新增模型授权
     */
    @SaCheckPermission("business:modelAuth:add")
    @Log(title = "模型授权", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiModelAuthBo bo) {
        return toAjax(aiModelAuthService.insertByBo(bo));
    }

    /**
     * 修改模型授权
     */
    @SaCheckPermission("business:modelAuth:edit")
    @Log(title = "模型授权", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiModelAuthBo bo) {
        return toAjax(aiModelAuthService.updateByBo(bo));
    }

    /**
     * 删除模型授权
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:modelAuth:remove")
    @Log(title = "模型授权", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(aiModelAuthService.deleteWithValidByIds(List.of(ids), true));
    }


    @Log(title = "key绑定用户授权", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/bindUser")
    public R<Void> bindUser(@RequestBody AiModelAuthBo bo) {
        return toAjax(aiModelAuthService.bindUser(bo));
    }


    /**
     * 根据key查询模型信息
     * @param modelKey
     * @return
     */
    @SaIgnore
    @GetMapping("/getInfoByKey")
    public R<Map<String,String>> getInfoByKey(@RequestParam(value = "modelKey") String modelKey) {
        return R.ok(aiModelAuthService.getInfoByKey(modelKey));
    }

    /**
     * 查询租户信息
     */
    @GetMapping("/getUserByKey")
    @SaIgnore
    public R<Map<String,String>> getUserByKey() {
        return R.ok(aiModelAuthService.getUserByKey());
    }


    /**
     * 获取各个授权key的调用次数(月)
     */
    @GetMapping("/getAuthModelInfoMonth")
    public R<List<AiModelAuthCountVO>> getAuthModelInfoMonth() {
        return R.ok(aiModelAuthService.getAuthModelInfo());
    }


    /**
     * 获取各个授权key的调用次数(所有)
     */
    @GetMapping("/getAuthModelInfoAll")
    public R<List<AiModelAuthCountVO>> getAuthModelInfoAll() {
        return R.ok(aiModelAuthService.getAuthModelInfoAll());
    }


}
