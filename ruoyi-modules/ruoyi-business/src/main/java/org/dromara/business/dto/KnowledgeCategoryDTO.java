package org.dromara.business.dto;

import lombok.Data;
import org.dromara.common.mybatis.core.page.PageQuery;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 知识库分类 DTO
 */
@Data
public class KnowledgeCategoryDTO extends PageQuery {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String name;

    /**
     * 分类描述
     */
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门ID列表（用于查询多个部门的分类）
     */
    private List<Long> deptIds;
}
