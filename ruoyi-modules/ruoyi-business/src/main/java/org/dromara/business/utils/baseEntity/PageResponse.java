package org.dromara.business.utils.baseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class PageResponse extends PageBase {

    private static final long serialVersionUID = -7378163356547441491L;

    private Long total;

    public PageResponse(long pageIndex, long pageSize, long total) {
        super.setPageIndex(pageIndex);
        super.setPageSize(pageSize);
        this.total = total;
    }

}
