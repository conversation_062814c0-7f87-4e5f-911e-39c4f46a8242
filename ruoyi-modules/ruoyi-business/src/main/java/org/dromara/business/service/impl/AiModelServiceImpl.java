package org.dromara.business.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.domain.bo.AiModelHistoryMsgBo;
import org.dromara.business.domain.vo.AiModelHistoryMsgVo;
import org.dromara.business.mapper.AiChatMessageMapper;
import org.dromara.common.core.constant.AIModelTypeConstants;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.sse.dto.SseMessageDto;
import org.dromara.common.sse.utils.SseMessageUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.service.impl.SysDeptServiceImpl;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.AiModelBo;
import org.dromara.business.domain.vo.AiModelVo;
import org.dromara.business.domain.AiModel;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.business.service.IAiModelService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.*;

/**
 * 大模型配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AiModelServiceImpl implements IAiModelService {

    private final AiModelMapper baseMapper;
    private final AiModelChildServiceImpl aiModelChildService;
    private final ScheduledExecutorService scheduledExecutorService;
    private final AiChatMessageMapper aiChatMessageMapper;
    private final SysDeptServiceImpl sysDeptService;


    /**
     * 查询大模型配置
     *
     * @param id 主键
     * @return 大模型配置
     */
    @Override
    public AiModelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询大模型配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 大模型配置分页列表
     */
    @Override
    public TableDataInfo<AiModelVo> queryPageList(AiModelBo bo, PageQuery pageQuery) {
        Page<AiModelVo> result = baseMapper.queryPageList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的大模型配置列表
     *
     * @param bo 查询条件
     * @return 大模型配置列表
     */
    @Override
    public List<AiModelVo> queryList(AiModelBo bo) {
        LambdaQueryWrapper<AiModel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiModel> buildQueryWrapper(AiModelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiModel> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getModelCode()), AiModel::getModelCode, bo.getModelCode());
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), AiModel::getModelName, bo.getModelName());
        lqw.eq(bo.getKnowledgeId() != null, AiModel::getKnowledgeId, bo.getKnowledgeId());
        lqw.eq(bo.getTemperature() != null, AiModel::getTemperature, bo.getTemperature());
        lqw.eq(bo.getMaxTokens() != null, AiModel::getMaxTokens, bo.getMaxTokens());
        lqw.eq(bo.getMaxContexts() != null, AiModel::getMaxContexts, bo.getMaxContexts());
        lqw.eq(bo.getStatus() != null, AiModel::getStatus, bo.getStatus());
        lqw.orderByAsc(AiModel::getCreateTime);
        return lqw;
    }

    /**
     * 新增大模型配置
     *
     * @param bo 大模型配置
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Boolean insertByBo(AiModelBo bo) {
        //获取当前登录人
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("未获取到登录人信息");
        }
        AiModel add = MapstructUtils.convert(bo, AiModel.class);
        if(null == add){
            throw new ServiceException("新增内容不能为空");
        }
        validEntityBeforeSave(add);
        add.setCreateDept(loginUser.getDeptId());
        add.setTenantId(loginUser.getTenantId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改大模型配置
     *
     * @param bo 大模型配置
     * @return 是否修改成功
     */
    @Override
    @CacheEvict(cacheNames = "global:ai_model_chat_info:#10m", key = "#bo.id")
    public Boolean updateByBo(AiModelBo bo) {
        AiModel update = MapstructUtils.convert(bo, AiModel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiModel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除大模型配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<AiModel> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 修改大模型配置
     * @param bo
     * @return
     */
    @Override
    @Transactional
    @CacheEvict(value ={ "global:ai_model_chat_info:#10m",
                        "global:ai_model_vision#10m",
                        "global:ai_model_coder#10m"},
                        allEntries = true)
    public void changeModel(AiModelBo bo) {
        AiModel chooseModle = baseMapper.selectById(bo.getId());
        if(null == chooseModle){
            throw new ServiceException("模型不存在，请重试");
        }
        if (bo.getStatus() == 0) {
            // 关闭当前模型
            baseMapper.changeModel(bo.getId(), 0);
        } else if (bo.getStatus() == 1) {
            // 开启模型
            baseMapper.changeModel(bo.getId(), 1);
        } else {
            throw new IllegalArgumentException("无效的状态值: " + bo.getStatus());
        }
    }

    /**
     * 获取模型信息
     * @return
     */
    @Override
    @Scheduled(cron = "0/30 * * * * ?")
    public Map<String,Object> getModelInfo() {
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> returnList = new ArrayList<>();
        // 查询模型库所有模型
        LambdaQueryWrapper<AiModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiModel::getDelFlag, 0);
        queryWrapper.eq(AiModel::getStatus, 1);
        queryWrapper.orderByDesc(AiModel::getCreateTime);
        List<AiModel> aiModelList = baseMapper.selectList(queryWrapper);
        //查询所有授权的key,且在有效期内
        LambdaQueryWrapper<AiModelAuth> queryParams = new LambdaQueryWrapper<>();
        queryParams.eq(AiModelAuth::getDelFlag, 0);
        queryParams.gt(AiModelAuth::getExpirationDate, new Date());
        if (aiModelList != null && !aiModelList.isEmpty()) {
            for (AiModel aiModel : aiModelList) {
                Map<String, Object> modelInfo = new HashMap<>();
                modelInfo.put("modelName", aiModel.getModelName());
                modelInfo.put("modelLogoUrl", aiModel.getModelLogoUrl());
                //根据模型拼接去查询所有模型地址
                List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelId(aiModel.getId());
                // 模型通道数量
                modelInfo.put("modelThor", aiModelChildList.size());
                // 在线客户端
                modelInfo.put("onlineClint", 0);
                long onlineClint = 0L;
                // 各通道情况
                List<Map<String, Object>> urlInfoList = new ArrayList<>();
                for (AiModelChild aiModelChild : aiModelChildList) {
                    Map<String, Object> urlInfo = new HashMap<>();
                    urlInfo.put("url", aiModelChild.getModelUrl());
                    //统计消息记录中该通道下的客户端id数量
                    long onlineNum = aiChatMessageMapper.getConversationBs(aiModelChild.getModelUrl(),aiModelChild.getModelId());
                    urlInfo.put("onlineNum", onlineNum);
                    urlInfoList.add(urlInfo);
                    onlineClint = onlineClint + onlineNum;
                }
                modelInfo.put("onlineClint", onlineClint);
                modelInfo.put("modelInfoList", urlInfoList);
                returnList.add(modelInfo);
            }
        }
        //排序
        returnList.sort((map1, map2) ->
            Long.compare((Long) map2.get("onlineClint"), (Long) map1.get("onlineClint"))
        );

        //查询所有不同的客户端在十分钟之内的会话数量
        Long conversation = aiChatMessageMapper.getConversationBs(null,null);

        //查询今日访问总量（按人分组查询user_id数量）
        Long allVisit = aiChatMessageMapper.getAllVisit();
        //查询月访问总量（按人分组查询user_id数量）
        Long allMonth = aiChatMessageMapper.getAllMonth();
        //查询今日会话次数（查询消息表中type为“user” 的数量）
        Long todayMsg = aiChatMessageMapper.getTodayMsg();
        //查询当月会话次数（查询消息表中type为“user” 的数量）
        Long monthMsg = aiChatMessageMapper.getMonthMsg();
        //查询今日消息峰值
        Map<String,Long> todayMsgPeak = aiChatMessageMapper.getTodayMsgPeak();
        //查询当月消息峰值
        Map<String,Long> monthMsgPeak = aiChatMessageMapper.getMonthMsgPeak();
        //查询今日人数峰值
        Map<String,Long> todayUserPeak = aiChatMessageMapper.getTodayUserPeak();
        //查询当月人数峰值
        Map<String,Long> monthUserPeak = aiChatMessageMapper.getMonthUserPeak();

        //获取图标折线图信息
        Map<String, Object> tokenInfo = getTokensCount();

        resultMap.put("tokenInfo", tokenInfo);
        resultMap.put("modelInfo", returnList);
        resultMap.put("onlineUser", conversation);
        resultMap.put("todayVisit", allVisit);
        resultMap.put("monthVisit", allMonth);
        resultMap.put("todayMsg", todayMsg);
        resultMap.put("monthMsg", monthMsg);
        resultMap.put("todayMsgPeak", todayMsgPeak);
        resultMap.put("monthMsgPeak", monthMsgPeak);
        resultMap.put("todayUserPeak", todayUserPeak);
        resultMap.put("monthUserPeak", monthUserPeak);

        //发送SSE请求，在message外部在封装一层
        Map<String, Object> message = new HashMap<>();
        message.put("messageType", 2);
        message.put("message", resultMap);
        scheduledExecutorService.schedule(() -> {
            SseMessageDto dto = new SseMessageDto();
            dto.setMessage(JSON.toJSONString(message));
            dto.setUserIds(List.of(1L));
            SseMessageUtils.publishMessage(dto);
        }, 0, TimeUnit.SECONDS);
        return resultMap;
    }


    /**
     * 查询模型信息资源图标信息
     * @return
     */
    @Override
    public Map<String, Object> getTokensCount() {
        //分两个部分，1：模型近七天tokens。2：模型近十二小时tokens
        Map<String, Object> returnList = new HashMap<>();
        aiChatMessageMapper.setGroupConcatMaxLen();
        returnList.put("sevenTokens",aiChatMessageMapper.getSevenTokensByModel());
        returnList.put("twelveTokens",getTwelveTokens());
        return returnList;
    }

    /**
     * 获取近十二小时Tokens统计（按模型分配）
     * @return
     */
    private List<Map<String,Object>> getTwelveTokens() {
        // 查询模型库所有模型
        LambdaQueryWrapper<AiModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiModel::getDelFlag, 0);
        queryWrapper.eq(AiModel::getStatus, 1);
        queryWrapper.orderByDesc(AiModel::getCreateTime);
        List<AiModel> aiModelList = baseMapper.selectList(queryWrapper);
        List<Map<String,Object>> modelTokens = new ArrayList<>();
        for (AiModel aiModel : aiModelList) {
            Map<String,Object> modelTokenMap = new HashMap<>();
            //获取近十二个小时的token数量
            List<String> modelToken = TenantHelper.ignore(() -> aiChatMessageMapper.getTwelveTokens(aiModel.getId()));
            modelTokenMap.put("modelName",aiModel.getModelName());
            modelTokenMap.put("modelTokens", modelToken);
            modelTokens.add(modelTokenMap);
        }
        return modelTokens;
    }


    /**
     * 重置redis模型分配通道
     */
    @Override
    @CacheEvict(value = {
        "global:ai_model_chat_info:#10m",
        "global:ai_model_vision#10m",
        "global:ai_model_chat_auth:#10m",
        "global:ai_chat_conver_info:#10m",
        "global:ai_chat_model_info_key:#10m",
        "global:ai_chat_modle#10m"
    }, allEntries = true)
    public void resetChannel() {
        RedisUtils.deleteKeys(CacheConstants.AI_MODEL_KEY + "*");
        log.info(new Date() + "操作清空全部AI相关缓存");
    }


    @Override
    @Cacheable(cacheNames = "global:ai_model_chat_info:#10m", key = "#modelId", condition = "#modelId != null")
    public AiModel getModelInfoInChat(Long modelId) {
        return baseMapper.selectById(modelId);
    }


    /**
     * 获取视觉模型列表
     * @return
     */
    @Override
    @Cacheable(cacheNames = "global:ai_model_vision#10m")
    public List<AiModel> getIdentifyModel() {
        LambdaQueryWrapper<AiModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiModel::getDelFlag, 0);
        queryWrapper.eq(AiModel::getStatus, 1);
        queryWrapper.eq(AiModel::getModelType, AIModelTypeConstants.visual_model);
        return baseMapper.selectList(queryWrapper);
    }


    /**
     * 获取SQL模型列表
     * @return
     */
    @Override
    @Cacheable(cacheNames = "global:ai_model_coder#10m")
    public List<AiModel> getCoderModel() {
        LambdaQueryWrapper<AiModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiModel::getDelFlag, 0);
        queryWrapper.eq(AiModel::getStatus, 1);
        queryWrapper.eq(AiModel::getModelType, AIModelTypeConstants.coder_model);
        return baseMapper.selectList(queryWrapper);
    }


    /**
     * 获取模型历史信息统计消息
     * @return
     */
    @Override
    public Map<String,Object> getModelHistoryInfo(AiModelHistoryMsgBo aiModelHistoryMsgBo) {
        Map<String,Object> result = new HashMap<>();
        //日统计
        List<AiModelHistoryMsgVo> dayCountInfo = aiChatMessageMapper.getDayCountInfo(aiModelHistoryMsgBo);
        //月统计
        List<AiModelHistoryMsgVo> monthCountInfo = aiChatMessageMapper.getMonthCountInfo(aiModelHistoryMsgBo);
        result.put("dayCountInfo",dayCountInfo);
        result.put("monthCountInfo",monthCountInfo);
        return result;
    }


}
