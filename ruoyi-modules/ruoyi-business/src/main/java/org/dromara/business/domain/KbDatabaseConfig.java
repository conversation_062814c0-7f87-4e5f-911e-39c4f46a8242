package org.dromara.business.domain;

import jakarta.validation.constraints.NotEmpty;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 知识库-数据库配置信息对象 kb_database_config
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("kb_database_config")
public class KbDatabaseConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据库配置id
     */
    @TableId(value = "database_config_id")
    private Long databaseConfigId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 数据库地址
     */
    private String databaseUrl;

    /**
     * 端口
     */
    private String databasePort;

    /**
     * 库名
     */
    private String databaseName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;


}
