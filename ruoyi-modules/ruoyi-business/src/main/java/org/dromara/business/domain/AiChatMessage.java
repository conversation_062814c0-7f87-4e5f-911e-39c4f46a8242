package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;


/**
 * AI 聊天消息对象 ai_chat_message
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_chat_message")
public class AiChatMessage extends TenantEntity {

    private static final long serialVersionUID=1L;

    /**
     * 消息编号
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 对话编号
     */
    private Long conversationId;
    /**
     * 回复编号
     */
    private Long replyId;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 段落编号数组
     */
    private String segmentIds;
    /**
     * 消息类型
     */
    private String type;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 是否携带上下文
     */
    private Integer useContext;
    /**
     * 删除标记 (0:未删除，1:已删除)
     */
    private Integer delFlag;

    /**
     * 标识回复结束
     */
    @TableField(exist = false)
    private Integer isready = 1;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 上传附件信息(Json)
     */
    private String fileInfo;

    /**
     * 浏览器标志
     */
    private String browserId;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 引用信息（json）
     */
    private String quoteInfo;

}
