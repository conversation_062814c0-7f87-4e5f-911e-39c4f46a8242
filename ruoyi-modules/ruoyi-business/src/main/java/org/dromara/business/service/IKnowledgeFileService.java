package org.dromara.business.service;

import org.dromara.business.domain.KnowledgeFile;
import org.dromara.business.domain.bo.KnowledgeFileBo;
import org.dromara.business.domain.vo.KnowledgeFileVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 知识库文件服务接口
 */
public interface IKnowledgeFileService {

    /**
     * 查询知识库文件
     *
     * @param fileId 文件主键
     * @return 知识库文件
     */
    KnowledgeFileVo queryById(Long fileId);

    /**
     * 查询知识库文件列表
     *
     * @param bo 知识库文件
     * @return 知识库文件集合
     */
    TableDataInfo<KnowledgeFileVo> queryPageList(KnowledgeFileBo bo, PageQuery pageQuery);

    /**
     * 新增知识库文件
     *
     * @param bo 知识库文件
     * @return 结果
     */
    Boolean insertByBo(KnowledgeFileBo bo);

    /**
     * 修改知识库文件
     *
     * @param bo 知识库文件
     * @return 结果
     */
    Boolean updateByBo(KnowledgeFileBo bo);

    /**
     * 校验并批量删除知识库文件信息
     *
     * @param ids 需要删除的知识库文件主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);


    /**
     * 上传非结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @param fileDescription 文件描述
     * @return 结果
     */
    Boolean uploadUnstructuredFile(Long knowledgeId, Long ossId, String fileDescription);

    /**
     * 上传结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @return 结果
     */
    Boolean uploadStructuredFile(Long knowledgeId, Long ossId);

    /**
     * 上传结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @param fileDescription 文件描述
     * @return 结果
     */
    Boolean uploadStructuredFile(Long knowledgeId, Long ossId, String fileDescription);

    /**
     * 根据知识库ID查询文件列表
     *
     * @param knowledgeId 知识库ID
     * @return 知识库文件集合
     */
    List<KnowledgeFileVo> queryListByKnowledgeId(Long knowledgeId);

    /**
     * 获取知识库文件数量
     *
     * @param knowledgeId 知识库ID
     * @return 文件数量
     */
    int countByKnowledgeId(Long knowledgeId);

    /**
     * 根据id查询知识库文件信息
     * @param knowledgeFileId
     * @return
     */
    KnowledgeFile selectById(Long knowledgeFileId);
}
