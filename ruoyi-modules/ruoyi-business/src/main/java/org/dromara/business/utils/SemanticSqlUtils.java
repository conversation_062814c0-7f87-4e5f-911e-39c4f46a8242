package org.dromara.business.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import io.swagger.v3.core.util.Json;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.service.impl.AiModelChildServiceImpl;
import org.dromara.business.service.impl.AiModelServiceImpl;
import org.dromara.business.service.impl.DataSaveServiceImpl;
import org.dromara.common.core.constant.AIModelTypeConstants;
import org.dromara.common.core.constant.KBDataBaseTypeConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.regex.Pattern;


/**
 * 语义Sql服务工具类
 * 用于调用语义Sql模型服务接口，支持重试机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SemanticSqlUtils {

    // 定义危险 SQL 关键字列表
    private static final List<String> DANGEROUS_SQL_KEYWORDS = Arrays.asList(
        "DROP", "DELETE", "UPDATE", "ALTER", "TRUNCATE", "INSERT"
    );
    //定义轮询标记
    private static int currentIndex = 0;
    private final DataSaveServiceImpl dataSaveService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final AiModelChildServiceImpl aiModelChildService;
    private final AiModelServiceImpl aiModelService;




    /**
     * 去除特殊符号
     *
     * @param sql
     * @return
     */
    public static String formatSQL(String sql) {
        if (StringUtils.isEmpty(sql)) {
            return null;
        }
        //如果没有检测到包含```sql的语句，则直接返回null
        if (!sql.contains("```sql")) {
            return null;
        }
        // 去除换行、制表符、多余空格 去除末尾多余的分号
        String formattedSql = sql.replaceFirst("sql", "").replaceAll("[`\s]+", " ").replaceAll(";+$", "").trim();
        //过滤危险 SQL
        for (String keyword : DANGEROUS_SQL_KEYWORDS) {
            String pattern = "\\b" + keyword + "\\b";  // 确保匹配整个单词
            if (Pattern.compile(pattern).matcher(formattedSql).find()) {
                // 检测到危险关键字
                return null;
            }
        }
        return formattedSql;
    }

    /**
     * 处理sql
     */
    public static JSONArray extractSQLBlock(String sql) {
        if (StringUtils.isEmpty(sql)) {
            return null;
        }
        //如果没有检测到包含```json的语句，则直接返回null
        if (!sql.contains("```json")) {
            return null;
        }
        //提取```json 到```之间的内容
        sql = sql.substring(sql.indexOf("```json"), sql.lastIndexOf("```"));
        // 去除换行、制表符、多余空格 去除末尾多余的分号
        String formattedSql = sql.replaceFirst("json", "").replaceAll("[`\s]+", " ").replaceAll(";+$", "").trim();
        //过滤危险 SQL
        for (String keyword : DANGEROUS_SQL_KEYWORDS) {
            String pattern = "\\b" + keyword + "\\b";  // 确保匹配整个单词
            if (Pattern.compile(pattern).matcher(formattedSql).find()) {
                // 检测到危险关键字
                return null;
            }
        }
        //是一个json，转换成一个json对象
        JSONArray sqlArray = new JSONArray();
        try {
            JSONObject jsonObject = JSONObject.parseObject(formattedSql);
            if(null != jsonObject){
                //获取key：生成的SQL语句，并判断是否是一个Array
                Object sqlField = jsonObject.get("生成的语句");
                if(sqlField instanceof String){
                    JSONObject sqlJson = new JSONObject();
                    sqlJson.put("查询", jsonObject.get("用户的问题"));
                    sqlJson.put("SQL",sqlField);
                    sqlArray.add(sqlJson);
                }else if(sqlField instanceof JSONArray){
                    sqlArray = (JSONArray) sqlField;
                }else {
                    return null;
                }
            }
        }catch (Exception e){
            log.error("json转换异常");
            return null;
        }
        return sqlArray;
    }



    /**
     * 生成sql
     *
     * @param userMessage
     * @param databaseConfig
     * @return
     */
    public JSONArray semanticSql(String userMessage, KbDatabaseConfig databaseConfig) throws ServiceException {
        if (null == databaseConfig) {
            return null;
        }
        //全局提示词
        String sysCustomStr = """
            1.根据用户提供的信息对用户的问题生成相关的语句。
            2.如果用户的问题不符合生成标准则不生成。
            3.符合生成标准时且是非统计类的语句时,如果用户未指定查询数量,则默认返回前10条。
            4.如果用户提问的是多个问题，需要将问题拆分，并将生成的sql与问题对应。
            5.请将用户的问题和生成的语句,按照以下格式输出:
                ```json {
                "用户的问题":"",
                "生成的语句":""
                }```
            """;
        String tempMessage = """
            {0}

            用户的问题: {1}
            """;
        KbDatabaseConfigBo bo = new KbDatabaseConfigBo();
        BeanUtils.copyProperties(databaseConfig, bo);
        String databaseInfo = dataSaveService.cacheDatabase(bo);
        if (StringUtils.isEmpty(databaseInfo)) {
            return null;
        }
//        sysCustomStr = MessageFormat.format(sysCustomStr, databaseConfig.getDatabaseType());
        tempMessage = MessageFormat.format(tempMessage, databaseInfo, userMessage);
        ChatMessage systemChatMessage = SystemMessage.systemMessage(sysCustomStr);
        UserMessage userChatMessage = UserMessage.userMessage(tempMessage);
        List<ChatMessage> messages = List.of(systemChatMessage, userChatMessage);
        // 构建模型服务
        ChatModel chatLanguageModel = createModel();
        if(null == chatLanguageModel) return null;
        // 调用模型
        return extractSQLBlock(chatLanguageModel.chat(messages).aiMessage().text());
    }

    /**
     * 执行sql
     *
     * @param sqlArray
     * @return
     */
    public String doSql(JSONArray sqlArray, KbDatabaseConfig kbDatabaseConfig) {
        if (sqlArray ==null || sqlArray.isEmpty()) {
            return null;
        }
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        List<Future<Void>> futures = new ArrayList<>();
        // 遍历 JSON 数组，提交 SQL 任务
        for (int i = 0; i < sqlArray.size(); i++) {
            JSONObject sqlObj = sqlArray.getJSONObject(i);
            String queryKey = sqlObj.getString("查询");
            String sql = sqlObj.getString("SQL");
            if(StringUtils.isEmpty(sql)){
                resultMap.put(queryKey, "");
                continue;
            }
            Future<Void> future = threadPoolTaskExecutor.submit(() -> {
                String result = executeQuery(sql, kbDatabaseConfig);
                resultMap.put(queryKey, result);
                return null;
            });

            futures.add(future);
        }
        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 执行sql
     */
    private String executeQuery(String sql, KbDatabaseConfig kbDatabaseConfig) {
        String url = StrUtil.format(KBDataBaseTypeConstants.Mysql_url, kbDatabaseConfig.getDatabaseUrl(), kbDatabaseConfig.getDatabasePort(), kbDatabaseConfig.getDatabaseName());
        // 连接数据库并执行查询
        try (Connection conn = DriverManager.getConnection(url, kbDatabaseConfig.getUsername(), kbDatabaseConfig.getPassword());
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            JSONArray jsonArray = new JSONArray();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object columnValue = rs.getObject(i);
                    jsonObject.put(columnName, columnValue);
                }
                jsonArray.add(jsonObject);
            }
            return jsonArray.toJSONString();
        } catch (SQLException e) {
            log.error("执行" + sql + "异常" + e.getMessage());
            return null;
        }
    }

    /**
     * 创建并获取模型信息
     *
     * @return
     */
    private ChatModel createModel() throws ServiceException {
        //此处修改为查询数据库中的SQL模型
        List<AiModel> aiModelList = aiModelService.getCoderModel();
        if (aiModelList.isEmpty()) throw new ServiceException("SQL转义模型未开启");
        List<Long> modelIds = aiModelList.stream().map(AiModel::getId).toList();
        List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelIds(modelIds, AIModelTypeConstants.coder_model);
        if(CollUtil.isEmpty(aiModelChildList)) throw new ServiceException("SQL转义模型未获取到模型地址");

        AiModelChild aiModelChild = aiModelChildList.get(currentIndex);
        currentIndex = (currentIndex + 1) % aiModelChildList.size();
        AiModel aiModel = aiModelList.stream().filter(model -> model.getId().equals(aiModelChild.getModelId())).findAny().orElse(null);
        if(null == aiModel) throw new ServiceException("SQL转义模型未获取到模型信息");

        return OpenAiChatModel.builder()
            .apiKey("key")
            .baseUrl(aiModelChild.getModelUrl())
            .modelName(aiModel.getModelCode())
            .temperature(aiModel.getTemperature())
            .build();
    }


}
