package org.dromara.business.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 模型历史信息用量返回实体
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
public class AiModelHistoryMsgBo implements Serializable {


    /**
     * 部门（1-市法院，2-费县法院）
     */
    private Integer deptType;

    /**
     * 日统计 开始时间
     */
    private String dayStartTime;

    /**
     * 日统计 结束时间
     */
    private String dayEndTime;

    /**
     * 月统计 开始时间
     */
    private String monthStartTime;


    /**
     * 月统计 结束时间
     */
    private String monthEndTime;


    /**
     * 子部门集合
     */
    private List<Long> deptList;

    /**
     * 部门id
     */
    private Long deptId;



}
