package org.dromara.business.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.AiChatConversation;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.domain.bo.AiChatConversationBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.business.mapper.AiChatConversationMapper;
import org.dromara.business.mapper.AiModelAuthMapper;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.business.service.IAiChatConversationService;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI 聊天对话Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RequiredArgsConstructor
@Service
public class AiChatConversationServiceImpl implements IAiChatConversationService {

    private final AiChatConversationMapper baseMapper;
    private final AiModelAuthMapper aiModelAuthMapper;

    /**
     * 查询AI 聊天对话
     */
    @Override
    public AiChatConversationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询AI 聊天对话列表
     */
    @Override
    public TableDataInfo<AiChatConversationVo> queryPageList(AiChatConversationBo bo, PageQuery pageQuery) {
        //判断是否是管理员身份，管理员可查看所有会话
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("未获取到登录用户信息");
        }
        if (!loginUser.isAdmin()) {
            bo.setUserId(loginUser.getUserId());
            //并且根据用户id，查询授权key
            AiModelAuth aiModelAuth = aiModelAuthMapper.selectOne(new LambdaQueryWrapper<AiModelAuth>().eq(AiModelAuth::getUserId, loginUser.getUserId()).eq(AiModelAuth::getDelFlag, 0));
            if(null != aiModelAuth){
                bo.setModelKey(aiModelAuth.getModelKey());
            }
        }
        Page<AiChatConversationVo> result = baseMapper.queryPageList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询AI 聊天对话列表
     */
    @Override
    public List<AiChatConversationVo> queryList(AiChatConversationBo bo) {
        LambdaQueryWrapper<AiChatConversation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiChatConversation> buildQueryWrapper(AiChatConversationBo bo) {
        LambdaQueryWrapper<AiChatConversation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getKnowledgeId() != null, AiChatConversation::getKnowledgeId, bo.getKnowledgeId());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AiChatConversation::getTitle, bo.getTitle());
        lqw.eq(bo.getPinned() != null, AiChatConversation::getPinned, bo.getPinned());
        lqw.eq(bo.getPinnedTime() != null, AiChatConversation::getPinnedTime, bo.getPinnedTime());
        lqw.eq(StringUtils.isNotBlank(bo.getSystemMessage()), AiChatConversation::getSystemMessage, bo.getSystemMessage());
        lqw.eq(bo.getTemperature() != null, AiChatConversation::getTemperature, bo.getTemperature());
        lqw.eq(bo.getMaxTokens() != null, AiChatConversation::getMaxTokens, bo.getMaxTokens());
        lqw.eq(bo.getMaxContexts() != null, AiChatConversation::getMaxContexts, bo.getMaxContexts());
        lqw.like(bo.getTitle() != null, AiChatConversation::getTitle, bo.getTitle());
        lqw.eq(bo.getModelKey() != null, AiChatConversation::getModelKey, bo.getModelKey());
        return lqw;
    }

    /**
     * 新增AI 聊天对话
     */
    @Override
    public Boolean insertByBo(AiChatConversationBo bo) {
        AiChatConversation add = BeanUtil.toBean(bo, AiChatConversation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改AI 聊天对话
     */
    @Override
    public Boolean updateByBo(AiChatConversationBo bo) {
        AiChatConversation update = BeanUtil.toBean(bo, AiChatConversation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiChatConversation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除AI 聊天对话
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 创建【我的】聊天对话
     * @param createReqVO
     * @return
     */
    @Override
    public Long createChatConversationMy(AiChatConversationBo createReqVO) {
        //前置条件，校验模型
        String key = getModelKey();
        //获取浏览器唯一标志
        String browserId = getBrowserId();
        // 创建 AiChatConversationDO 聊天对话
        AiChatConversation add = BeanUtil.toBean(createReqVO, AiChatConversation.class);
        add.setTitle(AiChatConversationBo.TITLE_DEFAULT);
        add.setModelKey(key);
        add.setBrowserId(browserId);
        //如果有登录人信息，则保存登录人信息
        boolean isLogin = StpUtil.isLogin();
        if(isLogin){
            LoginUser loginUser = LoginHelper.getLoginUser();
            if(loginUser != null){
                add.setUserId(loginUser.getUserId());
                add.setCreateDept(loginUser.getDeptId());
                add.setTenantId(loginUser.getTenantId());
            }
        }
        //创建会话时，查询key授权，补全模型信息
        AiModelAuth aiModelAuth = aiModelAuthMapper.selectOne(new LambdaQueryWrapper<AiModelAuth>().eq(AiModelAuth::getModelKey, key).eq(AiModelAuth::getDelFlag, 0));
        if(null != aiModelAuth){
            add.setModelId(aiModelAuth.getModelId());
            add.setModelName(aiModelAuth.getModelName());
        }
        baseMapper.insert(add);
        return add.getId();
    }

    /**
     * 获取浏览器唯一标志
     * @return
     */
    private String getBrowserId() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String browser = request.getHeader("Browser");
        if(StringUtils.isEmpty(browser)){
            throw new ServiceException("浏览器标识不能为空");
        }
        return browser;
    }

    /**
     * 更新【我的】聊天对话
     * @param updateReqVO
     */
    @Override
    public void updateChatConversationMy(AiChatConversationBo updateReqVO) {
        //前置条件，校验模型
        String key = getModelKey();
        // 1.1 校验对话是否存在
        AiChatConversation conversation = baseMapper.selectById(updateReqVO.getId());
        if (conversation == null) {
            throw new ServiceException("对话不存在");
        }
        if (!ObjectUtil.equals(conversation.getModelKey(), key)) {
            throw new ServiceException("对话不存在");
        }
        // 2. 更新对话信息
        AiChatConversation updateObj = new AiChatConversation();
        BeanUtils.copyProperties(updateReqVO, updateObj);
        if (ObjectUtil.equals(updateReqVO.getPinned(),1)) {
            updateObj.setPinnedTime(new Date());
        }
        baseMapper.updateById(updateObj);

    }

    @Override
    public List<AiChatConversationVo> getChatConversationListByUserId(String projectId) {
        //前置条件，校验模型
        String key = getModelKey();
        //获取浏览器标识
        String browserId = getBrowserId();
        AiChatConversationBo aiChatConversationBo = new AiChatConversationBo();
        aiChatConversationBo.setProjectId(projectId);
        boolean isLogin = StpUtil.isLogin();
        if(isLogin){
            aiChatConversationBo.setUserId(LoginHelper.getUserId());
        }else {
            aiChatConversationBo.setModelKey(key);
            aiChatConversationBo.setBrowserId(browserId);
        }
        return baseMapper.getChatConversationListByUserId(aiChatConversationBo);
    }

    /**
     * 统一校验模型令牌
     * @return
     */
    private String getModelKey() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String key = request.getHeader("Model-Key");
        if(StringUtils.isEmpty(key)){
            throw new ServiceException("模型令牌不能为空");
        }
        return key;
    }

    @Override
    @Cacheable(cacheNames = "global:ai_chat_conver_info:#10m", key = "#conversationId", condition = "#conversationId != null")
    public AiChatConversation getChatConversation(Long conversationId) {
        //前置条件，校验模型
        String key = getModelKey();
        //校验该对话是否是该用户的
        AiChatConversation conversation = baseMapper.selectById(conversationId);
        if (conversation == null || !ObjectUtil.equals(key, conversation.getModelKey())) {
           return null;
        }
        return conversation;
    }

    @Override
    public void deleteChatConversationMy(Long id) {
        //前置条件，校验模型
        String key = getModelKey();
        AiChatConversation conversation = baseMapper.selectById(id);
        if (conversation == null) {
            throw new ServiceException("对话不存在");
        }
        //校验该对话是否是该用户的，如果当前登录用户是管理员，则不校验key
        boolean isLogin = StpUtil.isLogin();
        if(isLogin){
            if(!LoginHelper.getLoginUser().isAdmin()){
                if (!ObjectUtil.equals(LoginHelper.getUserId(), conversation.getUserId())) {
                    throw new ServiceException("当前对话用户不一致无法删除");
                }
            }
        }else {
            if(conversation.getUserId() !=null){
                throw new ServiceException("当前对话用户不一致无法删除");
            }
            if (!ObjectUtil.equals(key, conversation.getModelKey())) {
                throw new ServiceException("当前对话令牌不一致无法删除");
            }
        }

        conversation.setDelFlag(1);
        conversation.setUpdateTime(new Date());
        baseMapper.updateById(conversation);
    }

    /**
     * 删除未置顶会话
     */
    @Override
    public void deleteChatConversationMyByUnpinned() {
        //前置条件，校验模型
        String key = getModelKey();
        //根据用户id查询该用户下所有未置顶的会话
        List<AiChatConversation> list = baseMapper.selectList(
            new LambdaQueryWrapper<AiChatConversation>().eq(AiChatConversation::getModelKey, key)
                .eq(AiChatConversation::getPinned, 0).eq(AiChatConversation::getDelFlag, 0));
        if(!CollectionUtil.isEmpty(list)){
            list.forEach(item -> {
                item.setDelFlag(1);
                item.setUpdateTime(new Date());
            });
            //删除所有会话
            baseMapper.updateBatchById(list);
        }
    }

    /**
     * 根据id更新
     * @param aiChatConversation
     */
    @Override
    public void updateBy(AiChatConversation aiChatConversation) {
        baseMapper.updateById(aiChatConversation);
    }
}
