package org.dromara.business.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.BsProjectAccept;
import org.dromara.business.domain.vo.BsProjectAcceptVo;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 施工图项目受理清单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface BsProjectAcceptMapper extends BaseMapperPlus<BsProjectAccept, BsProjectAcceptVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsProjectAccept> selectList(IPage<BsProjectAccept> page, @Param(Constants.WRAPPER) Wrapper<BsProjectAccept> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsProjectAccept> selectList(@Param(Constants.WRAPPER) Wrapper<BsProjectAccept> queryWrapper);

    @Override
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    }, joinStr = "AND")
    List<BsProjectAccept> selectByIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    int updateById(@Param(Constants.ENTITY) BsProjectAccept entity);

}
