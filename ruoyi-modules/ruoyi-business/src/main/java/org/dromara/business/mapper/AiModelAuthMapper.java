package org.dromara.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.domain.bo.AiModelAuthBo;
import org.dromara.business.domain.vo.AiModelAuthVo;
import org.dromara.business.vo.AiModelAuthCountVO;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.Map;

/**
 * 模型授权Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface AiModelAuthMapper extends BaseMapperPlus<AiModelAuth, AiModelAuthVo> {

    Page<AiModelAuthVo> queryPageList(@Param("page") Page<AiModelAuth> page, @Param("bo") AiModelAuthBo bo);

    List<AiModelAuthCountVO> getMonthCount();

    List<AiModelAuthCountVO> getAuthModelInfoAll();
}
