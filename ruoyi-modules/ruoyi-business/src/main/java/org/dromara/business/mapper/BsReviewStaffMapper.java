package org.dromara.business.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.BsReviewStaff;
import org.dromara.business.domain.vo.BsReviewStaffVo;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 审查人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface BsReviewStaffMapper extends BaseMapperPlus<BsReviewStaff, BsReviewStaffVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsReviewStaff> selectList(IPage<BsReviewStaff> page, @Param(Constants.WRAPPER) Wrapper<BsReviewStaff> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<BsReviewStaff> selectList(@Param(Constants.WRAPPER) Wrapper<BsReviewStaff> queryWrapper);

    @Override
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    }, joinStr = "AND")
    List<BsReviewStaff> selectByIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    int updateById(@Param(Constants.ENTITY) BsReviewStaff entity);

    /**
     * 根据人员姓名查询类目标识
     *
     * @param personName 人员姓名
     * @return 类目标识
     */
    String selectCategoryKeyByPersonName(@Param("personName") String personName);

}
