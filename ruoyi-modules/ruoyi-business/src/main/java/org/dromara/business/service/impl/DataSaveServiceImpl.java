package org.dromara.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import dev.langchain4j.data.message.ImageContent;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.response.ChatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDResources;
import org.apache.pdfbox.pdmodel.graphics.PDXObject;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.sax.BodyContentHandler;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.common.core.constant.AIModelTypeConstants;
import org.dromara.common.core.constant.KBDataBaseTypeConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysConfigService;
import org.dromara.system.service.impl.SysOssServiceImpl;
import org.slf4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.xml.sax.ContentHandler;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.chat.ChatModel;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Base64;

import static dev.langchain4j.data.message.TextContent.from;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DataSaveServiceImpl {

    private final SysOssServiceImpl sysOssService;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final AiModelServiceImpl aiModelService;
    private final AiModelChildServiceImpl aiModelChildService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final ISysConfigService configService;

    //前端支持的图片类型
    private final List<String> imgType = Arrays.asList(".png", ".jpg", ".jpeg");

    // 添加模型轮询计数器，使用AtomicInteger保证线程安全
    private final AtomicInteger modelCounter = new AtomicInteger(0);

    // 模型并发负载计数器，记录每个模型当前的并发数
    private final Map<String, AtomicInteger> modelConcurrentCounter = new HashMap<>();

    // 模型最大并发数
    private static final int MAX_CONCURRENT_PER_MODEL = 4;

    // 专用于视觉模型分析的线程池
    private ThreadPoolExecutor visionModelThreadPool;

    // 视觉模型分析任务队列最大长度
    private static final int VISION_TASK_QUEUE_CAPACITY = 100;

    //定义轮询标记
    private static int currentIndex = 0;

    @PostConstruct
    public void initVisionThreadPool() {
        log.info("初始化视觉模型分析线程池");
        // 获取所有可用的视觉模型
        List<AiModel> aiModelList = aiModelService.getIdentifyModel();
        int modelCount = 1; // 默认至少有1个模型

        if (!aiModelList.isEmpty()) {
            AiModel aiModel = aiModelList.get(0);
            List<AiModelChild> childModels = aiModelChildService.selectListByModelId(aiModel.getId());
            modelCount = Math.max(childModels.size(), 1);
        }

        // 根据模型数量和每个模型的最大并发数计算线程池大小
        int corePoolSize = modelCount * MAX_CONCURRENT_PER_MODEL;
        int maximumPoolSize = corePoolSize * 2;

        // 创建有界队列，当队列满时，会触发拒绝策略
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(VISION_TASK_QUEUE_CAPACITY);

        // 创建线程池，使用CallerRunsPolicy作为拒绝策略，避免任务丢失
        visionModelThreadPool = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            60L,
            TimeUnit.SECONDS,
            workQueue,
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        log.info("视觉模型分析线程池初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}",
            corePoolSize, maximumPoolSize, VISION_TASK_QUEUE_CAPACITY);
    }

    private static List<List<Map<String, String>>> partitionData(List<Map<String, String>> rows, int rowCount) {
        List<List<Map<String, String>>> partitions = new ArrayList<>();
        if (rowCount < 10000) {
            // 低于 1W 行，不分片
            partitions.add(rows);
        } else if (rowCount <= 50000) {
            // 1W - 5W 行，平均分片
            int chunkSize = (int) Math.ceil((double) rowCount / 2);
            partitions.addAll(splitList(rows, chunkSize));
        } else {
            // 超过 5W 行，按总行数的五分之一分片
            int chunkSize = Math.max(rowCount / 5, 1);
            List<List<Map<String, String>>> largePartitions = splitList(rows, chunkSize);

            for (List<Map<String, String>> part : largePartitions) {
                if (part.size() > 30000) {
                    // 单片大于 3W 行，继续细分
                    partitions.addAll(partitionData(part, part.size()));
                } else {
                    partitions.add(part);
                }
            }
        }
        return partitions;
    }

    /**
     * 均分表格
     */
    private static List<List<Map<String, String>>> splitList(List<Map<String, String>> rows, int chunkSize) {
        List<List<Map<String, String>>> splitResult = new ArrayList<>();
        for (int i = 0; i < rows.size(); i += chunkSize) {
            splitResult.add(rows.subList(i, Math.min(i + chunkSize, rows.size())));
        }
        return splitResult;
    }

    /**
     * 根据文件id下载文件信息
     *
     * @param fileId
     * @return
     */
    @Cacheable(cacheNames = "global:ai_chat_file:#10m", key = "#fileId", condition = "#fileId != null")
    public SysOssVo getFileContent(Long fileId) throws Exception{
        SysOssVo sysOssVo = sysOssService.getById(fileId);
        if (null != sysOssVo) {
            //判断文件格式，如果是图片，则需要走视觉模型分析图片内容
            if(imgType.contains(sysOssVo.getFileSuffix())){
                String imgContent = identifyImages(sysOssVo);
                sysOssVo.setFileContent(imgContent);
            }else {
                PDDocument document = null;
                InputStream inputStream = null;
                try {
                    String content = "";
                    inputStream = sysOssService.getFileContent(sysOssVo);
                    if (sysOssVo.getFileSuffix().equalsIgnoreCase(".pdf")) {
                        document = PDDocument.load(inputStream);
                        PDFTextStripper stripper = new PDFTextStripper();
                        stripper.setSortByPosition(true); // 按位置排序文本
                        content = stripper.getText(document);
                    }else {
                        Tika tika = new Tika();
                        content = tika.parseToString(inputStream);
                    }

                    sysOssVo.setFileContent(content);
                } catch (Exception e) {
                    log.error("文件{}{}转换失败", sysOssVo.getOssId(), sysOssVo.getOriginalName());
                }finally {
                    // 关闭资源
                    try {
                        if (document != null) document.close();
                        if (inputStream != null) inputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return sysOssVo;
    }


    /**
     * 从模型中获取图片信息
     * @param sysOssVo
     * @return
     */
    private String identifyImages(SysOssVo sysOssVo) throws Exception{
        //查询模型表，获取视觉模型，临时只有一个视觉模型，所以此处查询视觉模型取第一个
        List<AiModel> aiModelList = aiModelService.getIdentifyModel();
        //所有视觉模型均未开启，返回异常
        if(aiModelList.isEmpty()) throw new ServiceException("多模态模型未开启");
        List<Long> modelIds = aiModelList.stream().map(AiModel::getId).toList();
        List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelIds(modelIds, AIModelTypeConstants.visual_model);
        if(CollUtil.isEmpty(aiModelChildList)) throw new ServiceException("多模态模型未获取到模型地址");

        AiModelChild aiModelChild = aiModelChildList.get(currentIndex);
        currentIndex = (currentIndex + 1) % aiModelChildList.size();
        AiModel aiModel = aiModelList.stream().filter(model -> model.getId().equals(aiModelChild.getModelId())).findAny().orElse(null);
        if(null == aiModel) throw new ServiceException("多模态模型未获取到模型信息");

        try {
            ChatModel openAiChatModel = OpenAiChatModel.builder()
                .apiKey("key")
                .baseUrl(aiModelChild.getModelUrl())
                .modelName(aiModel.getModelCode()).build();
            //图片下载后转base64
            String base64Img = sysOssService.getFileBase64(sysOssVo);
            if(null != base64Img){
                UserMessage userMessage = UserMessage.from(
                    from("深度分析这张图片"),
                    ImageContent.from(base64Img, "image/" + sysOssVo.getFileSuffix().substring(1))
                );
                ChatResponse chatResponse = openAiChatModel.chat(userMessage);
                return chatResponse.aiMessage().text();
            }
            return "";
        }catch (ServiceException e){
            log.error("视觉模型{}{}调用失败{}", aiModel.getModelName(), aiModel.getModelUrl(),e.getMessage());
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("视觉模型{}{}调用失败{}", aiModel.getModelName(), aiModel.getModelUrl(),e.getMessage());
            throw new Exception("多模态模型调用失败");
        }
    }

    /**
     * 根据文件id下载文件信息并将文档分块
     *
     * @param fileId
     * @return
     */
//    @Cacheable(cacheNames = "global:ai_chat_file_segment:#10m", key = "#fileId", condition = "#fileId != null")
    public SysOssVo getFileContentSegment(Long fileId) throws Exception {
        Tika tika = new Tika();
        SysOssVo sysOssVo = sysOssService.getById(fileId);
        PDDocument document = null;
        InputStream inputStream = null;
        if (null != sysOssVo) {
            try {
                String content = "";
                inputStream = sysOssService.getFileContent(sysOssVo);
                if (sysOssVo.getFileSuffix().equalsIgnoreCase(".pdf")) {
                    document = PDDocument.load(inputStream);
                    PDFTextStripper stripper = new PDFTextStripper();
                    stripper.setSortByPosition(true); // 按位置排序文本
                    content = stripper.getText(document);
                }else {
                    content = this.parseFile(inputStream);
                }
                byte[] fileBytes = sysOssService.getFileContent(sysOssVo).readAllBytes();

                // 提取pdf文字内容
                InputStream textInputStream = new ByteArrayInputStream(fileBytes);


                StringBuilder imageContent = new StringBuilder();

                if (configService.selectConfigByKey("sys.knowledge.image").equals("true")){
                    // 提取文档图片内容
                    log.info("开始提取文档图片内容");
                    // 耗时统计
                    long startTime = System.currentTimeMillis();

                    this.extractImage(sysOssVo, fileBytes, imageContent);

                    long endTime = System.currentTimeMillis();
                    log.info("提取文档图片内容耗时：{}ms", endTime - startTime);
                }
                // 合并文字和图片内容
                String fullContent = content;
                if (!imageContent.isEmpty()) {
                    fullContent = content + "\n\n" + imageContent.toString();
                }

                // 将内容按最大长度进行分段
                List<String> paragraphs = new ArrayList<>();
                if (fullContent.length() > 300000) {
                    paragraphs = segmentContent(fullContent, 5000);
                } else {
                    paragraphs = segmentContent(fullContent, 2000);
                }

                // 设置分段结果
                sysOssVo.setFileParagraphs(paragraphs);

                // 设置清理后的完整内容
                sysOssVo.setFileContent(fullContent);

            }
            catch (ExecutionException  e) {
                throw e; // 直接抛出
            }
            catch (Exception e) {
                log.error("文件" + sysOssVo.getOssId() + sysOssVo.getOriginalName() + "转换失败", e);
                throw new ServiceException("无法识别到有效的文字内容，请重换文件");
            }
            finally {
                // 关闭资源
                try {
                    if (document != null) document.close();
                    if (inputStream != null) inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sysOssVo;
    }

    private void extractImage(SysOssVo sysOssVo, byte[] fileBytes, StringBuilder imageContent) throws ExecutionException, InterruptedException, TimeoutException {
        String fileSuffix = sysOssVo.getFileSuffix().toLowerCase();

        // 根据文件类型提取图片
        List<BufferedImage> images = new ArrayList<>();
        if (fileSuffix.equalsIgnoreCase(".pdf")) {
            // PDF文档图片提取
            InputStream imageInputStream = new ByteArrayInputStream(fileBytes);
            images = extractImagesFromPdf(imageInputStream);
        } else if (fileSuffix.equalsIgnoreCase(".docx") || fileSuffix.equalsIgnoreCase(".doc") || fileSuffix.equalsIgnoreCase(".wps")) {
            // Word文档图片提取
            InputStream imageInputStream = new ByteArrayInputStream(fileBytes);
            images = extractImagesFromWord(imageInputStream, fileSuffix);
        } else if (fileSuffix.equalsIgnoreCase(".pptx") || fileSuffix.equalsIgnoreCase(".ppt")) {
            // PPT文档图片提取
            InputStream imageInputStream = new ByteArrayInputStream(fileBytes);
            images = extractImagesFromPpt(imageInputStream, fileSuffix);
        }

        // 分析提取到的图片
        if (!images.isEmpty()) {
            AtomicInteger count = new AtomicInteger(0);
            List<Future<String>> imageFutures = new ArrayList<>();
            // 统计耗时
            long startTimeAll = System.currentTimeMillis();
            images.forEach(image -> {
                try {
                    // 统计转换图片耗时
                    long startTime = System.currentTimeMillis();
                    String base64Image = imageToBase64(image);
                    long endTime = System.currentTimeMillis();
                    log.info("转换图片耗时：{}ms", endTime - startTime);

                    if (base64Image != null) {
                        // 使用线程池提交图片分析任务
                        Future<String> future = visionModelThreadPool.submit(() -> {
                            try {
                                // 统计图片分析耗时
                                long analysisStart = System.currentTimeMillis();
                                String imgAnalysisResult = analyzeImage(base64Image, "png");
                                long analysisEnd = System.currentTimeMillis();
                                log.info("图片分析单次耗时：{}ms", analysisEnd - analysisStart);

                                if (StringUtils.isNotEmpty(imgAnalysisResult)) {
                                    return "\n图片" + count.incrementAndGet() + "内容：" + imgAnalysisResult + "\n";
                                }
                                return "";
                            }
                            catch (ServiceException e){
                                throw e;
                            }
                            catch (Exception e) {
                                log.error("分析文档中图片失败:{}", e);
                                throw new ServiceException("分析文档中图片失败");
                            }
                        });
                        imageFutures.add(future);
                    }
                }
                catch (ServiceException e) {
                    throw e;
                }
                catch (Exception e) {
                    log.error("处理图片失败", e);
                    throw new ServiceException("处理图片失败");
                }
            });

            // 收集所有分析结果
            for (Future<String> future : imageFutures) {
                try {
                    String result = future.get(30, java.util.concurrent.TimeUnit.SECONDS); // 设置超时
                    if (StringUtils.isNotEmpty(result)) {
                        imageContent.append(result);
                    }
                }
                catch (Exception e) {
                    log.error("等待图片分析结果时出错: {}", e.getMessage());
                    throw e;
                }
            }
            long endTimeAll = System.currentTimeMillis();
            log.info("图片分析总耗时{}ms", endTimeAll - startTimeAll);
            // 分析结果
            log.info("分析结果{}", imageContent.toString());
        }
    }

    private void saveImage(BufferedImage image) throws IOException {
        // 保存图片到resources目录
        String imageName = "pdf_image_1.png";
        String resourcePath = "src/main/resources/pdf_images/";
        File directory = new File(resourcePath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        File outputFile = new File(resourcePath + imageName);
        ImageIO.write(image, "png", outputFile);
    }

    /**
     * 从PDF文档中提取图片
     *
     * @param inputStream PDF文件输入流
     * @return 提取的图片列表
     */
    private List<BufferedImage> extractImagesFromPdf(InputStream inputStream) {
        List<BufferedImage> images = new ArrayList<>();
        PDDocument document = null;
        try {
//            document = PDDocument.load(new ByteArrayInputStream(inputStream.readAllBytes()));
            document = PDDocument.load(inputStream);

            // 使用RandomAccessReadBuffer加载PDF内容
//            byte[] bytes = inputStream.readAllBytes();
//            RandomAccessReadBuffer buffer = new RandomAccessReadBuffer(bytes);
//            document = PDDocument.load(buffer);

            for (PDPage page : document.getPages()) {
                PDResources resources = page.getResources();
                if (resources == null) continue;

                for (COSName xObjectName : resources.getXObjectNames()) {
                    PDXObject xObject = resources.getXObject(xObjectName);
                    if (xObject instanceof PDImageXObject) {
                        PDImageXObject imageXObject = (PDImageXObject) xObject;
                        BufferedImage image = imageXObject.getImage();
                        // 过滤掉太小的图片，可能是水印或页眉页脚
                        if (image.getWidth() > 100 && image.getHeight() > 100) {
                            images.add(image);
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("提取PDF中的图片失败", e);
        } finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    log.error("关闭PDF文档失败", e);
                }
            }
        }
        return images;
    }

    /**
     * 将BufferedImage转换为Base64字符串
     *
     * @param image 图片对象
     * @return Base64字符串
     */
    private String imageToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (IOException e) {
            log.error("图片转Base64失败", e);
            return null;
        }
    }

    /**
     * 使用视觉模型分析图片内容
     *
     * @param base64Image 图片的Base64编码
     * @param format 图片格式
     * @return 分析结果
     */
    private String analyzeImage(String base64Image, String format) {
        List<AiModel> aiModelList = aiModelService.getIdentifyModel();
        if (aiModelList.isEmpty()) {
            return "";
        }

        AiModel aiModel = aiModelList.get(0);
        List<AiModelChild> aiModelChildList = aiModelChildService.selectListByModelId(aiModel.getId());
        if (aiModelChildList.isEmpty()) {
            return "";
        }

        // 使用轮询方式选择初始模型
        int modelIndex = modelCounter.getAndIncrement() % aiModelChildList.size();
        if (modelCounter.get() > 100) {
            modelCounter.set(0);
        }

        // 选择当前模型
        AiModelChild aiModelChild = aiModelChildList.get(modelIndex);

        try {
            // 直接执行模型调用，不再提交到线程池
            // 初始化模型并发计数器（如果不存在）
            modelConcurrentCounter.computeIfAbsent(aiModelChild.getModelUrl(), k -> new AtomicInteger(0));

            // 增加模型并发计数
            AtomicInteger concurrentCount = modelConcurrentCounter.get(aiModelChild.getModelUrl());
            int currentCount = concurrentCount.incrementAndGet();

            try {
                log.info("使用模型: {}, URL: {}, 当前并发数: {}",
                    aiModelChild.getId(), aiModelChild.getModelUrl(), currentCount);

                // 调用模型分析图片
                ChatModel openAiChatModel = OpenAiChatModel.builder()
                    .apiKey("key")
                    .baseUrl(aiModelChild.getModelUrl())
                    .modelName(aiModel.getModelCode()).build();

                UserMessage userMessage = UserMessage.from(
                    from("提取这张图片中的所有文字"),
                    ImageContent.from(base64Image, "image/" + format)
                );

                ChatResponse chatResponse = openAiChatModel.chat(userMessage);
                return chatResponse.aiMessage().text();
            }catch (ResourceAccessException e){
                log.error(e.getMessage());
                throw new ServiceException("检查图片识别服务是否开启");
            }
            catch (Exception e) {
                log.error("模型分析失败: {}, 错误信息: {}", aiModelChild.getModelUrl(), e.getMessage());

                // 如果当前模型失败，尝试使用下一个模型
                if (aiModelChildList.size() > 1) {
                    int nextModelIndex = (modelIndex + 1) % aiModelChildList.size();
                    AiModelChild fallbackModelChild = aiModelChildList.get(nextModelIndex);

                    log.info("尝试使用备用模型: {}", fallbackModelChild.getModelUrl());

                    ChatModel fallbackModel = OpenAiChatModel.builder()
                        .apiKey("key")
                        .baseUrl(fallbackModelChild.getModelUrl())
                        .modelName(aiModel.getModelCode()).build();

                    UserMessage fallbackMessage = UserMessage.from(
                        from("提取这张图片中的所有文字"),
                        ImageContent.from(base64Image, "image/" + format)
                    );

                    ChatResponse fallbackResponse = fallbackModel.chat(fallbackMessage);
                    return fallbackResponse.aiMessage().text();
                }
                return "";
            } finally {
                // 减少模型并发计数
                concurrentCount.decrementAndGet();
            }
        } catch (Exception e) {
            log.error("视觉模型分析过程中出错: {}", e.getMessage());
            throw e;
        }
    }

    public static String parseFile(InputStream input){
        Parser parser = new AutoDetectParser();
        try{
            Metadata metadata = new Metadata();
            metadata.set(Metadata.CONTENT_ENCODING, "utf-8");
            ContentHandler handler = new BodyContentHandler(1024*1024*10);//当文件大于100000时，new BodyContentHandler(1024*1024*10);
            ParseContext context = new ParseContext();
            context.set(Parser.class,parser);
            parser.parse(input,handler, metadata,context);
            for(String name:metadata.names()) {
                System.out.println(name+":"+metadata.get(name));
            }
            System.out.println(handler.toString());
            return handler.toString();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                if(input!=null)input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;

    }


    /**
     * 清理文本内容，删除多余的空白字符和特殊符号
     *
     * @param content 原始文本内容
     * @return 清理后的文本内容
     */
    private String cleanTextContent(String content) {
        if (content == null) {
            return "";
        }

        // 替换连续的空白字符（空格、制表符、换行符等）为单个空格
        content = content.replaceAll("\\s+", " ");

        // 删除不可打印字符
        content = content.replaceAll("[\\p{C}]", "");

        // 删除特殊控制字符
        content = content.replaceAll("[\\x00-\\x1F\\x7F]", "");

        // 删除多余的标点符号重复
        content = content.replaceAll("(\\.|,|;|:|!|\\?){2,}", "$1");

        // 删除开头和结尾的空白字符
        content = content.trim();

        return content;
    }

    /**
     * 将内容按最大长度进行分段，保留原始格式
     *
     * @param content   文本内容
     * @param maxLength 每段最大长度
     * @return 分段后的内容列表
     */
    private List<String> segmentContent(String content, int maxLength) {
        if (content == null || content.isEmpty()) {
            return List.of();
        }

        List<String> paragraphs = new ArrayList<>();

        // 如果内容长度小于最大长度，直接返回
        if (content.length() <= maxLength) {
            paragraphs.add(content);
            return paragraphs;
        }

        // 按自然段落分割（保留换行符）
        String[] naturalParagraphs = content.split("(?<=\n)");
        StringBuilder currentParagraph = new StringBuilder();

        for (String paragraph : naturalParagraphs) {
            // 如果当前自然段落加上已有内容超过最大长度，且当前段落不为空
            if (currentParagraph.length() + paragraph.length() > maxLength && currentParagraph.length() > 0) {
                // 添加当前段落到结果列表
                paragraphs.add(currentParagraph.toString());
                // 重置当前段落
                currentParagraph = new StringBuilder();
            }

            // 如果单个自然段落超过最大长度，需要进一步分割
            if (paragraph.length() > maxLength) {
                // 如果当前段落不为空，先保存
                if (currentParagraph.length() > 0) {
                    paragraphs.add(currentParagraph.toString());
                    currentParagraph = new StringBuilder();
                }

                // 按句子分割，但保留句子结束符和空白
                //String[] sentences = paragraph.split("(?<=([.!?。！？]\\s*))");
                String[] sentences = paragraph.split("([.!?。！？]\\s*)");
                StringBuilder sentenceBuffer = new StringBuilder();

                for (String sentence : sentences) {
                    if (sentenceBuffer.length() + sentence.length() > maxLength) {
                        if (sentenceBuffer.length() > 0) {
                            paragraphs.add(sentenceBuffer.toString());
                            sentenceBuffer = new StringBuilder();
                        }

                        // 如果单个句子超过最大长度，按字符分割
                        if (sentence.length() > maxLength) {
                            int start = 0;
                            while (start < sentence.length()) {
                                int end = Math.min(start + maxLength, sentence.length());
                                paragraphs.add(sentence.substring(start, end));
                                start = end;
                            }
                        } else {
                            sentenceBuffer.append(sentence);
                        }
                    } else {
                        sentenceBuffer.append(sentence);
                    }
                }

                if (sentenceBuffer.length() > 0) {
                    paragraphs.add(sentenceBuffer.toString());
                }
            } else {
                // 添加自然段落到当前段落
                currentParagraph.append(paragraph);
            }
        }

        // 添加最后一个段落
        if (currentParagraph.length() > 0) {
            paragraphs.add(currentParagraph.toString());
        }

        return paragraphs;
    }

    /**
     * 解析文档
     *
     * @param ossId
     * @param knowledgeId
     * @return
     */
    @Cacheable(cacheNames = "global:ai_chat_file_stru:#10m", key = "#ossId", condition = "#ossId != null")
    public Map<String, List<Map<String, String>>> getExcelRow(Long ossId, Long knowledgeId) {
        try {
            //获取文件信息
            SysOssVo sysOssVo = sysOssService.getById(ossId);
            //不是Excel文件，报错
            if (!ObjectUtil.equals(sysOssVo.getFileSuffix(), ".xlsx") && !ObjectUtil.equals(sysOssVo.getFileSuffix(), ".xls")) {
                throw new ServiceException("文件格式错误");
            }
            InputStream inputStream = sysOssService.getFileContent(sysOssVo);
            return readExcelToJson(inputStream, knowledgeId, sysOssVo.getFileSuffix());
        } catch (Exception e) {
            log.error("Excel文档转换失败" + e.getMessage());
            return null;
        }
    }

    /**
     * 读取 Excel 转为 JSON列表
     *
     * @param inputStream 输入流
     * @return 结构化json列表
     */
    public LinkedHashMap<String, List<Map<String, String>>> readExcelToJson(InputStream inputStream, Long knowledgeId, String fileSuffix) throws Exception {
        LinkedHashMap<String, List<Map<String, String>>> sheetMap = new LinkedHashMap<>();

        try {
            // 读取流数据到字节，以便多次使用
            byte[] bytes = inputStream.readAllBytes();
            if (bytes.length == 0) {
                throw new IOException("输入的 Excel 文件为空！");
            }
            InputStream sheetStream = new ByteArrayInputStream(bytes);

            // 获取所有 Sheet
            List<ReadSheet> sheets;
            if (ObjectUtil.equals(fileSuffix, ".xlsx")) {
                sheets = EasyExcel.read(sheetStream).excelType(ExcelTypeEnum.XLSX).build().excelExecutor().sheetList();
            } else {
                sheets = EasyExcel.read(sheetStream).excelType(ExcelTypeEnum.XLS).build().excelExecutor().sheetList();
            }

            // 使用多线程解析
//            CountDownLatch countDownLatch = new CountDownLatch(sheets.size());
            sheets.forEach(sheet -> {
//                threadPoolTaskExecutor.execute(() -> {
                if (sheet != null) {
                    // 每次新建流，保证可读取
                    InputStream dataStream = new ByteArrayInputStream(bytes);
                    List<Map<String, String>> dataMapList = new ArrayList<>();
                    String sheetName = sheet.getSheetName();
                    List<Map<Integer, String>> dataList;
                    if (ObjectUtil.equals(fileSuffix, ".xlsx")) {
                        dataList = EasyExcel.read(dataStream).excelType(ExcelTypeEnum.XLSX).sheet(sheet.getSheetNo()).headRowNumber(0).doReadSync();
                    } else {
                        dataList = EasyExcel.read(dataStream).excelType(ExcelTypeEnum.XLS).sheet(sheet.getSheetNo()).headRowNumber(0).doReadSync();
                    }

                    if (!dataList.isEmpty()) {
                        //绑定表头关系
                        Map<Integer, String> headers = bindHearders(dataList.get(0), knowledgeId);
                        for (int i = 1; i < dataList.size(); i++) {
                            Map<String, String> rowMap = new HashMap<>();
                            Map<Integer, String> row = dataList.get(i);
                            row.forEach((index, value) -> rowMap.put(headers.get(index), value));
                            dataMapList.add(rowMap);
                        }
                        sheetMap.put(sheetName, dataMapList);
                    }
                }
//                countDownLatch.countDown();
            });
//        );
//            countDownLatch.await();
        } catch (Exception e) {
            throw new Exception("读取 Excel 文件失败！", e);
        }
        return sheetMap;
    }

    /**
     * 处理并绑定表头关系
     *
     * @param integerStringMap 表头
     * @param knowledgeId      知识库id
     * @return
     */
    private Map<Integer, String> bindHearders(Map<Integer, String> integerStringMap, Long knowledgeId) {
        //获取知识库信息
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeId);
        String fieldMapper = knowledgeBase.getFieldMapper();
        JSONArray fieldMapperJson = new JSONArray();
        if (StringUtils.isNotEmpty(fieldMapper)) {
            fieldMapperJson = JSON.parseArray(fieldMapper);
        }
        // 获取当前 jsonArray 已存在的表头集合，避免重复插入
        Map<String, String> existingHeaders = new HashMap<>();
        int maxIndex = fieldMapperJson.size() + 1;

        // 遍历 jsonArray，提取现有的表头和最大索引
        for (int i = 0; i < fieldMapperJson.size(); i++) {
            JSONObject obj = fieldMapperJson.getJSONObject(i);
            for (String key : obj.keySet()) {
                existingHeaders.put(obj.getString(key), key);
            }
        }
        // 遍历 headers，插入新数据
        for (Map.Entry<Integer, String> entry : integerStringMap.entrySet()) {
            String value = entry.getValue();
            if (!existingHeaders.containsKey(value)) {
                //表头不存在
                maxIndex++;
                String newKey = "a" + maxIndex;
                JSONObject newObj = new JSONObject();
                newObj.put(newKey, value);
                fieldMapperJson.add(newObj);
                integerStringMap.put(entry.getKey(), newKey);
                existingHeaders.put(value, newKey);
            } else {
                // 如果表头已存在，直接使用已有的 aX
                integerStringMap.put(entry.getKey(), existingHeaders.get(value));
            }
        }
        //最后将知识库字段映射更新到数据库中
        LambdaUpdateWrapper<KnowledgeBase> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KnowledgeBase::getKnowledgeId, knowledgeId)
            .set(KnowledgeBase::getFieldMapper, fieldMapperJson.toJSONString());
        knowledgeBaseMapper.update(null, updateWrapper);
        return integerStringMap;
    }

    /**
     * 对Excel文档内容进行分片
     *
     * @param content
     * @return
     */
    @Cacheable(cacheNames = "global:ai_chat_file_split:#10m", key = "#ossId", condition = "#ossId != null")
    public List<String> fragmentation(Map<String, List<Map<String, String>>> content, Long ossId) {
         /*1 不同的sheet分为不同的片段
          2 同一个sheet中，按照行数分片
            低于1W行的不分片，
            1W - 5W 的平均分。
            超过5W的，按照总行数的五分之一分片。
            如果单片超过3W。对单片在进行上述分片操作
         */
        List<String> result = new ArrayList<>();

        for (Map.Entry<String, List<Map<String, String>>> entry : content.entrySet()) {
            List<Map<String, String>> rows = entry.getValue();
            int rowCount = rows.size();

            List<List<Map<String, String>>> partitions = partitionData(rows, rowCount);

            // 转换为 JSON 并存入结果列表
            for (List<Map<String, String>> part : partitions) {
                result.add(JSON.toJSONString(part));
            }
        }
        return result;
    }

    /**
     * 获取知识库字段映射
     *
     * @param fieldMapper
     * @param knowledgeBaseId
     * @return
     */
//    @Cacheable(cacheNames = "global:ai_chat_field_mapper:#10m", key = "#knowledgeBaseId", condition = "#knowledgeBaseId != null")
    public String getFieldMapper(String fieldMapper, String knowledgeBaseId) {
        //结构化数据需要单独传参映射关系
        JSONArray fieldMapperJson = JSON.parseArray(fieldMapper);
        //遍历并冲洗拼接字段映射关系
        StringBuilder fieldMapperStr = new StringBuilder();
        for (int i = 0; i < fieldMapperJson.size(); i++) {
            JSONObject jsonObject = fieldMapperJson.getJSONObject(i);
            for (String key : jsonObject.keySet()) {
                fieldMapperStr.append(key).append("[").append(jsonObject.getString(key)).append("]");
                if (i != fieldMapperJson.size() - 1) {
                    fieldMapperStr.append(",");
                }
            }
        }
        return fieldMapperStr.toString();
    }

    /**
     * 从数据库表中获取数据
     */
    @Cacheable(cacheNames = "global:ai_database_info:#10m", key = "#bo.databaseConfigId")
    public String cacheDatabase(KbDatabaseConfigBo bo) {
        switch (bo.getDatabaseType()){
            case KBDataBaseTypeConstants.MYSQL ->{
                return mySqlDatabase(bo);
            }
            default -> {
                log.error("该数据库类型暂未开放" + bo.getDatabaseType());
                throw new ServiceException("该数据库类型暂未开放");
            }
        }
    }

    /**
     * 连接mysql数据库获取库表信息
     * @param bo
     */
    private String mySqlDatabase(KbDatabaseConfigBo bo) {
        // 记录开始日志
        log.info("开始解析数据库表结构信息{}", bo.getDatabaseName());

        Map<String, String> tableSchemas = new HashMap<>();
        String url = StrUtil.format(KBDataBaseTypeConstants.Mysql_url, bo.getDatabaseUrl(), bo.getDatabasePort(), bo.getDatabaseName());
        Properties properties = new Properties();
        properties.setProperty("user", bo.getUsername());
        properties.setProperty("password", bo.getPassword());

        try (Connection connection = DriverManager.getConnection(url, properties)) {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet tables = metaData.getTables(bo.getDatabaseName(), null, "%", new String[]{"TABLE"})) {
                List<String> tableNames = new ArrayList<>();
                while (tables.next()) {
                    tableNames.add(tables.getString("TABLE_NAME"));
                }

                // 使用线程池进行多线程处理
                List<Future<Map.Entry<String, String>>> futures = new ArrayList<>();
                for (String tableName : tableNames) {
                    Future<Map.Entry<String, String>> future = threadPoolTaskExecutor.submit(() -> {
                        try {
                            String createTableSQL = getCreateTableSQLTwo(connection, tableName);
                            return new AbstractMap.SimpleEntry<>(tableName, createTableSQL);
                        } catch (SQLException e) {
                            log.error("获取表 {} 的创建 SQL 失败: {}", tableName, e.getMessage());
                            return new AbstractMap.SimpleEntry<>(tableName, "");
                        }
                    });
                    futures.add(future);
                }

                // 收集结果
                for (Future<Map.Entry<String, String>> future : futures) {
                    try {
                        Map.Entry<String, String> entry = future.get();
                        if (isValidSQL(entry.getValue())) {
                            tableSchemas.put("数据库表：" + entry.getKey(), "表信息：" + entry.getValue());
                        }
                    } catch (Exception e) {
                        log.error("获取表创建 SQL 时发生异常: {}", e.getMessage());
                    }
                }
            }
        } catch (SQLException e) {
            log.error("获取数据库表结构失败: {}", e.getMessage());
        }

        if (tableSchemas.isEmpty()) {
            log.warn("数据库中未找到任何表，数据库表: {}", bo.getDatabaseName());
            return "";
        }

        // 记录结束日志
        log.info("结束解析数据库表结构信息{}", bo.getDatabaseName());
        return JSON.toJSONString(tableSchemas);
    }

    private boolean isValidSQL(String sql) {
        return sql != null && !sql.trim().isEmpty();
    }


    private String getCreateTableSQL(Connection connection, String tableName) throws SQLException {
        log.info("========获取表数据开始==========");
        StringBuilder sql = new StringBuilder("CREATE TABLE " + tableName + " (");
        try (Statement stmt = connection.createStatement(); ResultSet rs = stmt.executeQuery("SHOW FULL COLUMNS FROM " + tableName)) {
            List<String> columnDefinitions = new ArrayList<>();
            while (rs.next()) {
                String columnName = rs.getString("Field");
                String columnType = rs.getString("Type");
                String isNullable = rs.getString("Null").equals("YES") ? "NULL" : "NOT NULL";
                String defaultValue = rs.getString("Default") != null ? " DEFAULT '" + rs.getString("Default") + "'" : "";
                String extra = rs.getString("Extra");
                String comment = rs.getString("Comment");
                columnDefinitions.add(columnName + " " + columnType + " " + isNullable + defaultValue + " " + extra + (comment.isEmpty() ? "" : " COMMENT '" + comment + "'"));
            }
            sql.append(String.join(", ", columnDefinitions));
        }

        // 获取表的注释
        String tableComment = "";
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW TABLE STATUS WHERE Name = '" + tableName + "'")) {
            if (rs.next()) {
                tableComment = rs.getString("Comment");
            }
        }

        if (!tableComment.isEmpty()) {
            sql.append(" COMMENT='").append(tableComment).append("'");
        }
        sql.append(");");
        return sql.toString();
    }

    private String getCreateTableSQLTwo(Connection connection, String tableName) throws SQLException {
        log.info("========获取表数据开始==========");
        StringBuilder sql = new StringBuilder("CREATE TABLE " + tableName + " (");
        try (Statement stmt = connection.createStatement(); ResultSet rs = stmt.executeQuery("SHOW FULL COLUMNS FROM " + tableName)) {
            List<String> columnDefinitions = new ArrayList<>();
            while (rs.next()) {
                String columnName = rs.getString("Field");
                String columnType = rs.getString("Type");
                String isNullable = rs.getString("Null").equals("YES") ? "NULL" : "NOT NULL";
                String defaultValue = rs.getString("Default") != null ? " DEFAULT '" + rs.getString("Default") + "'" : "";
                String extra = rs.getString("Extra");
                String comment = rs.getString("Comment");
                columnDefinitions.add(columnName + " " + columnType + " " + isNullable + defaultValue + " " + extra + (comment.isEmpty() ? "" : " COMMENT '" + comment + "'"));
            }
            sql.append(String.join(", ", columnDefinitions));
        }
        // 获取表的注释
        String tableComment = "";
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW TABLE STATUS WHERE Name = '" + tableName + "'")) {
            if (rs.next()) {
                tableComment = rs.getString("Comment");
            }
        }

        if (!tableComment.isEmpty()) {
            sql.append(" COMMENT='").append(tableComment).append("'");
        }
        sql.append(");");
        return sql.toString();
    }

    /**
     * 从Word文档中提取图片
     *
     * @param inputStream Word文件输入流
     * @param fileType 文件类型 (.doc或.docx)
     * @return 提取的图片列表
     */
    private List<BufferedImage> extractImagesFromWord(InputStream inputStream, String fileType) {
        List<BufferedImage> images = new ArrayList<>();
        try {
            org.apache.poi.hwpf.HWPFDocument docDocument = null;
            org.apache.poi.xwpf.usermodel.XWPFDocument docxDocument = null;

            if (fileType.equalsIgnoreCase(".doc")||fileType.equalsIgnoreCase(".wps")) {
                docDocument = new org.apache.poi.hwpf.HWPFDocument(inputStream);
                org.apache.poi.hwpf.model.PicturesTable picturesTable = docDocument.getPicturesTable();
                List<org.apache.poi.hwpf.usermodel.Picture> pictures = picturesTable.getAllPictures();

                for (org.apache.poi.hwpf.usermodel.Picture picture : pictures) {
                    byte[] data = picture.getContent();
                    ByteArrayInputStream bis = new ByteArrayInputStream(data);
                    BufferedImage image = ImageIO.read(bis);
                    if (image != null && image.getWidth() > 100 && image.getHeight() > 100) {
                        images.add(image);
                    }
                    bis.close();
                }
            } else if (fileType.equalsIgnoreCase(".docx") ) {
                docxDocument = new org.apache.poi.xwpf.usermodel.XWPFDocument(inputStream);
                List<org.apache.poi.xwpf.usermodel.XWPFPictureData> pictures = docxDocument.getAllPictures();

                for (org.apache.poi.xwpf.usermodel.XWPFPictureData picture : pictures) {
                    byte[] data = picture.getData();
                    ByteArrayInputStream bis = new ByteArrayInputStream(data);
                    BufferedImage image = ImageIO.read(bis);
                    if (image != null && image.getWidth() > 100 && image.getHeight() > 100) {
                        images.add(image);
                    }
                    bis.close();
                }
            }

            if (docDocument != null) {
                docDocument.close();
            }
            if (docxDocument != null) {
                docxDocument.close();
            }
        } catch (IOException e) {
            log.error("提取Word文档中的图片失败", e);
        }
        return images;
    }

    /**
     * 从PPT文档中提取图片
     *
     * @param inputStream PPT文件输入流
     * @param fileType 文件类型 (.ppt或.pptx)
     * @return 提取的图片列表
     */
    private List<BufferedImage> extractImagesFromPpt(InputStream inputStream, String fileType) {
        List<BufferedImage> images = new ArrayList<>();
        try {
            if (fileType.equalsIgnoreCase(".ppt")) {
                org.apache.poi.hslf.usermodel.HSLFSlideShow ppt = new org.apache.poi.hslf.usermodel.HSLFSlideShow(inputStream);
                List<org.apache.poi.hslf.usermodel.HSLFPictureData> pictures = ppt.getPictureData();

                for (org.apache.poi.hslf.usermodel.HSLFPictureData picture : pictures) {
                    byte[] data = picture.getData();
                    ByteArrayInputStream bis = new ByteArrayInputStream(data);
                    BufferedImage image = ImageIO.read(bis);
                    if (image != null && image.getWidth() > 100 && image.getHeight() > 100) {
                        images.add(image);
                    }
                    bis.close();
                }
                ppt.close();
            } else if (fileType.equalsIgnoreCase(".pptx")) {
                org.apache.poi.xslf.usermodel.XMLSlideShow pptx = new org.apache.poi.xslf.usermodel.XMLSlideShow(inputStream);
                List<org.apache.poi.xslf.usermodel.XSLFPictureData> pictures = pptx.getPictureData();

                for (org.apache.poi.xslf.usermodel.XSLFPictureData picture : pictures) {
                    byte[] data = picture.getData();
                    ByteArrayInputStream bis = new ByteArrayInputStream(data);
                    BufferedImage image = ImageIO.read(bis);
                    if (image != null && image.getWidth() > 100 && image.getHeight() > 100) {
                        images.add(image);
                    }
                    bis.close();
                }
                pptx.close();
            }
        } catch (IOException e) {
            log.error("提取PPT文档中的图片失败", e);
        }
        return images;
    }

}
