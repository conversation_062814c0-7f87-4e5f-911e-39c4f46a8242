package org.dromara.business.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.BsProjectAcceptStatisticsVo;
import org.dromara.business.domain.bo.BsProjectAcceptStatisticsBo;
import org.dromara.business.service.IBsProjectAcceptStatisticsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 施工图项目受理清单统计记录
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/projectAcceptStatistics")
public class BsProjectAcceptStatisticsController extends BaseController {

    private final IBsProjectAcceptStatisticsService bsProjectAcceptStatisticsService;

    /**
     * 查询施工图项目受理清单统计记录列表
     */
    @SaCheckPermission("business:projectAcceptStatistics:list")
    @GetMapping("/list")
    public TableDataInfo<BsProjectAcceptStatisticsVo> list(BsProjectAcceptStatisticsBo bo, PageQuery pageQuery) {
        return bsProjectAcceptStatisticsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出施工图项目受理清单统计记录列表
     */
    @SaCheckPermission("business:projectAcceptStatistics:export")
    @Log(title = "施工图项目受理清单统计记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BsProjectAcceptStatisticsBo bo, HttpServletResponse response) {
        List<BsProjectAcceptStatisticsVo> list = bsProjectAcceptStatisticsService.queryList(bo);
        ExcelUtil.exportExcel(list, "施工图项目受理清单统计记录", BsProjectAcceptStatisticsVo.class, response);
    }

    /**
     * 获取施工图项目受理清单统计记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:projectAcceptStatistics:query")
    @GetMapping("/{id}")
    public R<BsProjectAcceptStatisticsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bsProjectAcceptStatisticsService.queryById(id));
    }

    /**
     * 新增施工图项目受理清单统计记录
     */
    @SaCheckPermission("business:projectAcceptStatistics:add")
    @Log(title = "施工图项目受理清单统计记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BsProjectAcceptStatisticsBo bo) {
        return toAjax(bsProjectAcceptStatisticsService.insertByBo(bo));
    }

    /**
     * 修改施工图项目受理清单统计记录
     */
    @SaCheckPermission("business:projectAcceptStatistics:edit")
    @Log(title = "施工图项目受理清单统计记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BsProjectAcceptStatisticsBo bo) {
        return toAjax(bsProjectAcceptStatisticsService.updateByBo(bo));
    }

    /**
     * 删除施工图项目受理清单统计记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:projectAcceptStatistics:remove")
    @Log(title = "施工图项目受理清单统计记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bsProjectAcceptStatisticsService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 执行分析
     *
     * @param id 统计记录主键
     */
    @PostMapping("/analyze/{id}")
    public R<String> executeAnalysis(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        String fileName = bsProjectAcceptStatisticsService.executeAnalysis(id);
        return R.ok("分析完成，文件已生成: " + fileName);
    }
}
