package org.dromara.business.service;

import org.dromara.business.domain.AiModelChild;
import org.dromara.business.domain.vo.AiModelChildVo;
import org.dromara.business.domain.bo.AiModelChildBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 模型算力地址Service接口
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface IAiModelChildService {

    /**
     * 查询模型算力地址
     *
     * @param id 主键
     * @return 模型算力地址
     */
    AiModelChildVo queryById(Long id);

    /**
     * 分页查询模型算力地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模型算力地址分页列表
     */
    TableDataInfo<AiModelChildVo> queryPageList(AiModelChildBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的模型算力地址列表
     *
     * @param bo 查询条件
     * @return 模型算力地址列表
     */
    List<AiModelChildVo> queryList(AiModelChildBo bo);

    /**
     * 新增模型算力地址
     *
     * @param bo 模型算力地址
     * @return 是否新增成功
     */
    Boolean insertByBo(AiModelChildBo bo);

    /**
     * 修改模型算力地址
     *
     * @param bo 模型算力地址
     * @return 是否修改成功
     */
    Boolean updateByBo(AiModelChildBo bo);

    /**
     * 校验并批量删除模型算力地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<AiModelChild> selectListByModelId(Long id);

    List<AiModelChild> selectListByModelIds(List<Long> ids,String modelType);

    void saveChildUrl(List<AiModelChildBo> aiModelChildBoList,Long modelId);
}
