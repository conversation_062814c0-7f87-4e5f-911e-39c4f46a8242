package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 审查人员信息对象 bs_review_staff
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bs_review_staff")
public class BsReviewStaff extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 所属类目
     */
    private String categoryKey;

    /**
     * 所属机构
     */
    private String organizationKey;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
