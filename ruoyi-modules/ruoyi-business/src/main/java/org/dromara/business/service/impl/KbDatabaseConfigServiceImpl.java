package org.dromara.business.service.impl;

import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.domain.vo.KbDatabaseConfigVo;
import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.business.mapper.KbDatabaseConfigMapper;
import org.dromara.business.service.IKbDatabaseConfigService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 知识库-数据库配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RequiredArgsConstructor
@Service
public class KbDatabaseConfigServiceImpl implements IKbDatabaseConfigService {

    private final KbDatabaseConfigMapper baseMapper;
    private final KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 查询知识库-数据库配置信息
     *
     * @param databaseConfigId 主键
     * @return 知识库-数据库配置信息
     */
    @Override
    public KbDatabaseConfigVo queryById(Long databaseConfigId){
        return baseMapper.selectVoById(databaseConfigId);
    }

    /**
     * 分页查询知识库-数据库配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 知识库-数据库配置信息分页列表
     */
    @Override
    public TableDataInfo<KbDatabaseConfigVo> queryPageList(KbDatabaseConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<KbDatabaseConfig> lqw = buildQueryWrapper(bo);
        Page<KbDatabaseConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的知识库-数据库配置信息列表
     *
     * @param bo 查询条件
     * @return 知识库-数据库配置信息列表
     */
    @Override
    public List<KbDatabaseConfigVo> queryList(KbDatabaseConfigBo bo) {
        LambdaQueryWrapper<KbDatabaseConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<KbDatabaseConfig> buildQueryWrapper(KbDatabaseConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<KbDatabaseConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getKnowledgeId() != null, KbDatabaseConfig::getKnowledgeId, bo.getKnowledgeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDatabaseType()), KbDatabaseConfig::getDatabaseType, bo.getDatabaseType());
        lqw.eq(StringUtils.isNotBlank(bo.getDatabaseUrl()), KbDatabaseConfig::getDatabaseUrl, bo.getDatabaseUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getDatabasePort()), KbDatabaseConfig::getDatabasePort, bo.getDatabasePort());
        lqw.like(StringUtils.isNotBlank(bo.getDatabaseName()), KbDatabaseConfig::getDatabaseName, bo.getDatabaseName());
        lqw.like(StringUtils.isNotBlank(bo.getUsername()), KbDatabaseConfig::getUsername, bo.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), KbDatabaseConfig::getPassword, bo.getPassword());
        return lqw;
    }

    /**
     * 新增知识库-数据库配置信息
     *
     * @param bo 知识库-数据库配置信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(KbDatabaseConfigBo bo) {
        KbDatabaseConfig add = MapstructUtils.convert(bo, KbDatabaseConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDatabaseConfigId(add.getDatabaseConfigId());
        }
        return flag;
    }

    /**
     * 修改知识库-数据库配置信息
     *
     * @param bo 知识库-数据库配置信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(KbDatabaseConfigBo bo) {
        KbDatabaseConfig update = MapstructUtils.convert(bo, KbDatabaseConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(KbDatabaseConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除知识库-数据库配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<KbDatabaseConfig> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据知识库id查询知识库-数据库配置信息
     * @param knowledgeId
     * @return
     */
    @Override
    public KbDatabaseConfigVo getInfoById(Long knowledgeId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("请登录");
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(KbDatabaseConfig.class).eq(KbDatabaseConfig::getCreateBy, loginUser.getUserId())
            .eq(KbDatabaseConfig::getKnowledgeId, knowledgeId).eq(KbDatabaseConfig::getDelFlag, 0)).stream().findFirst().orElse(null);
    }
}
