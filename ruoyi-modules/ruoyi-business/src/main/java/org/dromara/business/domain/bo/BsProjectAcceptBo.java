package org.dromara.business.domain.bo;

import org.dromara.business.domain.BsProjectAccept;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 施工图项目受理清单业务对象 bs_project_accept
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BsProjectAccept.class, reverseConvertGenerate = false)
public class BsProjectAcceptBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 县区
     */
    @NotBlank(message = "县区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String district;

    /**
     * 图审机构
     */
    @NotBlank(message = "图审机构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String drawingReviewAgency;

    /**
     * 政审机构
     */
    @NotBlank(message = "政审机构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String politicalReviewAgency;

    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectNumber;

    /**
     * 建设单位
     */
    @NotBlank(message = "建设单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String constructionCompany;

    /**
     * 设计单位
     */
    @NotBlank(message = "设计单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String designCompany;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectName;

    /**
     * 建筑类型
     */
    @NotBlank(message = "建筑类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingType;

    /**
     * 建筑面积（㎡）
     */
    @NotNull(message = "建筑面积（㎡）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal constructionAreaSqm;

    /**
     * 规划面积（㎡）
     */
    @NotNull(message = "规划面积（㎡）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal plannedAreaSqm;

    /**
     * 机构遴选日期
     */
    @NotNull(message = "机构遴选日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date agencySelectionDate;

    /**
     * 是否自费
     */
    @NotBlank(message = "是否自费不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isSelfFunded;

    /**
     * 合格书盖章日期
     */
    @NotNull(message = "合格书盖章日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date qualifiedStampDate;


}
