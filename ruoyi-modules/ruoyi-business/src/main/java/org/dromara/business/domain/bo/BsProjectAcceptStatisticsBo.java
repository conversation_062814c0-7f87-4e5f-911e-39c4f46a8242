package org.dromara.business.domain.bo;

import org.dromara.business.domain.BsProjectAcceptStatistics;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 施工图项目受理清单统计记录业务对象 bs_project_accept_statistics
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BsProjectAcceptStatistics.class, reverseConvertGenerate = false)
public class BsProjectAcceptStatisticsBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 统计起始时间
     */
    @NotNull(message = "统计起始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 统计结束日期
     */
    @NotNull(message = "统计结束日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 统计状态0分析中 1分析完成
     */
    private String status;

    /**
     * 统计报表文件ID
     */
    private Long ossId;


}
