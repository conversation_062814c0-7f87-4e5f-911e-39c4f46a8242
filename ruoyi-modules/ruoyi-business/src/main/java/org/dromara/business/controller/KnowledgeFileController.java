package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.bo.KnowledgeBaseBo;
import org.dromara.business.domain.bo.KnowledgeFileBo;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.domain.vo.KnowledgeFileVo;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.service.IKnowledgeFileService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库文件控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/knowledge/file")
@Slf4j
public class KnowledgeFileController extends BaseController {

    private final IKnowledgeFileService knowledgeFileService;
    private final IKnowledgeBaseService knowledgeBaseService;

    /**
     * 查询知识库文件列表
     */
    @GetMapping("/list")
    public TableDataInfo<KnowledgeFileVo> list(KnowledgeFileBo bo, PageQuery pageQuery) {
        return knowledgeFileService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取知识库文件详细信息
     */
    @GetMapping("/{fileId}")
    public R<KnowledgeFileVo> getInfo(@PathVariable Long fileId) {
        return R.ok(knowledgeFileService.queryById(fileId));
    }

    /**
     * 根据知识库ID查询文件列表
     */
    @GetMapping("/list/{knowledgeId}")
    public R<List<KnowledgeFileVo>> listByKnowledgeId(@PathVariable Long knowledgeId) {
        return R.ok(knowledgeFileService.queryListByKnowledgeId(knowledgeId));
    }

    /**
     * 上传文件到知识库
     */
    @Log(title = "知识库文件管理", businessType = BusinessType.INSERT)
    @PostMapping("/upload/{knowledgeId}/{ossId}")
    public R<Void> upload(@PathVariable Long knowledgeId, @PathVariable Long ossId, @RequestBody(required = false) String fileDescription) {
        // 记录获取知识库信息开始时间
        long startTime = System.currentTimeMillis();
        // 获取知识库信息
        KnowledgeBaseVo knowledgeBaseVo = knowledgeBaseService.queryById(knowledgeId);
        // 记录获取知识库信息结束时间
        long endTime = System.currentTimeMillis();
        // 计算获取知识库信息耗时
        long elapsedTime = endTime - startTime;
        log.info("获取知识库信息耗时: {} 毫秒", elapsedTime);
        if (knowledgeBaseVo == null) {
            return R.fail("知识库不存在");
        }
        //判断知识库如果是数据库直连模式，则不允许修改为文件上传模式
        if (knowledgeBaseVo.getLearningStyle() ==2) {
            return R.fail("学习方式不匹配，无法学习");
        }

        try {
            // 根据知识库数据类型调用不同的上传方法
            boolean result;
            if (StringUtils.equals(knowledgeBaseVo.getDataType(), "0")) {
                // 结构化数据
                result = knowledgeFileService.uploadStructuredFile(knowledgeId, ossId, fileDescription);
            } else {
                // 非结构化数据（默认）
                result = knowledgeFileService.uploadUnstructuredFile(knowledgeId, ossId, fileDescription);
            }
            //修改知识库学习方式
            if(knowledgeBaseVo.getLearningStyle() == 0){
                KnowledgeBaseBo knowledgeBaseBo = new KnowledgeBaseBo();
                knowledgeBaseBo.setKnowledgeId(knowledgeId);
                knowledgeBaseBo.setLearningStyle(1);
                knowledgeBaseService.updateByBo(knowledgeBaseBo);
            }

            return toAjax(result);
        } catch (Exception e) {
            // 捕获异常并返回错误信息
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除知识库文件
     */
    @Log(title = "知识库文件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fileIds}")
    public R<Void> remove(@PathVariable Long[] fileIds) {
        return toAjax(knowledgeFileService.deleteWithValidByIds(List.of(fileIds)));
    }
}
