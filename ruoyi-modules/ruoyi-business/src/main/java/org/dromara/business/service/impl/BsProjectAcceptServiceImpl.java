package org.dromara.business.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.BsProjectAccept;
import org.dromara.business.domain.bo.BsProjectAcceptBo;
import org.dromara.business.domain.vo.BsProjectAcceptVo;
import org.dromara.business.mapper.BsProjectAcceptMapper;
import org.dromara.business.service.IBsProjectAcceptService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 施工图项目受理清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BsProjectAcceptServiceImpl implements IBsProjectAcceptService {

    private final BsProjectAcceptMapper baseMapper;

    /**
     * 查询施工图项目受理清单
     *
     * @param id 主键
     * @return 施工图项目受理清单
     */
    @Override
    public BsProjectAcceptVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询施工图项目受理清单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 施工图项目受理清单分页列表
     */
    @Override
    public TableDataInfo<BsProjectAcceptVo> queryPageList(BsProjectAcceptBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsProjectAccept> lqw = buildQueryWrapper(bo);
        Page<BsProjectAcceptVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的施工图项目受理清单列表
     *
     * @param bo 查询条件
     * @return 施工图项目受理清单列表
     */
    @Override
    public List<BsProjectAcceptVo> queryList(BsProjectAcceptBo bo) {
        LambdaQueryWrapper<BsProjectAccept> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsProjectAccept> buildQueryWrapper(BsProjectAcceptBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsProjectAccept> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDistrict()), BsProjectAccept::getDistrict, bo.getDistrict());
        lqw.eq(StringUtils.isNotBlank(bo.getDrawingReviewAgency()), BsProjectAccept::getDrawingReviewAgency, bo.getDrawingReviewAgency());
        lqw.eq(StringUtils.isNotBlank(bo.getPoliticalReviewAgency()), BsProjectAccept::getPoliticalReviewAgency, bo.getPoliticalReviewAgency());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectNumber()), BsProjectAccept::getProjectNumber, bo.getProjectNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getConstructionCompany()), BsProjectAccept::getConstructionCompany, bo.getConstructionCompany());
        lqw.eq(StringUtils.isNotBlank(bo.getDesignCompany()), BsProjectAccept::getDesignCompany, bo.getDesignCompany());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), BsProjectAccept::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getBuildingType()), BsProjectAccept::getBuildingType, bo.getBuildingType());
        lqw.eq(bo.getConstructionAreaSqm() != null, BsProjectAccept::getConstructionAreaSqm, bo.getConstructionAreaSqm());
        lqw.eq(bo.getPlannedAreaSqm() != null, BsProjectAccept::getPlannedAreaSqm, bo.getPlannedAreaSqm());
        lqw.eq(bo.getAgencySelectionDate() != null, BsProjectAccept::getAgencySelectionDate, bo.getAgencySelectionDate());
        lqw.eq(StringUtils.isNotBlank(bo.getIsSelfFunded()), BsProjectAccept::getIsSelfFunded, bo.getIsSelfFunded());
        lqw.eq(bo.getQualifiedStampDate() != null, BsProjectAccept::getQualifiedStampDate, bo.getQualifiedStampDate());
        return lqw;
    }

    /**
     * 新增施工图项目受理清单
     *
     * @param bo 施工图项目受理清单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsProjectAcceptBo bo) {
        BsProjectAccept add = MapstructUtils.convert(bo, BsProjectAccept.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改施工图项目受理清单
     *
     * @param bo 施工图项目受理清单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsProjectAcceptBo bo) {
        BsProjectAccept update = MapstructUtils.convert(bo, BsProjectAccept.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsProjectAccept entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除施工图项目受理清单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsProjectAccept> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    // ==================== 导入相关功能 ====================

    /**
     * 导入进度信息类
     */
    public static class ImportProgressInfo {
        private int percentage;
        private String status;
        private String message;
        private Long totalRows;
        private Long processedRows;
        private Long importedCount;
        private LocalDateTime startTime;
        private LocalDateTime endTime;

        public ImportProgressInfo() {
            this.startTime = LocalDateTime.now();
            this.percentage = 0;
            this.status = "processing";
            this.message = "开始处理...";
        }

        // Getters and Setters
        public int getPercentage() {
            return percentage;
        }

        public void setPercentage(int percentage) {
            this.percentage = percentage;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Long getTotalRows() {
            return totalRows;
        }

        public void setTotalRows(Long totalRows) {
            this.totalRows = totalRows;
        }

        public Long getProcessedRows() {
            return processedRows;
        }

        public void setProcessedRows(Long processedRows) {
            this.processedRows = processedRows;
        }

        public Long getImportedCount() {
            return importedCount;
        }

        public void setImportedCount(Long importedCount) {
            this.importedCount = importedCount;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }
    }

    // 进度缓存
    private static final Map<String, ImportProgressInfo> progressCache = new ConcurrentHashMap<>();

    // 定时清理缓存的调度器
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    static {
        // 每小时清理一次过期的进度信息（超过2小时的）
        scheduler.scheduleAtFixedRate(() -> {
            LocalDateTime cutoff = LocalDateTime.now().minusHours(2);
            progressCache.entrySet().removeIf(entry -> {
                ImportProgressInfo info = entry.getValue();
                return info.getStartTime().isBefore(cutoff);
            });
        }, 1, 1, TimeUnit.HOURS);
    }

    @Override
    public void initImportProgressInController(String importNo) {
        ImportProgressInfo progressInfo = new ImportProgressInfo();
        progressInfo.setPercentage(0);
        progressInfo.setStatus("processing");
        progressInfo.setMessage("初始化导入进度...");
        progressCache.put(importNo, progressInfo);
        log.info("初始化导入进度，导入流水号: {}", importNo);
    }

    @Override
    public Map<String, Object> getImportProgress(String importNo) {
        ImportProgressInfo progressInfo = progressCache.get(importNo);
        Map<String, Object> result = new HashMap<>();

        if (progressInfo == null) {
            result.put("percentage", 0);
            result.put("status", "not_found");
            result.put("message", "未找到导入进度信息");
        } else {
            result.put("percentage", progressInfo.getPercentage());
            result.put("status", progressInfo.getStatus());
            result.put("message", progressInfo.getMessage());
            result.put("totalRows", progressInfo.getTotalRows());
            result.put("processedRows", progressInfo.getProcessedRows());
            result.put("importedCount", progressInfo.getImportedCount());
            result.put("startTime", progressInfo.getStartTime());
            result.put("endTime", progressInfo.getEndTime());
        }

        return result;
    }

    @Override
    public void updateImportProgressFromListener(String importNo, int percentage, String message) {
        ImportProgressInfo progressInfo = progressCache.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(percentage);
            progressInfo.setMessage(message);
        }
    }

    @Override
    public void updateImportProgressWithRowsFromListener(String importNo, int percentage, String message, Long totalRows, Long processedRows) {
        ImportProgressInfo progressInfo = progressCache.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(percentage);
            progressInfo.setMessage(message);
            progressInfo.setTotalRows(totalRows);
            progressInfo.setProcessedRows(processedRows);
        }
    }

    private void completeImportProgress(String importNo, Long importedCount) {
        ImportProgressInfo progressInfo = progressCache.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(100);
            progressInfo.setStatus("completed");
            progressInfo.setMessage("导入完成");
            progressInfo.setImportedCount(importedCount);
            progressInfo.setEndTime(LocalDateTime.now());

            // 如果没有设置总行数，使用导入数量作为总行数
            if (progressInfo.getTotalRows() == null) {
                progressInfo.setTotalRows(importedCount);
            }
            if (progressInfo.getProcessedRows() == null) {
                progressInfo.setProcessedRows(importedCount);
            }
        }
    }

    private void failImportProgress(String importNo, String errorMessage) {
        ImportProgressInfo progressInfo = progressCache.get(importNo);
        if (progressInfo != null) {
            progressInfo.setStatus("failed");
            progressInfo.setMessage("导入失败: " + errorMessage);
            progressInfo.setEndTime(LocalDateTime.now());
        }
    }

    @Async
    @Override
    public void importProjectAcceptAsync(MultipartFile file, String importNo) {
        initImportProgress(importNo);
        processImportProjectAccept(file, importNo);
    }

    @Async
    @Override
    public void importProjectAcceptAsyncWithoutInit(MultipartFile file, String importNo) {
        processImportProjectAccept(file, importNo);
    }

    private void initImportProgress(String importNo) {
        ImportProgressInfo progressInfo = new ImportProgressInfo();
        progressInfo.setPercentage(5);
        progressInfo.setStatus("processing");
        progressInfo.setMessage("开始处理导入文件...");
        progressCache.put(importNo, progressInfo);
        log.info("初始化导入进度，导入流水号: {}", importNo);
    }

    private void processImportProjectAccept(MultipartFile file, String importNo) {
        try {
            if (file == null || file.isEmpty()) {
                failImportProgress(importNo, "文件为空");
                return;
            }

            updateImportProgressFromListener(importNo, 10, "开始解析Excel文件...");

            List<BsProjectAcceptVo> dataList = new ArrayList<>();
            ProjectAcceptImportListener listener = new ProjectAcceptImportListener(dataList, importNo, this);

            // 使用EasyExcel读取文件
            EasyExcel.read(file.getInputStream(), BsProjectAcceptVo.class, listener).sheet().doRead();

            updateImportProgressFromListener(importNo, 70, "Excel解析完成，开始处理数据...");

            if (dataList.isEmpty()) {
                failImportProgress(importNo, "Excel文件中没有有效数据");
                return;
            }

            // 处理重复数据：根据编号删除历史数据
            processDuplicateData(dataList, importNo);

            updateImportProgressFromListener(importNo, 90, "开始保存数据到数据库...");

            // 批量插入数据
            List<BsProjectAccept> entityList = dataList.stream()
                .map(vo -> MapstructUtils.convert(vo, BsProjectAccept.class))
                .collect(Collectors.toList());

            // 使用MyBatis-Plus的批量插入
            boolean success = baseMapper.insertBatch(entityList);

            if (success) {
                completeImportProgress(importNo, (long) entityList.size());
                log.info("导入完成，导入流水号: {}，导入数量: {}", importNo, entityList.size());
            } else {
                failImportProgress(importNo, "数据保存失败");
            }

        } catch (IOException e) {
            log.error("导入文件读取失败，导入流水号: {}", importNo, e);
            failImportProgress(importNo, "文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导入处理异常，导入流水号: {}", importNo, e);
            failImportProgress(importNo, "处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理重复数据：根据编号删除历史数据
     */
    private void processDuplicateData(List<BsProjectAcceptVo> dataList, String importNo) {
        updateImportProgressFromListener(importNo, 75, "处理重复数据...");

        // 提取所有编号
        Set<String> projectNumbers = dataList.stream()
            .map(BsProjectAcceptVo::getProjectNumber)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());

        if (!projectNumbers.isEmpty()) {
            // 根据编号删除历史数据
            LambdaQueryWrapper<BsProjectAccept> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.in(BsProjectAccept::getProjectNumber, projectNumbers);

            int deletedCount = baseMapper.delete(deleteWrapper);
            log.info("删除历史数据，导入流水号: {}，删除数量: {}", importNo, deletedCount);

            updateImportProgressFromListener(importNo, 80, String.format("已删除%d条历史数据", deletedCount));
        }
    }

    /**
     * 项目受理清单导入监听器
     */
    public static class ProjectAcceptImportListener implements ReadListener<BsProjectAcceptVo> {
        private final List<BsProjectAcceptVo> dataList;
        private final String importNo;
        private final BsProjectAcceptServiceImpl service;
        private int currentRow = 0;

        public ProjectAcceptImportListener(List<BsProjectAcceptVo> dataList, String importNo, BsProjectAcceptServiceImpl service) {
            this.dataList = dataList;
            this.importNo = importNo;
            this.service = service;
        }

        @Override
        public void invoke(BsProjectAcceptVo data, AnalysisContext context) {
            currentRow++;

            // 基本数据验证
            if (StringUtils.isNotBlank(data.getProjectNumber()) || StringUtils.isNotBlank(data.getProjectName())) {
                dataList.add(data);
            }

            // 每100行更新一次进度
            if (currentRow % 100 == 0) {
                int percentage = Math.min(60, 20 + (currentRow * 40 / Math.max(currentRow + 100, 1000)));
                service.updateImportProgressFromListener(importNo, percentage, String.format("已解析%d行数据", currentRow));
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            service.updateImportProgressFromListener(importNo, 65, String.format("Excel解析完成，共解析%d行有效数据", dataList.size()));
        }
    }

    @Override
    public void importProjectAcceptSync(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件为空");
        }

        try {
            List<BsProjectAcceptVo> dataList = new ArrayList<>();
            SyncProjectAcceptImportListener listener = new SyncProjectAcceptImportListener(dataList);

            // 使用EasyExcel读取文件
            EasyExcel.read(file.getInputStream(), BsProjectAcceptVo.class, listener).sheet().doRead();

            if (dataList.isEmpty()) {
                throw new ServiceException("Excel文件中没有有效数据");
            }

            // 处理重复数据：根据编号删除历史数据
            processDuplicateDataSync(dataList);

            // 批量插入数据
            List<BsProjectAccept> entityList = dataList.stream()
                .map(vo -> MapstructUtils.convert(vo, BsProjectAccept.class))
                .collect(Collectors.toList());

            // 使用MyBatis-Plus的批量插入
            boolean success = baseMapper.insertBatch(entityList);

            if (!success) {
                throw new ServiceException("数据保存失败");
            }

            log.info("同步导入完成，导入数量: {}", entityList.size());

        } catch (IOException e) {
            log.error("导入文件读取失败", e);
            throw new ServiceException("文件读取失败: " + e.getMessage());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导入处理异常", e);
            throw new ServiceException("处理异常: " + e.getMessage());
        }
    }

    /**
     * 同步处理重复数据：根据编号删除历史数据
     */
    private void processDuplicateDataSync(List<BsProjectAcceptVo> dataList) {
        // 提取所有编号
        Set<String> projectNumbers = dataList.stream()
            .map(BsProjectAcceptVo::getProjectNumber)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());

        if (!projectNumbers.isEmpty()) {
            // 根据编号删除历史数据
            LambdaQueryWrapper<BsProjectAccept> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.in(BsProjectAccept::getProjectNumber, projectNumbers);

            int deletedCount = baseMapper.delete(deleteWrapper);
            log.info("删除历史数据，删除数量: {}", deletedCount);
        }
    }

    /**
     * 同步项目受理清单导入监听器
     */
    public static class SyncProjectAcceptImportListener implements ReadListener<BsProjectAcceptVo> {
        private final List<BsProjectAcceptVo> dataList;

        public SyncProjectAcceptImportListener(List<BsProjectAcceptVo> dataList) {
            this.dataList = dataList;
        }

        @Override
        public void invoke(BsProjectAcceptVo data, AnalysisContext context) {
            // 基本数据验证
            if (StringUtils.isNotBlank(data.getProjectNumber()) || StringUtils.isNotBlank(data.getProjectName())) {
                dataList.add(data);
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 同步处理，无需额外操作
        }
    }
}
