package org.dromara.business.service;

import org.dromara.business.domain.bo.KnowledgeUserRelBo;
import org.dromara.business.domain.vo.KnowledgeUserRelVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 知识库用户关联服务接口
 */
public interface IKnowledgeUserRelService {

    /**
     * 查询知识库用户关联
     *
     * @param relId 关联主键
     * @return 知识库用户关联
     */
    KnowledgeUserRelVo queryById(Long relId);

    /**
     * 查询知识库用户关联列表
     *
     * @param bo 知识库用户关联
     * @return 知识库用户关联集合
     */
    TableDataInfo<KnowledgeUserRelVo> queryPageList(KnowledgeUserRelBo bo, PageQuery pageQuery);

    /**
     * 查询知识库用户关联列表
     *
     * @param bo 知识库用户关联
     * @return 知识库用户关联集合
     */
    List<KnowledgeUserRelVo> queryList(KnowledgeUserRelBo bo);

    /**
     * 新增知识库用户关联
     *
     * @param bo 知识库用户关联
     * @return 结果
     */
    Boolean insertByBo(KnowledgeUserRelBo bo);

    /**
     * 修改知识库用户关联
     *
     * @param bo 知识库用户关联
     * @return 结果
     */
    Boolean updateByBo(KnowledgeUserRelBo bo);

    /**
     * 校验并批量删除知识库用户关联信息
     *
     * @param ids 需要删除的知识库用户关联主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 根据知识库ID查询用户关联列表
     *
     * @param knowledgeId 知识库ID
     * @return 知识库用户关联集合
     */
    List<KnowledgeUserRelVo> queryListByKnowledgeId(Long knowledgeId);

    /**
     * 根据用户ID查询知识库关联列表
     *
     * @param userId 用户ID
     * @return 知识库用户关联集合
     */
    List<KnowledgeUserRelVo> queryListByUserId(Long userId);

    /**
     * 批量添加知识库用户关联
     *
     * @param knowledgeId 知识库ID
     * @param userIds 用户ID集合
     * @param permissionType 权限类型
     * @return 结果
     */
    Boolean batchAddUsers(Long knowledgeId, Collection<Long> userIds, String permissionType);

    /**
     * 批量删除知识库用户关联
     *
     * @param knowledgeId 知识库ID
     * @param userIds 用户ID集合
     * @return 结果
     */
    Boolean batchDeleteUsers(Long knowledgeId, Collection<Long> userIds);

    /**
     * 检查用户是否有权限访问知识库
     *
     * @param knowledgeId 知识库ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    Boolean checkPermission(Long knowledgeId, Long userId);

    /**
     * 检查用户是否有编辑权限
     *
     * @param knowledgeId 知识库ID
     * @param userId 用户ID
     * @return 是否有编辑权限
     */
    Boolean checkEditPermission(Long knowledgeId, Long userId);

}
