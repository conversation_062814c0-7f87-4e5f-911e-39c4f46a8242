package org.dromara.business.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 大模型配置对象 ai_model
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_model")
public class AiModel extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 知识库编号
     */
    private Long knowledgeId;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 单条回复的最大 Token 数量
     */
    private Long maxTokens;

    /**
     * 上下文的最大 Message 数量
     */
    private Long maxContexts;

    /**
     * 开关状态（0-关闭，1-开启）
     */
    private Integer status;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

    /**
     * 模型描述
     */
    private String remark;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 模型类型
     */
    private String modelType;


}
