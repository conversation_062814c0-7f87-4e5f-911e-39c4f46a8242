package org.dromara.business.domain.vo;

import org.dromara.business.domain.AiModel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 大模型配置视图对象 ai_model
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiModel.class)
public class AiModelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型id
     */
    @ExcelProperty(value = "模型id")
    private Long id;

    /**
     * 模型编码
     */
    @ExcelProperty(value = "模型编码")
    private String modelCode;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称")
    private String modelName;

    /**
     * 知识库编号
     */
    @ExcelProperty(value = "知识库编号")
    private Long knowledgeId;

    /**
     * 温度参数
     */
    @ExcelProperty(value = "温度参数")
    private Double temperature;

    /**
     * 单条回复的最大 Token 数量
     */
    @ExcelProperty(value = "单条回复的最大 Token 数量")
    private Long maxTokens;

    /**
     * 上下文的最大 Message 数量
     */
    @ExcelProperty(value = "上下文的最大 Message 数量")
    private Long maxContexts;

    /**
     * 开关状态（0-关闭，1-开启）
     */
    @ExcelProperty(value = "开关状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-关闭，1-开启")
    private Integer status;

    /**
     * 模型描述
     */
    private String remark;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 模型负载地址数量
     */
    private String modelUrlCount;

    /**
     * 模型类型
     */
    private String modelType;
}
