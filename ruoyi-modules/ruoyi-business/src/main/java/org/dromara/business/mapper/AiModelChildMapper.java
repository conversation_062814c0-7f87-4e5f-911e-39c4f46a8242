package org.dromara.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.domain.bo.AiModelChildBo;
import org.dromara.business.domain.vo.AiModelChildVo;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * 模型算力地址Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface AiModelChildMapper extends BaseMapperPlus<AiModelChild, AiModelChildVo> {

    @Select("SELECT * FROM ai_model_child WHERE model_id = #{id} AND del_flag = 0")
    List<AiModelChild> selectListByModelId(@Param("id") Long id);

    @Update("UPDATE ai_model_child SET del_flag = 1 WHERE model_id = #{modelId}")
    void deleteByModelId(Long modelId);

    Page<AiModelChildVo> queryPageList(@Param("page") Page<Object> build, @Param("bo") AiModelChildBo bo);
}
