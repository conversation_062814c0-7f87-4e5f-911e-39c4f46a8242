package org.dromara.business.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * AI 聊天对话业务对象 ai_chat_conversation
 *
 * <AUTHOR>
 * @date 2025-01-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AiChatConversationBo extends BaseEntity {

    public static final String TITLE_DEFAULT = "新对话";

    /**
     * 对话编号
     */
    @NotNull(message = "对话编号不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 知识库编号
     */
    private Long knowledgeId;

    /**
     * 对话标题
     */
    @NotBlank(message = "对话标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 是否置顶
     */
    @NotNull(message = "是否置顶不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer pinned;

    /**
     * 置顶时间
     */
    @NotNull(message = "置顶时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date pinnedTime;

    /*
     * 角色设定
     */
    @NotBlank(message = "角色设定不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemMessage;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 单条回复的最大 Token 数量
     */
    private Long maxTokens;

    /**
     * 上下文的最大 Message 数量
     */
    @NotNull(message = "上下文的最大 Message 数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer maxContexts;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 使用项目
     */
    private String project;

    /**
     * 使用单位
     */
    private String userCom;

    /**
     * 浏览器标志
     */
    private String browserId;

}
