package org.dromara.business.controller;

import org.dromara.business.domain.bo.KnowledgeShareBo;
import org.dromara.business.domain.vo.KnowledgeShareVo;
import org.dromara.business.utils.KnowledgeShareUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库分享控制器
 */
@RestController
@RequestMapping("/business/knowledge/share")
public class KnowledgeShareController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(KnowledgeShareController.class);

    /**
     * 生成知识库分享链接
     */
    @Log(title = "知识库分享", businessType = BusinessType.INSERT)
    @PostMapping
    public R<KnowledgeShareVo> share(@Validated(AddGroup.class) @RequestBody KnowledgeShareBo bo) {
        try {
            log.info("开始生成知识库分享链接，参数：{}", bo);
            
            if (bo == null) {
                log.error("请求参数为空");
                return R.fail("请求参数不能为空");
            }
            
            // 获取当前用户ID
            Long userId = LoginHelper.getUserId();
            log.info("当前用户ID: {}", userId);
            
            // 生成包含用户ID的分享链接
            String shareUrl = KnowledgeShareUtils.generateShareUrl(
                bo.getPagePath(), 
                bo.getKnowledgeId(),
                userId.toString()
            );
            
            if (shareUrl == null) {
                log.error("生成分享链接失败，页面路径：{}，知识库ID：{}，用户ID：{}", 
                    bo.getPagePath(), bo.getKnowledgeId(), userId);
                return R.fail("生成分享链接失败，请检查参数是否正确");
            }
            
            // 加密用户ID（保留，可用于其他场景）
            String encryptedUserId = KnowledgeShareUtils.encryptKnowledgeId(userId.toString());
            
            KnowledgeShareVo vo = new KnowledgeShareVo();
            vo.setShareUrl(shareUrl);
            vo.setEncryptedUserId(encryptedUserId);
            
            log.info("知识库分享链接生成成功，分享链接：{}", shareUrl);
            return R.ok(vo);
        } catch (Exception e) {
            log.error("生成知识库分享链接异常，参数：{}，错误信息：{}", bo, e.getMessage(), e);
            return R.fail("生成分享链接时发生异常：" + e.getMessage());
        }
    }
} 