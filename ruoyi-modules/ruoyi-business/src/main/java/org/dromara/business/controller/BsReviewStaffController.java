package org.dromara.business.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.BsReviewStaffVo;
import org.dromara.business.domain.bo.BsReviewStaffBo;
import org.dromara.business.service.IBsReviewStaffService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 审查人员信息
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/reviewStaff")
public class BsReviewStaffController extends BaseController {

    private final IBsReviewStaffService bsReviewStaffService;

    /**
     * 查询审查人员信息列表
     */
    @SaCheckPermission("business:reviewStaff:list")
    @GetMapping("/list")
    public TableDataInfo<BsReviewStaffVo> list(BsReviewStaffBo bo, PageQuery pageQuery) {
        return bsReviewStaffService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出审查人员信息列表
     */
    @SaCheckPermission("business:reviewStaff:export")
    @Log(title = "审查人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BsReviewStaffBo bo, HttpServletResponse response) {
        List<BsReviewStaffVo> list = bsReviewStaffService.queryList(bo);
        ExcelUtil.exportExcel(list, "审查人员信息", BsReviewStaffVo.class, response);
    }

    /**
     * 获取审查人员信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:reviewStaff:query")
    @GetMapping("/{id}")
    public R<BsReviewStaffVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bsReviewStaffService.queryById(id));
    }

    /**
     * 新增审查人员信息
     */
    @SaCheckPermission("business:reviewStaff:add")
    @Log(title = "审查人员信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BsReviewStaffBo bo) {
        return toAjax(bsReviewStaffService.insertByBo(bo));
    }

    /**
     * 修改审查人员信息
     */
    @SaCheckPermission("business:reviewStaff:edit")
    @Log(title = "审查人员信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BsReviewStaffBo bo) {
        return toAjax(bsReviewStaffService.updateByBo(bo));
    }

    /**
     * 删除审查人员信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:reviewStaff:remove")
    @Log(title = "审查人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bsReviewStaffService.deleteWithValidByIds(List.of(ids), true));
    }
}
