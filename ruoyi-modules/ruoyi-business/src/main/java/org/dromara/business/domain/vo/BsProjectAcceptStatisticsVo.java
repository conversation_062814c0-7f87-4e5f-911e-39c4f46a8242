package org.dromara.business.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.business.domain.BsProjectAcceptStatistics;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 施工图项目受理清单统计记录视图对象 bs_project_accept_statistics
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BsProjectAcceptStatistics.class)
public class BsProjectAcceptStatisticsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 统计起始时间
     */
    @ExcelProperty(value = "统计起始时间")
    private Date startDate;

    /**
     * 统计结束日期
     */
    @ExcelProperty(value = "统计结束日期")
    private Date endDate;

    /**
     * 统计状态0分析中 1分析完成
     */
    @ExcelProperty(value = "统计状态0分析中 1分析完成")
    private String status;

    /**
     * 统计报表文件ID
     */
    @ExcelProperty(value = "统计报表文件ID")
    private Long ossId;

    private Date createTime;


}
