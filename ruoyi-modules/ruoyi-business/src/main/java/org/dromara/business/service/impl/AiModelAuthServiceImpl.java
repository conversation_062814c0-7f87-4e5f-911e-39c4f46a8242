package org.dromara.business.service.impl;

import jakarta.servlet.http.HttpServletRequest;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.AiModelChild;
import org.dromara.business.mapper.AiModelChildMapper;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.business.vo.AiModelAuthCountVO;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.GenerateCodeUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysTenant;
import org.dromara.system.mapper.SysTenantMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.dromara.business.domain.bo.AiModelAuthBo;
import org.dromara.business.domain.vo.AiModelAuthVo;
import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.mapper.AiModelAuthMapper;
import org.dromara.business.service.IAiModelAuthService;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.util.*;

/**
 * 模型授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@RequiredArgsConstructor
@Service
public class AiModelAuthServiceImpl implements IAiModelAuthService {

    private final AiModelAuthMapper baseMapper;
    private final AiModelMapper aiModelMapper;
    private final AiModelChildMapper aiModelChildMapper;

    @Resource
    private SysTenantMapper sysTenantMapper;

    /**
     * 查询模型授权
     *
     * @param id 主键
     * @return 模型授权
     */
    @Override
    public AiModelAuthVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询模型授权列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模型授权分页列表
     */
    @Override
    public TableDataInfo<AiModelAuthVo> queryPageList(AiModelAuthBo bo, PageQuery pageQuery) {
        Page<AiModelAuthVo> result = baseMapper.queryPageList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的模型授权列表
     *
     * @param bo 查询条件
     * @return 模型授权列表
     */
    @Override
    public List<AiModelAuthVo> queryList(AiModelAuthBo bo) {
        LambdaQueryWrapper<AiModelAuth> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiModelAuth> buildQueryWrapper(AiModelAuthBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiModelAuth> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getModelId() != null, AiModelAuth::getModelId, bo.getModelId());
        lqw.eq(StringUtils.isNotBlank(bo.getModelCode()), AiModelAuth::getModelCode, bo.getModelCode());
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), AiModelAuth::getModelName, bo.getModelName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserCom()), AiModelAuth::getUserCom, bo.getUserCom());
        lqw.eq(StringUtils.isNotBlank(bo.getProject()), AiModelAuth::getProject, bo.getProject());
        lqw.eq(StringUtils.isNotBlank(bo.getModelKey()), AiModelAuth::getModelKey, bo.getModelKey());
        lqw.eq(bo.getExpirationDate() != null, AiModelAuth::getExpirationDate, bo.getExpirationDate());
        return lqw;
    }

    /**
     * 新增模型授权
     *
     * @param bo 模型授权
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiModelAuthBo bo) {
        //获取当前登录人
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("未获取到登录人信息");
        }
        AiModelAuth add = MapstructUtils.convert(bo, AiModelAuth.class);
        if(null == add){
            throw new ServiceException("新增信息不能为空");
        }
        validEntityBeforeSave(add);
        //查询模型信息
        AiModel aiModel = aiModelMapper.selectById(bo.getModelId());
        if(null != aiModel){
            add.setModelId(aiModel.getId());
            add.setModelCode(aiModel.getModelCode());
            add.setModelName(aiModel.getModelName());
        }
        add.setCreateDept(loginUser.getDeptId());
        add.setTenantId(loginUser.getTenantId());
        add.setModelKey(GenerateCodeUtils.generateAlphaNumeric());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改模型授权
     *
     * @param bo 模型授权
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiModelAuthBo bo) {
        AiModelAuth update = MapstructUtils.convert(bo, AiModelAuth.class);
        if(null == update){
            throw new ServiceException("模型授权信息不存在");
        }
        validEntityBeforeSave(update);
        //查询模型信息
        AiModel aiModel = aiModelMapper.selectById(bo.getModelId());
        if(null != aiModel){
            update.setModelId(aiModel.getId());
            update.setModelCode(aiModel.getModelCode());
            update.setModelName(aiModel.getModelName());
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiModelAuth entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除模型授权信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<AiModelAuth> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 模型keu绑定用户
     * @param bo
     * @return
     */
    @Override
    public int bindUser(AiModelAuthBo bo) {
        AiModelAuth bindAuth = baseMapper.selectById(bo.getId());
        if(null == bindAuth){
            throw new ServiceException("授权信息未找到");
        }
        if(bo.getBindType()==1){
            //绑定操作
            //首先根据返回的userid查询是否已经绑定模型授权
            LambdaQueryWrapper<AiModelAuth> lqw = Wrappers.lambdaQuery();
            lqw.eq(AiModelAuth::getUserId,bo.getUserId());
            lqw.eq(AiModelAuth::getDelFlag,0);
            AiModelAuth aiModelAuth = baseMapper.selectOne(lqw);
            if(null != aiModelAuth){
                throw new ServiceException("该用户已绑定授权，请勿重复操作");
            }
            bindAuth.setUserId(bo.getUserId());
        }else {
            //解绑操作
            bindAuth.setUserId(null);
        }
        return baseMapper.updateById(bindAuth);
    }

    /**
     * 根据key查询模型信息
     * @param modelKey
     * @return
     */
    @Override
    @Cacheable(cacheNames = "global:ai_chat_model_info_key:#10m", key = "#modelKey", condition = "#modelKey != null")
    public Map<String, String> getInfoByKey(String modelKey) {
        Map<String, String> result = new HashMap<>();
        AiModelAuth aiModelAuth = baseMapper.selectOne(Wrappers.lambdaQuery(AiModelAuth.class)
                .eq(AiModelAuth::getModelKey, modelKey)
                .eq(AiModelAuth::getDelFlag, 0));
        if (null != aiModelAuth) {
            result.put("modelName", aiModelAuth.getModelName());
            result.put("project", aiModelAuth.getProject());
            AiModel aiModel = aiModelMapper.selectById(aiModelAuth.getModelId());
            if (null != aiModel) {
                result.put("modelName", aiModel.getModelName());
                result.put("modelLogoUrl", aiModel.getModelLogoUrl());
                //从redis中获取通道地址
                HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
                String browserId = request.getHeader("Browser");
                String redisKey = CacheConstants.AI_MODEL_KEY + aiModel.getModelCode() + ":" + modelKey;
                if (StringUtils.isNotBlank(browserId)) {
                    redisKey = redisKey + ":" + browserId;
                }
                Object keyObjUrl = RedisUtils.getCacheObject(redisKey);
                if(null != keyObjUrl){
                    //根据通道地址和模型key查询模型地址
                    String modelUrl = keyObjUrl.toString();
                    List<AiModelChild> aiModelChild = aiModelChildMapper.selectList(new LambdaQueryWrapper<AiModelChild>()
                        .eq(AiModelChild::getModelId, aiModel.getId())
                        .eq(AiModelChild::getModelUrl, modelUrl)
                        .eq(AiModelChild::getDelFlag, 0)
                        .last("limit 1"));
                    if(aiModelChild != null && !aiModelChild.isEmpty()){
                        result.put("modelSpeed", aiModelChild.get(0).getModelSpeed());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Map<String,String> getUserByKey() {
        List<SysTenant> sysTenants = sysTenantMapper.selectList();
        if (sysTenants.isEmpty()){
            throw new ServiceException("租户信息未找到");
        }
        SysTenant sysTenant = sysTenants.get(0);

        Map<String,String> result = new HashMap<>();
        result.put("companyName",sysTenant.getCompanyName());
        result.put("logoUrl",sysTenant.getLogoUrl());
        result.put("welcomeText",sysTenant.getWelcomeText());
        result.put("imageUrlTitle",sysTenant.getImageUrlTitle());
        result.put("titleManagementPage",sysTenant.getTitleManagementPage());

        result.put("browserTagTitle",sysTenant.getBrowserTagTitle());
        result.put("browserTagImageUrl",sysTenant.getBrowserTagImageUrl());
        result.put("loginPageComment",sysTenant.getLoginPageComment());
        result.put("loginPageTitle",sysTenant.getLoginPageTitle());
        result.put("chatPageComment",sysTenant.getChatPageComment());
        result.put("browserBookmark",sysTenant.getBrowserBookmark());
        result.put("knowledgeBaseComment",sysTenant.getKnowledgeBaseComment());

        result.put("docDownloadSwitch",sysTenant.getDocDownloadSwitch());
        result.put("mobileOperateInstruction",sysTenant.getMobileOperateInstruction());
        result.put("pcOperateInstruction",sysTenant.getPcOperateInstruction());
        result.put("modelAccessInstruction",sysTenant.getModelAccessInstruction());
        result.put("modelAccessApplication",sysTenant.getModelAccessApplication());
        result.put("modelAccessApiDoc",sysTenant.getModelAccessApiDoc());
        return result;
    }

    /**
     * 根据模型key查询模型授权信息
     * @param modelKey
     * @return
     */
    @Override
    @Cacheable(cacheNames = "global:ai_model_chat_auth:#10m", key = "#modelKey", condition = "#modelKey != null")
    public AiModelAuth getAuthInChat(String modelKey) {
        return baseMapper.selectOne(new LambdaQueryWrapper<AiModelAuth>().eq(AiModelAuth::getModelKey, modelKey).eq(AiModelAuth::getDelFlag, 0));
    }


    /**
     * 获取各个授权key的调用次数
     * @return
     */
    @Override
    public List<AiModelAuthCountVO> getAuthModelInfo() {
        return baseMapper.getMonthCount();
    }

    @Override
    public List<AiModelAuthCountVO> getAuthModelInfoAll() {
        //所有时间下的调用次数
        return baseMapper.getAuthModelInfoAll();
    }

}
