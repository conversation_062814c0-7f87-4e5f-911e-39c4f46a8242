package org.dromara.business.domain.vo;

import lombok.Data;
import org.dromara.business.domain.bo.KnowledgeBaseBo;
import org.dromara.common.mybatis.core.page.PageQuery;

@Data
public class KnowledgeBaseListVo {
    /**
     * 部门id
     */
    private KnowledgeBaseBo knowledgeBaseBo;

    /**
     * 分页参数
     */
    private PageQuery pageQuery;

    /**
     * 数据库名字
     */
    private String knowledgeName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 知识库类别
     */
    private String buildType;

    /**
     * 排序方式（asc升序 desc降序）
     */
    private String sortType;

    /**
     * 分类ID
     */
    private Long categoryId;
}
