package org.dromara.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.bo.AiModelBo;
import org.dromara.business.domain.vo.AiModelVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 大模型配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface AiModelMapper extends BaseMapperPlus<AiModel, AiModelVo> {


    @Update("UPDATE ai_model SET `status` = #{status} WHERE id = #{id}")
    void changeModel(@Param("id") Long id, @Param("status") int status);

    @Update("UPDATE ai_model SET `status` = 0")
    void closeModel();

    Page<AiModelVo> queryPageList(@Param("page") Page<Object> build, @Param("bo") AiModelBo bo);
}
