package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;



/**
 * AI 聊天消息视图对象 ai_chat_message
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@ExcelIgnoreUnannotated
public class AiChatMessageVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 消息编号
     */
    @ExcelProperty(value = "消息编号")
    private Long id;

    /**
     * 对话编号
     */
    @ExcelProperty(value = "对话编号")
    private Long conversationId;

    /**
     * 回复编号
     */
    @ExcelProperty(value = "回复编号")
    private Long replyId;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private Long userId;

    /**
     * 段落编号数组
     */
    @ExcelProperty(value = "段落编号数组")
    private String segmentIds;

    /**
     * 消息类型
     */
    @ExcelProperty(value = "消息类型")
    private String type;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;

    /**
     * 是否携带上下文
     */
    @ExcelProperty(value = "是否携带上下文")
    private Integer useContext;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 上传附件信息(Json)
     */
    private String fileInfo;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 引用信息（json）
     */
    private String quoteInfo;


}
