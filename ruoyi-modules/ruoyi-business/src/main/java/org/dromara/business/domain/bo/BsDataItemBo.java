package org.dromara.business.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.business.domain.BsDataItem;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 数据归纳业务对象 bs_data_item
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BsDataItem.class, reverseConvertGenerate = false)
public class BsDataItemBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 数据名称
     */
    @NotBlank(message = "数据名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dataName;

    /**
     * 数据来源
     */
    @NotNull(message = "数据来源不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long dataSource;

    /**
     * 数据分类
     */
    @NotNull(message = "数据分类不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long dataClass;

    /**
     * 数据状态
     */
    @NotNull(message = "数据状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long dataState;

    /**
     * 数据字段
     */
    @NotBlank(message = "数据字段不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dataField;

    /**
     * 应用项目
     */
    private List<String> dataProject;

}
