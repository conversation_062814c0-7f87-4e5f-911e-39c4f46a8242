package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 知识库文件实体类
 */
@Data
@TableName("bs_knowledge_file")
public class KnowledgeFile extends BaseEntity {

    /**
     * 文件ID
     */
    @TableId
    private Long fileId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * OSS文件ID
     */
    private Long ossId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小（KB）
     */
    private Long fileSize;

    /**
     * 文件状态（0处理中 1已完成 2处理失败）
     */
    private String status;

    /**
     * 向量ID（数据治理服务返回）
     */
    private String vectorId;

    /**
     * 文件描述
     */
    private String remark;


}
