package org.dromara.business.service;

import org.dromara.business.domain.vo.BsProjectAcceptStatisticsVo;
import org.dromara.business.domain.bo.BsProjectAcceptStatisticsBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 施工图项目受理清单统计记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IBsProjectAcceptStatisticsService {

    /**
     * 查询施工图项目受理清单统计记录
     *
     * @param id 主键
     * @return 施工图项目受理清单统计记录
     */
    BsProjectAcceptStatisticsVo queryById(Long id);

    /**
     * 分页查询施工图项目受理清单统计记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 施工图项目受理清单统计记录分页列表
     */
    TableDataInfo<BsProjectAcceptStatisticsVo> queryPageList(BsProjectAcceptStatisticsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的施工图项目受理清单统计记录列表
     *
     * @param bo 查询条件
     * @return 施工图项目受理清单统计记录列表
     */
    List<BsProjectAcceptStatisticsVo> queryList(BsProjectAcceptStatisticsBo bo);

    /**
     * 新增施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BsProjectAcceptStatisticsBo bo);

    /**
     * 修改施工图项目受理清单统计记录
     *
     * @param bo 施工图项目受理清单统计记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BsProjectAcceptStatisticsBo bo);

    /**
     * 校验并批量删除施工图项目受理清单统计记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 执行统计分析
     *
     * @param id 统计记录主键
     * @return 生成的文件名
     */
    String executeAnalysis(Long id);
}
