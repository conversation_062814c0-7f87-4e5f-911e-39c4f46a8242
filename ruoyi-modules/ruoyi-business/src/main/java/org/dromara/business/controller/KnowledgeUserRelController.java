package org.dromara.business.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.bo.KnowledgeUserRelBo;
import org.dromara.business.domain.vo.KnowledgeUserRelVo;
import org.dromara.business.service.IKnowledgeUserRelService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 知识库用户关联控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/knowledge/user")
public class KnowledgeUserRelController extends BaseController {

    private final IKnowledgeUserRelService knowledgeUserRelService;

    /**
     * 查询知识库用户关联列表
     */
    @GetMapping("/list")
    public TableDataInfo<KnowledgeUserRelVo> list(KnowledgeUserRelBo bo, PageQuery pageQuery) {
        return knowledgeUserRelService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取知识库用户关联详细信息
     */
    @GetMapping("/{relId}")
    public R<KnowledgeUserRelVo> getInfo(@PathVariable Long relId) {
        return R.ok(knowledgeUserRelService.queryById(relId));
    }

    /**
     * 根据知识库ID查询用户关联列表
     */
    @GetMapping("/list/{knowledgeId}")
    public R<List<KnowledgeUserRelVo>> listByKnowledgeId(@PathVariable Long knowledgeId) {
        return R.ok(knowledgeUserRelService.queryListByKnowledgeId(knowledgeId));
    }

    /**
     * 根据用户ID查询知识库关联列表
     */
    @GetMapping("/list/user/{userId}")
    public R<List<KnowledgeUserRelVo>> listByUserId(@PathVariable Long userId) {
        return R.ok(knowledgeUserRelService.queryListByUserId(userId));
    }

    /**
     * 新增知识库用户关联
     */
    @Log(title = "知识库用户关联", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody KnowledgeUserRelBo bo) {
        return toAjax(knowledgeUserRelService.insertByBo(bo));
    }

    /**
     * 修改知识库用户关联
     */
    @Log(title = "知识库用户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody KnowledgeUserRelBo bo) {
        return toAjax(knowledgeUserRelService.updateByBo(bo));
    }

    /**
     * 删除知识库用户关联
     */
    @Log(title = "知识库用户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{relIds}")
    public R<Void> remove(@PathVariable Long[] relIds) {
        return toAjax(knowledgeUserRelService.deleteWithValidByIds(Arrays.asList(relIds)));
    }

    /**
     * 批量添加知识库用户关联
     */
    @Log(title = "知识库用户关联", businessType = BusinessType.INSERT)
    @PostMapping("/batch/{knowledgeId}/{permissionType}")
    public R<Void> batchAddUsers(@PathVariable Long knowledgeId, @PathVariable String permissionType, @RequestBody Long[] userIds) {
        return toAjax(knowledgeUserRelService.batchAddUsers(knowledgeId, Arrays.asList(userIds), permissionType));
    }

    /**
     * 批量删除知识库用户关联
     */
    @Log(title = "知识库用户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{knowledgeId}")
    public R<Void> batchDeleteUsers(@PathVariable Long knowledgeId, @RequestBody Long[] userIds) {
        return toAjax(knowledgeUserRelService.batchDeleteUsers(knowledgeId, Arrays.asList(userIds)));
    }
}
