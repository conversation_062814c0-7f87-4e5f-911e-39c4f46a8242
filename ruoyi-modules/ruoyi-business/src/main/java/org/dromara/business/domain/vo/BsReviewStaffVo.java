package org.dromara.business.domain.vo;

import org.dromara.business.domain.BsReviewStaff;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 审查人员信息视图对象 bs_review_staff
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BsReviewStaff.class)
public class BsReviewStaffVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名")
    private String personName;

    /**
     * 所属类目
     */
    @ExcelProperty(value = "所属类目")
    private String categoryKey;

    /**
     * 所属机构
     */
    @ExcelProperty(value = "所属机构")
    private String organizationKey;


}
