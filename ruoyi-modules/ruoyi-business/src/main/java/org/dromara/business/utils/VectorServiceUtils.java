package org.dromara.business.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.json.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 向量服务工具类
 * 用于调用向量服务接口，支持重试机制
 * <p>
 * 提供了向量数据的存储和删除功能，使用Hutool的HTTP工具发起请求
 * 当请求失败时，会自动重试，最大重试次数为3次
 */
@Slf4j
@Configuration
public class VectorServiceUtils {

    /**
     * 向量服务器基础URL
     */
    @Value("${rag.url}")
    private String ragUrl;
    private static String VECTOR_SERVER_URL;

    /**
     * 向量存储接口路径
     */
    private static final String SAVE_VECTOR_PATH = "/milvus/saveVectorData";
    /**
     * 向量删除接口路径
     */
    private static final String DELETE_VECTOR_PATH = "/milvus/deleteVectorData";
    /**
     * 向量检索接口路径
     */
    private static final String SEARCH_VECTOR_PATH = "/milvus/searchVectorData";
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_TIMES = 3;
    /**
     * 重试等待时间（毫秒）
     */
    private static final long RETRY_WAIT_TIME = 1000;
    /**
     * HTTP请求超时时间（毫秒）
     */
    private static final int HTTP_TIMEOUT = 30000;

    /**
     * 创建重试器
     *
     * @param <T> 返回类型
     * @return 重试器
     */
    private static <T> Retryer<T> createRetryer() {
        return RetryerBuilder.<T>newBuilder()
            .retryIfException() // 发生异常时重试
            .withWaitStrategy(WaitStrategies.fixedWait(RETRY_WAIT_TIME, TimeUnit.MILLISECONDS)) // 固定等待时间
            .withStopStrategy(StopStrategies.stopAfterAttempt(MAX_RETRY_TIMES)) // 最大重试次数
            .build();
    }

    /**
     * 执行带重试的操作
     *
     * @param supplier 操作
     * @param <T>      返回类型
     * @return 操作结果
     */
    private static <T> T executeWithRetry(Supplier<T> supplier) {
        Retryer<T> retryer = createRetryer();
        try {
            return retryer.call(supplier::get);
        } catch (Exception e) {
            log.error("执行重试操作失败", e);
            throw new ServiceException("调用向量服务失败，请稍后重试");
        }
    }

    /**
     * 保存向量数据
     *
     * @param userPhone         用户手机号
     * @param userType          用户类型
     * @param knowledgeBaseId   知识库ID
     * @param knowledgeBaseType 知识库类型
     * @param fileId            文件ID
     * @param textVectorContent 文本向量内容
     * @param indexNumber       索引编号，默认为1
     * @param totalFileLength   文件总长度，默认为"1"
     * @return 向量ID
     */
    public static String saveVectorData(String userPhone, String userType, String knowledgeBaseId,
                                        String knowledgeBaseType, String fileId, String textVectorContent,
                                        Integer indexNumber, String totalFileLength, JSONArray jsonVectorData) {
        // 参数校验
        if (StrUtil.isBlank(userPhone) || StrUtil.isBlank(knowledgeBaseId) || StrUtil.isBlank(fileId) || StrUtil.isBlank(knowledgeBaseType)) {
            throw new ServiceException("参数不能为空：用户手机号、知识库ID和文件ID为必填项");
        }

        if (ObjectUtil.equals(knowledgeBaseType, "not_structured") && StrUtil.isBlank(textVectorContent)) {
            throw new ServiceException("向量内容不能为空");
        } else if (ObjectUtil.equals(knowledgeBaseType, "structured") && jsonVectorData.isEmpty()) {
            //结构化数据
            throw new ServiceException("向量内容不能为空");
        }

        // 构建请求实体
        VectorStoreRequest request = new VectorStoreRequest();
        request.setUserPhone(userPhone);
        request.setUserType(userType != null ? userType : "");
        request.setKnowledgeBaseId(knowledgeBaseId);
        request.setKnowledgeBaseType(knowledgeBaseType);
        request.setFileId(fileId);
        request.setIndexNumber(indexNumber != null ? indexNumber : 1);
        request.setTotalFileLength(totalFileLength != null ? totalFileLength : "1");
        request.setTextVectorContent(textVectorContent);
        request.setJsonVectorData(jsonVectorData);

        // 执行带重试的HTTP请求
        return executeWithRetry(() -> {
            String url = VECTOR_SERVER_URL + SAVE_VECTOR_PATH;
            String jsonParams = JsonUtils.toJsonString(request);
            log.info("调用向量存储接口，URL: {}", url);

            HttpResponse response = null;
            try {
                response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(jsonParams)
                    .timeout(HTTP_TIMEOUT)
                    .execute();

                int status = response.getStatus();
                String body = response.body();
                log.info("向量存储接口返回，状态码: {}, 响应: {}", status, body);

                if (status != HttpStatus.HTTP_OK) {
                    log.error("调用向量存储接口失败，状态码: {}, 响应: {}", status, body);
                    throw new ServiceException("文件上传失败: " + status);
                }

                Map<String, Object> result = JsonUtils.parseMap(body);
                if (result == null) {
                    throw new ServiceException("文件上传失败");
                }

                // 检查向量结果
                Object vectorResult = result.get("向量结果");
                if (vectorResult == null) {
                    log.error("接口返回结果格式错误，缺少'向量结果'字段: {}", body);
                    return null;
                }

                String resultStr = vectorResult.toString();
                if (!"success".equals(resultStr)) {
                    log.error("向量存储失败，结果: {}", resultStr);
                    return null; // 失败返回null
                }

                // 成功返回"success"
                return "success";
            } finally {
                // 关闭响应
                if (response != null) {
                    response.close();
                }
            }
        });
    }

    /**
     * 删除向量数据
     *
     * @param userPhone         用户手机号
     * @param userType          用户类型
     * @param knowledgeBaseId   知识库ID
     * @param knowledgeBaseType 知识库类型
     * @param fileId            文件ID
     * @return 是否删除成功
     */
    public static boolean deleteVectorData(String userPhone, String userType, String knowledgeBaseId,
                                           String knowledgeBaseType, String fileId) {
        // 参数校验
        if (StrUtil.isBlank(userPhone) || StrUtil.isBlank(knowledgeBaseId) || StrUtil.isBlank(fileId)) {
            throw new ServiceException("参数不能为空：用户手机号、知识库ID和文件ID为必填项");
        }

        // 构建请求实体
        VectorDeleteRequest request = new VectorDeleteRequest();
        request.setUserPhone(userPhone);
        request.setUserType(userType != null ? userType : "");
        request.setKnowledgeBaseId(knowledgeBaseId);
        request.setKnowledgeBaseType(knowledgeBaseType != null ? knowledgeBaseType : "");
        request.setFileId(fileId);

        // 执行带重试的HTTP请求
        return executeWithRetry(() -> {
            String url = VECTOR_SERVER_URL + DELETE_VECTOR_PATH;
            String jsonParams = JsonUtils.toJsonString(request);
            log.info("调用向量删除接口，URL: {}, 参数: {}", url, jsonParams);

            HttpResponse response = null;
            try {
                response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(jsonParams)
                    .timeout(HTTP_TIMEOUT)
                    .execute();

                int status = response.getStatus();
                String body = response.body();
                log.info("向量删除接口返回，状态码: {}, 响应: {}", status, body);

                if (status != HttpStatus.HTTP_OK) {
                    log.error("调用向量删除接口失败，状态码: {}, 响应: {}", status, body);
                    throw new ServiceException("调用向量删除接口失败，HTTP状态码: " + status);
                }

                Map<String, Object> result = JsonUtils.parseMap(body);
                if (result == null) {
                    throw new ServiceException("解析向量删除接口响应失败");
                }

                // 检查向量结果
                Object vectorResult = result.get("删除结果");
                if (vectorResult == null) {
                    log.error("接口返回结果格式错误，缺少'向量结果'字段: {}", body);
                    throw new ServiceException("接口返回结果格式错误");
                }

                String resultStr = vectorResult.toString();
                if (!"success".equals(resultStr)) {
                    log.error("向量删除失败，结果: {}", resultStr);
                    throw new ServiceException("向量删除失败");
                }
                return true;
            } finally {
                // 关闭响应
                if (response != null) {
                    response.close();
                }
            }
        });
    }

    /**
     * 发起向量检索
     *
     * @param vectorStoreSearch
     * @return
     */
    public static String searchVectorData(VectorStoreSearch vectorStoreSearch) {
        String paramStr = JSON.toJSONString(vectorStoreSearch);
        String url = VECTOR_SERVER_URL + SEARCH_VECTOR_PATH;
        String result = "";
        try {
            result = HttpUtil.post(url, paramStr, 30000);
        } catch (Exception e) {
            log.error("连接向量服务失败" + e.getMessage());
            return result;
        }
        return result;
    }

    @PostConstruct
    void init() {
        VECTOR_SERVER_URL = ragUrl;
    }

    /**
     * 向量存储请求实体
     */
    @Data
    public static class VectorStoreRequest {
        /**
         * 用户手机号
         */
        private String userPhone;

        /**
         * 用户类型
         */
        private String userType;

        /**
         * 知识库ID
         */
        private String knowledgeBaseId;

        /**
         * 知识库类型
         */
        private String knowledgeBaseType;

        /**
         * 文件ID
         */
        private String fileId;

        /**
         * 索引编号
         */
        private Integer indexNumber;

        /**
         * 文件总长度
         */
        private String totalFileLength;

        /**
         * 文本向量内容
         */
        private String textVectorContent;

        /**
         * json数组，每行是一个对象
         */
        private JSONArray jsonVectorData;
    }

    /**
     * 向量删除请求实体
     */
    @Data
    public static class VectorDeleteRequest {
        /**
         * 用户手机号
         */
        private String userPhone;

        /**
         * 用户类型
         */
        private String userType;

        /**
         * 知识库ID
         */
        private String knowledgeBaseId;

        /**
         * 知识库类型
         */
        private String knowledgeBaseType;

        /**
         * 文件ID
         */
        private String fileId;
    }

    /**
     * 检索向量数据实体
     */
    @Data
    public static class VectorStoreSearch {
        /**
         * 用户会话问题
         */
        private String userPrompt;

        /**
         * 用户手机号
         */
        private String userPhone;

        /**
         * 用户类型
         */
        private String userType;

        /**
         * 知识库id
         */
        private String knowledgeBaseId;

        /**
         * 知识库类型
         */
        private String knowledgeBaseType;

        /**
         * 映射关系（结构化 必传）
         */
        private String schemaInfos;

        /**
         * 检索片段上限
         */
        private Integer topK = 8;

        /**
         * 相似度
         */
        private Double similarityThreshold = 0.25;

        /**
         * 文件字符召回
         */
        private Integer fileRecall = 5000;
    }


    /**
     * 批量保存向量数据
     *
     * @param userPhone        用户手机号
     * @param userType         用户类型
     * @param knowledgeBaseId  知识库ID
     * @param knowledgeBaseType 知识库类型
     * @param fileId           文件ID
     * @param jsonVectorData   向量数据列表
     * @return 向量ID
     */
//    public static String saveVectorDataBatch(String userPhone, String userType, String knowledgeBaseId,
//                                           String knowledgeBaseType, String fileId, List<Map<String, Object>> jsonVectorData) {
//        // 参数校验
//        if (StrUtil.isBlank(userPhone) || StrUtil.isBlank(knowledgeBaseId) || StrUtil.isBlank(fileId)) {
//            throw new ServiceException("参数不能为空：用户手机号、知识库ID和文件ID为必填项");
//        }
//
//        if (jsonVectorData == null || jsonVectorData.isEmpty()) {
//            throw new ServiceException("向量数据不能为空");
//        }
//
//        // 构建请求参数
//        Map<String, Object> params = new HashMap<>();
//        params.put("userPhone", userPhone);
//        params.put("userType", userType != null ? userType : "");
//        params.put("knowledgeBaseId", knowledgeBaseId);
//        params.put("knowledgeBaseType", knowledgeBaseType != null ? knowledgeBaseType : "");
//        params.put("fileId", fileId);
//        params.put("indexNumber", 1);
//        params.put("totalFileLength", String.valueOf(jsonVectorData.size()));
//        params.put("jsonVectorData", jsonVectorData);
//
//        // 执行带重试的HTTP请求
//        return executeWithRetry(() -> {
//            String url = VECTOR_SERVER_URL + SAVE_VECTOR_PATH;
//            String jsonParams = JsonUtils.toJsonString(params);
//            log.info("调用批量向量存储接口，URL: {}, 参数: {}", url, jsonParams);
//
//            HttpResponse response = null;
//            try {
//                response = HttpRequest.post(url)
//                    .header("Content-Type", "application/json")
//                    .body(jsonParams)
//                    .timeout(HTTP_TIMEOUT)
//                    .execute();
//
//                int status = response.getStatus();
//                String body = response.body();
//                log.info("批量向量存储接口返回，状态码: {}, 响应: {}", status, body);
//
//                if (status != HttpStatus.HTTP_OK) {
//                    log.error("调用批量向量存储接口失败，状态码: {}, 响应: {}", status, body);
//                    throw new ServiceException("调用批量向量存储接口失败，HTTP状态码: " + status);
//                }
//
//                Map<String, Object> result = JsonUtils.parseMap(body);
//                if (result == null) {
//                    throw new ServiceException("解析批量向量存储接口响应失败");
//                }
//
//                Object code = result.get("code");
//                if (code == null || !code.toString().equals("200")) {
//                    String message = result.get("message") != null ? result.get("message").toString() : "未知错误";
//                    throw new ServiceException("批量向量存储失败: " + message);
//                }
//
//                // 返回向量ID
//                Object data = result.get("data");
//                if (data != null) {
//                    return data.toString();
//                }
//                return null;
//            } finally {
//                // 关闭响应
//                if (response != null) {
//                    response.close();
//                }
//            }
//        });
//    }
}
