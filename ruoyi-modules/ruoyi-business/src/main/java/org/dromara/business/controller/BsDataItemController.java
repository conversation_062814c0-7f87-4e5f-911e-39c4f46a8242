package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.bo.BsDataItemBo;
import org.dromara.business.domain.vo.BsDataItemVo;
import org.dromara.business.service.IBsDataItemService;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据归纳
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dataItem")
public class BsDataItemController extends BaseController {

    private final IBsDataItemService bsDataItemService;

    /**
     * 查询数据归纳列表
     */
    @SaCheckPermission("system:dataItem:list")
    @GetMapping("/list")
    public TableDataInfo<BsDataItemVo> list(BsDataItemBo bo, PageQuery pageQuery) {
        return bsDataItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出数据归纳列表
     */
    @SaCheckPermission("system:dataItem:export")
    @Log(title = "数据归纳", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BsDataItemBo bo, HttpServletResponse response) {
        List<BsDataItemVo> list = bsDataItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "数据归纳", BsDataItemVo.class, response);
    }

    /**
     * 获取数据归纳详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:dataItem:query")
    @GetMapping("/{id}")
    public R<BsDataItemVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(bsDataItemService.queryById(id));
    }

    /**
     * 新增数据归纳
     */
    @SaCheckPermission("system:dataItem:add")
    @Log(title = "数据归纳", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BsDataItemBo bo) {
        return toAjax(bsDataItemService.insertByBo(bo));
    }

    /**
     * 修改数据归纳
     */
    @SaCheckPermission("system:dataItem:edit")
    @Log(title = "数据归纳", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BsDataItemBo bo) {
        return toAjax(bsDataItemService.updateByBo(bo));
    }

    /**
     * 删除数据归纳
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:dataItem:remove")
    @Log(title = "数据归纳", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bsDataItemService.deleteWithValidByIds(List.of(ids), true));
    }
}
