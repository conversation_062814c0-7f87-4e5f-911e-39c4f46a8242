package org.dromara.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.KnowledgeCategoryRelation;
import org.dromara.business.dto.KnowledgeCategoryRelationDTO;
import org.dromara.business.mapper.KnowledgeCategoryRelationMapper;
import org.dromara.business.service.IKnowledgeCategoryRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库分类关联Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class KnowledgeCategoryRelationServiceImpl extends ServiceImpl<KnowledgeCategoryRelationMapper, KnowledgeCategoryRelation>
    implements IKnowledgeCategoryRelationService {

    private final KnowledgeCategoryRelationMapper relationMapper;

    /**
     * 绑定知识库到分类
     *
     * @param dto 绑定信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindKnowledgesToCategory(KnowledgeCategoryRelationDTO dto) {
        Long categoryId = dto.getCategoryId();
        List<Long> knowledgeIds = dto.getKnowledgeIds();

        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return false;
        }

        // 先删除已有的关联
        QueryWrapper<KnowledgeCategoryRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KnowledgeCategoryRelation::getCategoryId, categoryId)
            .in(KnowledgeCategoryRelation::getKnowledgeId, knowledgeIds);
        relationMapper.delete(queryWrapper);

        // 批量添加新的关联
        List<KnowledgeCategoryRelation> relations = new ArrayList<>();
        for (Long knowledgeId : knowledgeIds) {
            KnowledgeCategoryRelation relation = new KnowledgeCategoryRelation();
            relation.setCategoryId(categoryId);
            relation.setKnowledgeId(knowledgeId);
            relations.add(relation);
        }

        return saveBatch(relations);
    }

    /**
     * 解绑知识库与分类的关联
     *
     * @param categoryId 分类ID
     * @param knowledgeId 知识库ID
     * @return 结果
     */
    @Override
    public boolean unbindKnowledgeFromCategory(Long categoryId, Long knowledgeId) {
        LambdaQueryWrapper<KnowledgeCategoryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeCategoryRelation::getCategoryId, categoryId)
                   .eq(KnowledgeCategoryRelation::getKnowledgeId, knowledgeId);
        return remove(queryWrapper);
    }

    /**
     * 根据分类ID获取关联的知识库ID列表
     *
     * @param categoryId 分类ID
     * @return 知识库ID列表
     */
    @Override
    public List<Long> getKnowledgeIdsByCategoryId(Long categoryId) {
        LambdaQueryWrapper<KnowledgeCategoryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeCategoryRelation::getCategoryId, categoryId)
                   .select(KnowledgeCategoryRelation::getKnowledgeId);

        return list(queryWrapper).stream()
                .map(KnowledgeCategoryRelation::getKnowledgeId)
                .collect(Collectors.toList());
    }

    /**
     * 根据知识库ID获取关联的分类ID列表
     *
     * @param knowledgeId 知识库ID
     * @return 分类ID列表
     */
    @Override
    public List<Long> getCategoryIdsByKnowledgeId(Long knowledgeId) {
        LambdaQueryWrapper<KnowledgeCategoryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeCategoryRelation::getKnowledgeId, knowledgeId)
                   .select(KnowledgeCategoryRelation::getCategoryId);

        return list(queryWrapper).stream()
                .map(KnowledgeCategoryRelation::getCategoryId)
                .collect(Collectors.toList());
    }

    /**
     * 更新知识库关联的分类
     *
     * @param knowledgeId 知识库ID
     * @param categoryId 新的分类ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateKnowledgeCategory(Long knowledgeId, Long categoryId) {
        // 删除知识库与原有分类的所有关联
        LambdaQueryWrapper<KnowledgeCategoryRelation> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(KnowledgeCategoryRelation::getKnowledgeId, knowledgeId);
        remove(deleteWrapper);
        if (categoryId == null) {
            return true;
        }
        // 创建知识库与新分类的关联
        KnowledgeCategoryRelation relation = new KnowledgeCategoryRelation();
        relation.setKnowledgeId(knowledgeId);
        relation.setCategoryId(categoryId);

        return save(relation);
    }
}
