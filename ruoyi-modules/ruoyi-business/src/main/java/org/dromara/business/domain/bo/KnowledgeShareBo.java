package org.dromara.business.domain.bo;

import org.dromara.business.domain.vo.KnowledgeShareVo;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库分享业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = KnowledgeShareVo.class)
public class KnowledgeShareBo extends BaseEntity {

    /**
     * 页面路径
     */
    @NotBlank(message = "页面路径不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 1, max = 255, message = "页面路径长度不能超过255个字符")
    private String pagePath;

    /**
     * 知识库ID
     */
    @NotBlank(message = "知识库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 1, max = 64, message = "知识库ID长度不能超过64个字符")
    private String knowledgeId;
}
