package org.dromara.business.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.vo.AiModelChildVo;
import org.dromara.business.domain.bo.AiModelChildBo;
import org.dromara.business.service.IAiModelChildService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 模型算力地址
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/modelChild")
public class AiModelChildController extends BaseController {

    private final IAiModelChildService aiModelChildService;

    /**
     * 查询模型算力地址列表
     */
    @SaCheckPermission("business:modelChild:list")
    @GetMapping("/list")
    public TableDataInfo<AiModelChildVo> list(AiModelChildBo bo, PageQuery pageQuery) {
        return aiModelChildService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出模型算力地址列表
     */
    @SaCheckPermission("business:modelChild:export")
    @Log(title = "模型算力地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiModelChildBo bo, HttpServletResponse response) {
        List<AiModelChildVo> list = aiModelChildService.queryList(bo);
        ExcelUtil.exportExcel(list, "模型算力地址", AiModelChildVo.class, response);
    }

    /**
     * 获取模型算力地址详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:modelChild:query")
    @GetMapping("/{id}")
    public R<AiModelChildVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(aiModelChildService.queryById(id));
    }

    /**
     * 新增模型算力地址
     */
    @SaCheckPermission("business:modelChild:add")
    @Log(title = "模型算力地址", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiModelChildBo bo) {
        return toAjax(aiModelChildService.insertByBo(bo));
    }

    /**
     * 修改模型算力地址
     */
    @SaCheckPermission("business:modelChild:edit")
    @Log(title = "模型算力地址", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiModelChildBo bo) {
        return toAjax(aiModelChildService.updateByBo(bo));
    }

    /**
     * 删除模型算力地址
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:modelChild:remove")
    @Log(title = "模型算力地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(aiModelChildService.deleteWithValidByIds(List.of(ids), true));
    }
}
