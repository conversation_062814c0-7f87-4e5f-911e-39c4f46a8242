package org.dromara.business.domain.vo;

import lombok.Data;

import java.io.Serializable;


/**
 * 模型历史信息用量返回实体
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
public class AiModelHistoryMsgVo implements Serializable {

    /**
     * 日期
     */
    private String dateTime;

    /**
     * 峰值日期/时间
     */
    private String peakUserTime;

    /**
     * 峰值使用人数
     */
    private String peakUserCount;

    /**
     * 峰值日期/时间
     */
    private String peakMsgTime;

    /**
     * 峰值会话次数
     */
    private String peakMsgCount;

    /**
     * 总使用人数
     */
    private String userCount;


    /**
     * 总会话次数
     */
    private String msgCount;


}
