package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.business.handler.ListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.List;

/**
 * 数据归纳对象 bs_data_item
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bs_data_item", autoResultMap = true)
public class BsDataItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 数据名称
     */
    private String dataName;

    /**
     * 数据来源
     */
    private Long dataSource;

    /**
     * 数据分类
     */
    private Long dataClass;

    /**
     * 数据状态
     */
    private Long dataState;

    /**
     * 数据字段
     */
    private String dataField;

    /**
     * 应用项目
     */
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> dataProject;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;


}
