package org.dromara.business.service;


import jakarta.validation.Valid;
import org.dromara.business.domain.AiChatConversation;
import org.dromara.business.domain.bo.AiChatConversationBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * AI 聊天对话Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IAiChatConversationService {

    /**
     * 查询AI 聊天对话
     */
    AiChatConversationVo queryById(Long id);

    /**
     * 查询AI 聊天对话列表
     */
    TableDataInfo<AiChatConversationVo> queryPageList(AiChatConversationBo bo, PageQuery pageQuery);

    /**
     * 查询AI 聊天对话列表
     */
    List<AiChatConversationVo> queryList(AiChatConversationBo bo);

    /**
     * 新增AI 聊天对话
     */
    Boolean insertByBo(AiChatConversationBo bo);

    /**
     * 修改AI 聊天对话
     */
    Boolean updateByBo(AiChatConversationBo bo);

    /**
     * 校验并批量删除AI 聊天对话信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Long createChatConversationMy(AiChatConversationBo createReqVO);

    void updateChatConversationMy(AiChatConversationBo updateReqVO);

    List<AiChatConversationVo> getChatConversationListByUserId(String projectId);

    AiChatConversation getChatConversation(Long id);

    void deleteChatConversationMy(Long id);

    void deleteChatConversationMyByUnpinned();

    void updateBy(AiChatConversation aiChatConversation);
}
