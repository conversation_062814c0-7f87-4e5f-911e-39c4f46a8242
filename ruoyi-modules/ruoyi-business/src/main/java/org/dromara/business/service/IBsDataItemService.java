package org.dromara.business.service;

import org.dromara.business.domain.bo.BsDataItemBo;
import org.dromara.business.domain.vo.BsDataItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 数据归纳Service接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface IBsDataItemService {

    /**
     * 查询数据归纳
     *
     * @param id 主键
     * @return 数据归纳
     */
    BsDataItemVo queryById(Long id);

    /**
     * 分页查询数据归纳列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 数据归纳分页列表
     */
    TableDataInfo<BsDataItemVo> queryPageList(BsDataItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的数据归纳列表
     *
     * @param bo 查询条件
     * @return 数据归纳列表
     */
    List<BsDataItemVo> queryList(BsDataItemBo bo);

    /**
     * 新增数据归纳
     *
     * @param bo 数据归纳
     * @return 是否新增成功
     */
    Boolean insertByBo(BsDataItemBo bo);

    /**
     * 修改数据归纳
     *
     * @param bo 数据归纳
     * @return 是否修改成功
     */
    Boolean updateByBo(BsDataItemBo bo);

    /**
     * 校验并批量删除数据归纳信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
