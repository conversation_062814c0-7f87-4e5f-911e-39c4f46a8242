package org.dromara.business.service;


import org.dromara.business.domain.AiChatMessage;
import org.dromara.business.domain.bo.AiChatMessageBo;
import org.dromara.business.domain.vo.AiChatConversationVo;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * AI 聊天消息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IAiChatMessageService {

    /**
     * 查询AI 聊天消息
     */
    AiChatMessageVo queryById(Long id);

    /**
     * 查询AI 聊天消息列表
     */
    TableDataInfo<AiChatMessage> queryPageList(AiChatMessageBo bo, PageQuery pageQuery);

    /**
     * 查询AI 聊天消息列表
     */
    List<AiChatMessageVo> queryList(AiChatMessageBo bo);

    /**
     * 新增AI 聊天消息
     */
    Boolean insertByBo(AiChatMessageBo bo);

    /**
     * 修改AI 聊天消息
     */
    Boolean updateByBo(AiChatMessageBo bo);

    /**
     * 校验并批量删除AI 聊天消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<AiChatMessageVo> getChatMessageListByConversationId(Long conversationId);

    void deleteChatMessage(Long id);

    void deleteChatMessageByConversationId(Long conversationId);

    Boolean editChatMessage(Long id, String content);

    Boolean clearMessages(Long conversationId);
}
