package org.dromara.business.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.KnowledgeUserRel;
import org.dromara.business.domain.bo.KnowledgeUserRelBo;
import org.dromara.business.domain.vo.KnowledgeUserRelVo;
import org.dromara.business.mapper.KnowledgeUserRelMapper;
import org.dromara.business.service.IKnowledgeUserRelService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 知识库用户关联服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeUserRelServiceImpl implements IKnowledgeUserRelService {

    private final KnowledgeUserRelMapper baseMapper;
    private final ISysUserService userService;

    /**
     * 查询知识库用户关联
     *
     * @param relId 关联主键
     * @return 知识库用户关联
     */
    @Override
    public KnowledgeUserRelVo queryById(Long relId) {
        return baseMapper.selectVoById(relId);
    }

    /**
     * 查询知识库用户关联列表
     *
     * @param bo 知识库用户关联
     * @return 知识库用户关联
     */
    @Override
    public TableDataInfo<KnowledgeUserRelVo> queryPageList(KnowledgeUserRelBo bo, PageQuery pageQuery) {
        return baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo));
    }

    /**
     * 查询知识库用户关联列表
     *
     * @param bo 知识库用户关联
     * @return 知识库用户关联
     */
    @Override
    public List<KnowledgeUserRelVo> queryList(KnowledgeUserRelBo bo) {
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    /**
     * 新增知识库用户关联
     *
     * @param bo 知识库用户关联
     * @return 结果
     */
    @Override
    public Boolean insertByBo(KnowledgeUserRelBo bo) {
        KnowledgeUserRel add = MapstructUtils.convert(bo, KnowledgeUserRel.class);
        // 默认权限类型为查看
        if (StringUtils.isEmpty(add.getPermissionType())) {
            add.setPermissionType("0");
        }
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改知识库用户关联
     *
     * @param bo 知识库用户关联
     * @return 结果
     */
    @Override
    public Boolean updateByBo(KnowledgeUserRelBo bo) {
        KnowledgeUserRel update = MapstructUtils.convert(bo, KnowledgeUserRel.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除知识库用户关联
     *
     * @param ids 需要删除的知识库用户关联主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据知识库ID查询用户关联列表
     *
     * @param knowledgeId 知识库ID
     * @return 知识库用户关联集合
     */
    @Override
    public List<KnowledgeUserRelVo> queryListByKnowledgeId(Long knowledgeId) {
        KnowledgeUserRelBo bo = new KnowledgeUserRelBo();
        bo.setKnowledgeId(knowledgeId);
        List<KnowledgeUserRelVo> list = this.queryList(bo);
        // 补充用户信息
        for (KnowledgeUserRelVo vo : list) {
            if (vo.getUserId() != null) {
                try {
                    var user = userService.selectUserById(vo.getUserId());
                    if (user != null) {
                        vo.setUserName(user.getUserName());
                        vo.setNickName(user.getNickName());
                    }
                } catch (Exception e) {
                    log.error("获取用户信息失败", e);
                }
            }
        }
        return list;
    }

    /**
     * 根据用户ID查询知识库关联列表
     *
     * @param userId 用户ID
     * @return 知识库用户关联集合
     */
    @Override
    public List<KnowledgeUserRelVo> queryListByUserId(Long userId) {
        KnowledgeUserRelBo bo = new KnowledgeUserRelBo();
        bo.setUserId(userId);
        return this.queryList(bo);
    }

    /**
     * 批量添加知识库用户关联
     *
     * @param knowledgeId    知识库ID
     * @param userIds        用户ID集合
     * @param permissionType 权限类型
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddUsers(Long knowledgeId, Collection<Long> userIds, String permissionType) {
        if (userIds == null || userIds.isEmpty()) {
            return false;
        }

        // 先删除已有的关联
        this.batchDeleteUsers(knowledgeId, userIds);

        // 批量添加新关联
        List<KnowledgeUserRel> relList = new ArrayList<>();
        for (Long userId : userIds) {
            KnowledgeUserRel rel = new KnowledgeUserRel();
            rel.setKnowledgeId(knowledgeId);
            rel.setUserId(userId);
            rel.setPermissionType(StringUtils.isNotEmpty(permissionType) ? permissionType : "0");
            rel.setCreateBy(LoginHelper.getUserId());
            rel.setUpdateBy(LoginHelper.getUserId());
            relList.add(rel);
        }

        return baseMapper.insertBatch(relList);
    }

    /**
     * 批量删除知识库用户关联
     *
     * @param knowledgeId 知识库ID
     * @param userIds     用户ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteUsers(Long knowledgeId, Collection<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return false;
        }

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeUserRel> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(KnowledgeUserRel::getKnowledgeId, knowledgeId);
        lqw.in(KnowledgeUserRel::getUserId, userIds);
        return baseMapper.delete(lqw) >= 0;
    }

    /**
     * 检查用户是否有权限访问知识库
     *
     * @param knowledgeId 知识库ID
     * @param userId      用户ID
     * @return 是否有权限
     */
    @Override
    public Boolean checkPermission(Long knowledgeId, Long userId) {
        if (knowledgeId == null || userId == null) {
            return false;
        }

        // 超级管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId)) {
            return true;
        }

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeUserRel> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(KnowledgeUserRel::getKnowledgeId, knowledgeId);
        lqw.eq(KnowledgeUserRel::getUserId, userId);
        return baseMapper.exists(lqw);
    }

    /**
     * 检查用户是否有编辑权限
     *
     * @param knowledgeId 知识库ID
     * @param userId      用户ID
     * @return 是否有编辑权限
     */
    @Override
    public Boolean checkEditPermission(Long knowledgeId, Long userId) {
        if (knowledgeId == null || userId == null) {
            return false;
        }

        // 超级管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId)) {
            return true;
        }

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeUserRel> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(KnowledgeUserRel::getKnowledgeId, knowledgeId);
        lqw.eq(KnowledgeUserRel::getUserId, userId);
        lqw.in(KnowledgeUserRel::getPermissionType, "1", "2"); // 编辑或管理权限
        return baseMapper.exists(lqw);
    }


    /**
     * 构建查询条件
     *
     * @param bo 知识库用户关联信息
     * @return 查询条件
     */
    private com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeUserRel> buildQueryWrapper(KnowledgeUserRelBo bo) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeUserRel> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(bo.getKnowledgeId() != null, KnowledgeUserRel::getKnowledgeId, bo.getKnowledgeId());
        lqw.eq(bo.getUserId() != null, KnowledgeUserRel::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getPermissionType()), KnowledgeUserRel::getPermissionType, bo.getPermissionType());
        return lqw;
    }
}
