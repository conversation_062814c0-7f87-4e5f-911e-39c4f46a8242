package org.dromara.business.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.business.dto.KnowledgeCategoryDTO;
import org.dromara.business.dto.KnowledgeCategoryRelationDTO;
import org.dromara.business.service.IKnowledgeCategoryRelationService;
import org.dromara.business.service.IKnowledgeCategoryService;
import org.dromara.business.vo.KnowledgeCategoryVO;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.service.ISysDeptService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 知识库分类 控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/knowledge/category")
public class KnowledgeCategoryController {

    private final IKnowledgeCategoryService categoryService;
    private final IKnowledgeCategoryRelationService relationService;

    /**
     * 查询知识库分类列表（分页）
     */
    @GetMapping("/list")
    public R<TableDataInfo<KnowledgeCategoryVO>> list(KnowledgeCategoryDTO dto) {
        TableDataInfo<KnowledgeCategoryVO> pageResult = categoryService.pageKnowledgeCategories(dto);
        return R.ok(pageResult);
    }

    /**
     * 获取所有分类列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<KnowledgeCategoryVO>> listAll() {
        List<KnowledgeCategoryVO> list = categoryService.listAllCategories();
        return R.ok(list);
    }

    /**
     * 获取知识库分类详细信息
     */
    @GetMapping("/{id}")
    public R<KnowledgeCategoryVO> getInfo(@PathVariable("id") Long id) {
        return R.ok(categoryService.getKnowledgeCategoryById(id));
    }

    /**
     * 新增知识库分类
     */
    @PostMapping
    public R<Void> add(@Validated @RequestBody KnowledgeCategoryDTO dto) {
        return categoryService.addKnowledgeCategory(dto) ? R.ok() : R.fail("新增知识库分类失败");
    }

    /**
     * 修改知识库分类
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody KnowledgeCategoryDTO dto) {
        return categoryService.updateKnowledgeCategory(dto) ? R.ok() : R.fail("修改知识库分类失败");
    }

    /**
     * 删除知识库分类
     */
    @DeleteMapping("/{id}")
    public R<Void> remove(@PathVariable Long id) {
        return categoryService.deleteKnowledgeCategory(id) ? R.ok() : R.fail("删除知识库分类失败");
    }

    /**
     * 绑定知识库到分类
     */
    @PostMapping("/bind")
    public R<Void> bindKnowledges(@Validated @RequestBody KnowledgeCategoryRelationDTO dto) {
        return relationService.bindKnowledgesToCategory(dto) ? R.ok() : R.fail("绑定知识库失败");
    }

    /**
     * 解绑知识库与分类
     */
    @DeleteMapping("/unbind/{categoryId}/{knowledgeId}")
    public R<Void> unbindKnowledge(@PathVariable("categoryId") Long categoryId, @PathVariable("knowledgeId") Long knowledgeId) {
        return relationService.unbindKnowledgeFromCategory(categoryId, knowledgeId) ? R.ok() : R.fail("解绑知识库失败");
    }

    /**
     * 获取分类下的知识库ID列表
     */
    @GetMapping("/knowledges/{categoryId}")
    public R<List<Long>> getKnowledgeIds(@PathVariable("categoryId") Long categoryId) {
        List<Long> knowledgeIds = relationService.getKnowledgeIdsByCategoryId(categoryId);
        return R.ok(knowledgeIds);
    }

    /**
     * 获取知识库所属的分类ID列表
     */
    @GetMapping("/categories/{knowledgeId}")
    public R<List<Long>> getCategoryIds(@PathVariable("knowledgeId") Long knowledgeId) {
        List<Long> categoryIds = relationService.getCategoryIdsByKnowledgeId(knowledgeId);
        return R.ok(categoryIds);
    }

    /**
     * 根据多个部门ID查询分类列表
     */
    @PostMapping("/listByDeptIds")
    public R<List<KnowledgeCategoryVO>> listByDeptIds(@RequestBody List<Long> deptIds) {
        List<KnowledgeCategoryVO> categories = categoryService.listCategoriesByDeptIds(deptIds);
        return R.ok(categories);
    }

    /**
     * 顶级部门分类列表
     */
    @PostMapping("/listTopDept")
    public R<List<KnowledgeCategoryVO>> listTopDept() {
        Long topDeptId = categoryService.topDeptId();
        if (topDeptId == null) {
            return R.ok(List.of());
        }
        return R.ok(categoryService.listCategoriesByDeptIds(Collections.singletonList(topDeptId)));
    }
}
