package org.dromara.business.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.constant.ServiceApiConstant;
import org.dromara.common.json.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
@Slf4j
public class StatisticsServiceImpl4 implements Function<StatisticsServiceImpl4.Request, StatisticsServiceImpl4.Response> {

    private final String HOST = "http://localhost:7001";

    @Override
    public Response apply(Request request) {
        log.info("request:{}", request);
        //调用外部业务服务获取数据
        String url = HOST + ServiceApiConstant.getComDetails;
        Map<String, Object> param = new HashMap<>();
        if(request!=null && StringUtils.isNotBlank(request.companyName)){
            param.put("comName", request.companyName);
        }
        String resString = HttpUtil.get(url, param);
        log.info("response:{}", resString);
        JSONObject resJSON = JSONObject.parseObject(resString);
        JSONObject respost = new JSONObject();
        respost.put("", 0);
        if (resJSON.getInteger("code") == 200) {
            respost.putAll(resJSON.getJSONObject("data"));
        }
        return new Response(JsonUtils.toJsonString(respost));
    }

    public record Request(String companyName) {
    }

    public record Response(String data) {
    }

}
