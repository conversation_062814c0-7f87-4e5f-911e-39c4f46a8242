package org.dromara.business.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.dromara.business.domain.KnowledgeUserRel;
import org.dromara.business.domain.vo.KnowledgeUserRelVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 知识库用户关联Mapper接口
 */
public interface KnowledgeUserRelMapper extends BaseMapperPlus<KnowledgeUserRel, KnowledgeUserRelVo> {

    Long selectKnowledgeCountByUserId(@Param("userId")Long userId);

    List<KnowledgeUserRel> selectSourceTypeByKnowledgeIds(@Param("knowledgeIds") List<Long> knowledgeIds);
    
    /**
     * 批量查询知识库用户关系和部门信息
     * 
     * @param knowledgeIds 知识库ID列表
     * @return 包含知识库ID、来源类型和部门ID的列表
     */
    List<Map<String, Object>> selectSourceTypeAndDeptByKnowledgeIds(@Param("knowledgeIds") List<Long> knowledgeIds);
}
