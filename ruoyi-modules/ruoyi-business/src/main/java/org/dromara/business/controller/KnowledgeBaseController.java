package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.bo.KnowledgeBaseBo;
import org.dromara.business.domain.dto.KnowledgeBaseMyListDTO;
import org.dromara.business.domain.vo.KnowledgeBaseListVo;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.service.IKnowledgeCategoryRelationService;
import org.dromara.business.service.IKnowledgeCategoryService;
import org.dromara.business.utils.baseEntity.PageResult;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.service.IKnowledgeFileService;
import org.dromara.business.service.IKnowledgeUserRelService;
import org.dromara.business.vo.KnowledgeCategoryVO;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/knowledge")
public class KnowledgeBaseController extends BaseController {

    private final IKnowledgeBaseService knowledgeBaseService;
    private final IKnowledgeUserRelService knowledgeUserRelService;
    private final IKnowledgeFileService knowledgeFileService;

    @Resource
    private IKnowledgeCategoryRelationService knowledgeCategoryRelationService;

    @Resource
    private IKnowledgeCategoryService knowledgeCategoryService;

    /**
     * 查询知识库列表根据部门
     */
    @PostMapping("/list")
    public TableDataInfo<KnowledgeBaseVo> list(@RequestBody KnowledgeBaseListVo knowledgeBaseListVo) {
        knowledgeBaseListVo.getKnowledgeBaseBo().setKnowledgeName(knowledgeBaseListVo.getKnowledgeName());
        return knowledgeBaseService.queryPageList(knowledgeBaseListVo.getKnowledgeBaseBo(), knowledgeBaseListVo.getPageQuery(), knowledgeBaseListVo);
    }

    /**
     * 查询当前用户的知识库列表(最终)
     */
    @PostMapping("/my")
    @SaIgnore
    public TableDataInfo<KnowledgeBaseVo> myList(@RequestBody KnowledgeBaseMyListDTO dto) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return TableDataInfo.build(List.of());
        }

        KnowledgeBaseBo bo = new KnowledgeBaseBo();
        // 设置当前用户ID
        bo.setCreateUserId(loginUser.getUserId());
        bo.setDeptId(loginUser.getDeptId());
        bo.setKnowledgeName(dto.getKnowledgeName());
        bo.setCategoryId(dto.getCategoryId());
        IPage<KnowledgeBaseVo> resultList = null;

        String knowledgeBaseType = dto.getKnowledgeBaseType();
        String sortType = dto.getSortType();
        // 查询院级知识库
        if ("institute".equals(knowledgeBaseType)) {
            resultList = knowledgeBaseService.queryInstituteList(bo, sortType,dto);
        }else if ("department".equals(knowledgeBaseType)){
            if (loginUser.getDeptId() == null){
                return TableDataInfo.build(List.of());
            }
            // 使用queryList方法获取列表数据
            resultList = knowledgeBaseService.queryListDepartment(bo, dto.getDeptId(), sortType,dto);
        }else if ("user".equals(knowledgeBaseType)){
            // 使用queryList方法获取列表数据
            resultList = knowledgeBaseService.queryListUser(bo, sortType,dto);
        }else {
            throw new ServiceException("未知的知识库类型");
        }
        // 来源类型source_type赋值
        if (resultList != null) {
            knowledgeBaseService.fillKnowledgeBaseSourceType(resultList.getRecords());
        }

        return TableDataInfo.build(resultList);
    }

    /**
     * 获取知识库详细信息
     */
    @GetMapping("/{knowledgeId}")
    public R<KnowledgeBaseVo> getInfo(@PathVariable Long knowledgeId) {
        List<Long> categoryIdsByKnowledgeId = knowledgeCategoryRelationService.getCategoryIdsByKnowledgeId(knowledgeId);
        KnowledgeBaseVo knowledgeBaseVo = knowledgeBaseService.queryById(knowledgeId);
        if (categoryIdsByKnowledgeId != null && !categoryIdsByKnowledgeId.isEmpty()) {
            knowledgeBaseVo.setCategoryId(categoryIdsByKnowledgeId.get(0));
        }
        return R.ok(knowledgeBaseVo);
    }

    /**
     * 新增知识库
     */
    @Log(title = "知识库管理", businessType = BusinessType.INSERT)
    @PostMapping
    @SaIgnore
    public R<Void> add(@Validated(AddGroup.class) @RequestBody KnowledgeBaseBo bo) {
        // 设置默认值
        if (StringUtils.isEmpty(bo.getStatus())) {
            bo.setStatus("0"); // 默认正常状态
        }
        if (StringUtils.isEmpty(bo.getDataType())) {
            bo.setDataType("1"); // 默认非结构化数据
        }
        // 如果排序字段为空，设置默认排序值为0
        if (bo.getSortOrder() == null) {
            bo.setSortOrder(0);
        }
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        // 如果信息为空，抛出异常
        if (userId == null) {
            return R.fail(HttpServletResponse.SC_UNAUTHORIZED, "请重新登录!");
        }
        // 调用Service层创建知识库并建立用户关联
        return toAjax(knowledgeBaseService.createKnowledgeBase(bo));
    }

    /**
     * 修改知识库
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody KnowledgeBaseBo bo) {
        // 校验权限
//        knowledgeBaseService.checkUpdatePermission(bo);
        return toAjax(knowledgeBaseService.updateByBo(bo));
    }

    /**
     * 删除知识库
     */
    @Log(title = "知识库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{knowledgeIds}")
    public R<Void> remove(@PathVariable Long[] knowledgeIds) {
        return toAjax(knowledgeBaseService.deleteWithValidByIds(List.of(knowledgeIds)));
    }

    /**
     * 修改知识库状态
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody KnowledgeBaseBo bo) {
        if (bo.getKnowledgeId() == null) {
            return R.fail("知识库ID不能为空");
        }
        if (StringUtils.isEmpty(bo.getStatus())) {
            return R.fail("状态不能为空");
        }
        KnowledgeBaseVo vo = knowledgeBaseService.queryById(bo.getKnowledgeId());
        if (vo == null) {
            return R.fail("知识库不存在");
        }
        return toAjax(knowledgeBaseService.updateByBo(bo));
    }



}
