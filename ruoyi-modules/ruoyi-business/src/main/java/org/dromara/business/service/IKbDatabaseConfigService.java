package org.dromara.business.service;

import org.dromara.business.domain.vo.KbDatabaseConfigVo;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 知识库-数据库配置信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IKbDatabaseConfigService {

    /**
     * 查询知识库-数据库配置信息
     *
     * @param databaseConfigId 主键
     * @return 知识库-数据库配置信息
     */
    KbDatabaseConfigVo queryById(Long databaseConfigId);

    /**
     * 分页查询知识库-数据库配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 知识库-数据库配置信息分页列表
     */
    TableDataInfo<KbDatabaseConfigVo> queryPageList(KbDatabaseConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的知识库-数据库配置信息列表
     *
     * @param bo 查询条件
     * @return 知识库-数据库配置信息列表
     */
    List<KbDatabaseConfigVo> queryList(KbDatabaseConfigBo bo);

    /**
     * 新增知识库-数据库配置信息
     *
     * @param bo 知识库-数据库配置信息
     * @return 是否新增成功
     */
    Boolean insertByBo(KbDatabaseConfigBo bo);

    /**
     * 修改知识库-数据库配置信息
     *
     * @param bo 知识库-数据库配置信息
     * @return 是否修改成功
     */
    Boolean updateByBo(KbDatabaseConfigBo bo);

    /**
     * 校验并批量删除知识库-数据库配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据知识库id查询知识库-数据库配置信息
     * @param knowledgeId
     * @return
     */
    KbDatabaseConfigVo getInfoById(Long knowledgeId);
}
