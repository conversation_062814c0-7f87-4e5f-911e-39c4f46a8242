package org.dromara.business.domain.bo;

import org.dromara.business.domain.BsReviewStaff;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 审查人员信息业务对象 bs_review_staff
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BsReviewStaff.class, reverseConvertGenerate = false)
public class BsReviewStaffBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 人员姓名
     */
    @NotBlank(message = "人员姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String personName;

    /**
     * 所属类目
     */
    @NotBlank(message = "所属类目不能为空", groups = { AddGroup.class, EditGroup.class })
    private String categoryKey;

    /**
     * 所属机构
     */
    @NotBlank(message = "所属机构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String organizationKey;


}
