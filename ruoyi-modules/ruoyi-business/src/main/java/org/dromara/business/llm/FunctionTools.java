package org.dromara.business.llm;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.constant.ServiceApiConstant;
import org.dromara.common.json.utils.JsonUtils;
import org.springframework.ai.tool.annotation.Tool;
import java.util.HashMap;
import java.util.Map;

@Log4j2
public class FunctionTools {

    private String HOST = "http://*************:8002";

    @Tool(description = "Query for enterprise or company information")
    String statisticalQuantity(String company) {
        return getCompanyInfo(company);
    }

    @Tool(description = "Generate enterprise reports or portraits")
    String statisticalQuantityTwo(String companyName) {
        return getBusinessPortraits(companyName);
    }

    @Tool(description = "Search for the top ten enterprise names in a county or district based on their rating rankings")
    String statisticalQuantityThree(String areaName) {
        return getComScore(areaName);
    }


    /**
     * 获取公司评分前十
     * @param areaName
     * @return
     */
    private String getComScore(String areaName) {
        try {
            if (StringUtils.isBlank(areaName)) {
                return "请输入县区名称";
            }
            log.info("request:{}", areaName);
            //调用外部业务服务获取数据
            String url = HOST + ServiceApiConstant.getComScore;
            Map<String, Object> param = new HashMap<>();
            param.put("areaName", areaName);
            String resString = HttpUtil.get(url, param);
            log.info("response:{}", resString);
            Object data = "无结果";
            try {
                // 解析JSON字符串
                JSONObject resJSON = JSONObject.parseObject(resString);
                // 检查并取出data字段
                if (resJSON.getInteger("code")== 200){
                    if (resJSON.containsKey("data")) {
                        data = resJSON.get("data");
                    } else {
                        return "无结果";
                    }
                }
            } catch (Exception e) {
                return "无结果";
            }
            return JsonUtils.toJsonString(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            return "无结果";
        }
    }

    /**
     * 进行企业画像
     */
    private String getBusinessPortraits(String companyName) {
        try {
            if (StringUtils.isBlank(companyName)) {
                return "请输入企业名称";
            }
            log.info("request:{}", companyName);
            //调用外部业务服务获取数据
            String url = HOST + ServiceApiConstant.businessPortraits;
            Map<String, Object> param = new HashMap<>();
            param.put("companyName", companyName);
            String resString = HttpUtil.get(url, param);
            log.info("response:{}", resString);
            Object data = "无结果";
            try {
                // 解析JSON字符串
                JSONObject resJSON = JSONObject.parseObject(resString);
                // 检查并取出data字段
                if (resJSON.getInteger("code")== 200){
                    if (resJSON.containsKey("data")) {
                        data = resJSON.get("data");
                    } else {
                        return "无结果";
                    }
                }
            } catch (Exception e) {
                return "无结果";
            }
            return JsonUtils.toJsonString(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            return "无结果";
        }
    }

    /**
     * 获取公司信息
     */
    private String getCompanyInfo(String company) {
        try {
            log.info("request:{}", company);
            //调用外部业务服务获取数据
            String url = HOST + ServiceApiConstant.getComDetails;
            Map<String, Object> param = new HashMap<>();
            if (StringUtils.isNotBlank(company)) {
                param.put("comName", company);
            }
            String resString = HttpUtil.get(url, param);
            log.info("response:{}", resString);
            JSONObject resJSON = JSON.parseObject(resString);
            JSONObject respost = new JSONObject();
            respost.put("", 0);
            if (resJSON.getInteger("code") == 200) {
                JSONObject resObj = resJSON.getJSONObject("data");
                if(resObj.get("score") != null){
                    respost.put("企业评分", resObj.get("score"));
                }
                if(resObj.get("comRank") != null){
                    respost.put("企业等级", resObj.get("comRank"));
                }
                if(resObj.get("reportUrl") != null){
                    respost.put("企业画像报告", resObj.get("reportUrl"));
                }
                respost.putAll(resJSON.getJSONObject("data"));
            }
            return JsonUtils.toJsonString(respost);
        } catch (Exception e) {
            log.error(e.getMessage());
            return "无结果";
        }

    }

}
