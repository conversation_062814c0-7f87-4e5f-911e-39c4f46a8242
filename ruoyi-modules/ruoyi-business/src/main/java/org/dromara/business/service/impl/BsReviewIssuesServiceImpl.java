package org.dromara.business.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.BsReviewIssues;
import org.dromara.business.domain.bo.BsReviewIssuesBo;
import org.dromara.business.domain.vo.BsReviewIssuesImportVO;
import org.dromara.business.domain.vo.BsReviewIssuesVo;
import org.dromara.business.mapper.BsReviewIssuesMapper;
import org.dromara.business.service.IBsReviewIssuesService;
import org.dromara.business.service.IBsReviewStaffService;
import org.dromara.business.support.listener.ReviewIssuesImportListener;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 审查问题记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BsReviewIssuesServiceImpl implements IBsReviewIssuesService {

    private final BsReviewIssuesMapper baseMapper;
    private final IBsReviewStaffService reviewStaffService;
    
    // 线程安全的内存缓存，存储导入进度
    private static final ConcurrentHashMap<String, ImportProgressInfo> IMPORT_PROGRESS_CACHE = new ConcurrentHashMap<>();
    
    // 定时清理过期缓存的调度器
    private static final ScheduledExecutorService CACHE_CLEANER = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ImportProgressCacheCleaner");
        t.setDaemon(true);
        return t;
    });
    
    // 缓存过期时间（小时）
    private static final int CACHE_EXPIRE_HOURS = 24;
    
    static {
        // 每小时清理一次过期缓存
        CACHE_CLEANER.scheduleAtFixedRate(() -> {
            try {
                cleanExpiredCache();
            } catch (Exception e) {
                // 使用System.err避免日志依赖问题
                System.err.println("清理导入进度缓存时发生错误: " + e.getMessage());
            }
        }, 1, 1, TimeUnit.HOURS);
    }
    
    /**
     * 导入进度信息类
     */
    private static class ImportProgressInfo {
        private volatile int percentage;
        private volatile String status;
        private volatile String message;
        private volatile int totalRows;
        private volatile int processedRows;
        private volatile LocalDateTime startTime;
        private volatile LocalDateTime endTime;
        
        public ImportProgressInfo() {
            this.percentage = 0;
            this.status = "processing";
            this.message = "开始导入...";
            this.totalRows = 0;
            this.processedRows = 0;
            this.startTime = LocalDateTime.now();
        }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("percentage", percentage);
            map.put("status", status);
            map.put("message", message);
            map.put("totalRows", totalRows);
            map.put("processedRows", processedRows);
            map.put("startTime", startTime);
            map.put("endTime", endTime);
            return map;
        }
        
        // Getters and Setters
        public int getPercentage() { return percentage; }
        public void setPercentage(int percentage) { this.percentage = percentage; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public int getTotalRows() { return totalRows; }
        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        public int getProcessedRows() { return processedRows; }
        public void setProcessedRows(int processedRows) { this.processedRows = processedRows; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    }
    
    /**
     * 清理过期的缓存
     */
    private static void cleanExpiredCache() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(CACHE_EXPIRE_HOURS);
        IMPORT_PROGRESS_CACHE.entrySet().removeIf(entry -> {
            ImportProgressInfo info = entry.getValue();
            return info.getStartTime().isBefore(expireTime);
        });
    }

    /**
     * 查询审查问题记录
     *
     * @param id 主键
     * @return 审查问题记录
     */
    @Override
    public BsReviewIssuesVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询审查问题记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审查问题记录分页列表
     */
    @Override
    public TableDataInfo<BsReviewIssuesVo> queryPageList(BsReviewIssuesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BsReviewIssues> lqw = buildQueryWrapper(bo);
        Page<BsReviewIssuesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的审查问题记录列表
     *
     * @param bo 查询条件
     * @return 审查问题记录列表
     */
    @Override
    public List<BsReviewIssuesVo> queryList(BsReviewIssuesBo bo) {
        LambdaQueryWrapper<BsReviewIssues> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BsReviewIssues> buildQueryWrapper(BsReviewIssuesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BsReviewIssues> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getReviewComments()), BsReviewIssues::getReviewComments, bo.getReviewComments());
        lqw.like(StringUtils.isNotBlank(bo.getSpecificationClause()), BsReviewIssues::getSpecificationClause, bo.getSpecificationClause());
        lqw.eq(StringUtils.isNotBlank(bo.getCommentType()), BsReviewIssues::getCommentType, bo.getCommentType());
        lqw.like(StringUtils.isNotBlank(bo.getCategorys()), BsReviewIssues::getCategorys, bo.getCategorys());
        lqw.eq(StringUtils.isNotBlank(bo.getProposers()), BsReviewIssues::getProposers, bo.getProposers());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectNames()), BsReviewIssues::getProjectNames, bo.getProjectNames());
        lqw.eq(StringUtils.isNotBlank(bo.getDesignUnits()), BsReviewIssues::getDesignUnits, bo.getDesignUnits());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            BsReviewIssues::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        return lqw;
    }

    /**
     * 新增审查问题记录
     *
     * @param bo 审查问题记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BsReviewIssuesBo bo) {
        BsReviewIssues add = MapstructUtils.convert(bo, BsReviewIssues.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改审查问题记录
     *
     * @param bo 审查问题记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BsReviewIssuesBo bo) {
        BsReviewIssues update = MapstructUtils.convert(bo, BsReviewIssues.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BsReviewIssues entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除审查问题记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<BsReviewIssues> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 处理重复数据：查询已存在的审查意见，删除重复的旧数据
     *
     * @param importedIssues 导入的数据列表
     */
    private void processDuplicateData(List<BsReviewIssues> importedIssues) {
        if (importedIssues == null || importedIssues.isEmpty()) {
            return;
        }

        // 提取所有导入数据的审查意见
        List<String> reviewCommentsList = importedIssues.stream()
            .map(BsReviewIssues::getReviewComments)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (reviewCommentsList.isEmpty()) {
            return;
        }

        // 查询数据库中已存在的同名审查意见记录
        List<BsReviewIssues> existingIssues = baseMapper.selectByReviewComments(reviewCommentsList);

        if (!existingIssues.isEmpty()) {
            // 提取需要删除的旧数据ID
            List<Long> idsToDelete = existingIssues.stream()
                .map(BsReviewIssues::getId)
                .collect(Collectors.toList());

            // 批量删除重复的旧数据
            if (!idsToDelete.isEmpty()) {
                baseMapper.deleteByIds(idsToDelete);
                log.info("删除重复的审查问题记录 {} 条", idsToDelete.size());
            }
        }
    }

    /**
     * 查询所有意见类型可选项
     *
     * @return 意见类型列表
     */
    @Override
    public List<String> getCommentTypeOptions() {
        return baseMapper.selectCommentTypeOptions();
    }

    /**
     * 初始化导入进度
     *
     * @param importNo 导入流水号
     */
    private void initImportProgress(String importNo) {
        ImportProgressInfo progressInfo = new ImportProgressInfo();
        IMPORT_PROGRESS_CACHE.put(importNo, progressInfo);
        log.info("初始化导入进度，流水号：{}，当前线程：{}", importNo, Thread.currentThread().getName());
    }

    /**
     * 更新导入进度
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     * @param processedRows 已处理行数
     */
    private void updateImportProgress(String importNo, int percentage, String message, int processedRows) {
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(percentage);
            progressInfo.setMessage(message);
            if (processedRows > 0) {
                progressInfo.setProcessedRows(processedRows);
            }
            log.info("更新导入进度，流水号：{}，进度：{}%，消息：{}，已处理：{}行，总计：{}行", 
                    importNo, percentage, message, progressInfo.getProcessedRows(), progressInfo.getTotalRows());
        }
    }
    
    /**
     * 从监听器更新导入进度（仅更新百分比和消息，不更新行数）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     */
    @Override
    public void updateImportProgressFromListener(String importNo, int percentage, String message) {
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(percentage);
            progressInfo.setMessage(message);
            log.debug("从监听器更新导入进度，流水号：{}，进度：{}%，消息：{}", importNo, percentage, message);
        } else {
            log.warn("从监听器更新进度时未找到缓存，流水号：{}", importNo);
        }
    }
    
    /**
     * 从监听器更新导入进度（包含行数信息）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     * @param totalRows 总行数
     * @param processedRows 已处理行数
     */
    @Override
    public void updateImportProgressWithRowsFromListener(String importNo, int percentage, String message, int totalRows, int processedRows) {
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(percentage);
            progressInfo.setMessage(message);
            
            // 更新行数信息
            if (totalRows > 0) {
                progressInfo.setTotalRows(totalRows);
            }
            if (processedRows >= 0) {
                progressInfo.setProcessedRows(processedRows);
            }
            
            log.debug("从监听器更新导入进度和行数，流水号：{}，进度：{}%，消息：{}，总行数：{}，已处理：{}行", 
                    importNo, percentage, message, totalRows, processedRows);
        } else {
            log.warn("从监听器更新进度时未找到缓存，流水号：{}", importNo);
        }
    }

    /**
     * 完成导入进度
     *
     * @param importNo 导入流水号
     * @param importedCount 实际导入的记录数
     */
    private void completeImportProgress(String importNo, int importedCount) {
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            progressInfo.setPercentage(100);
            progressInfo.setStatus("completed");
            progressInfo.setMessage("导入完成");
            progressInfo.setEndTime(LocalDateTime.now());
            
            // 最终确保行数不为0
            int currentTotalRows = progressInfo.getTotalRows();
            int currentProcessedRows = progressInfo.getProcessedRows();
            
            // 如果总行数为0，使用实际导入的记录数
            if (currentTotalRows == 0) {
                progressInfo.setTotalRows(importedCount);
                log.info("总行数为0，设置为实际导入记录数：{}，流水号：{}", importedCount, importNo);
            }
            
            // 如果已处理行数为0，使用实际导入的记录数
            if (currentProcessedRows == 0) {
                progressInfo.setProcessedRows(importedCount);
                log.info("已处理行数为0，设置为实际导入记录数：{}，流水号：{}", importedCount, importNo);
            }
            
            log.info("导入完成，流水号：{}，总行数：{}，已处理：{}行，实际导入：{}条记录", 
                    importNo, progressInfo.getTotalRows(), progressInfo.getProcessedRows(), importedCount);
        } else {
            log.error("完成导入时未找到进度信息，流水号：{}", importNo);
        }
    }

    /**
     * 设置导入失败状态
     *
     * @param importNo 导入流水号
     * @param errorMessage 错误消息
     */
    private void failImportProgress(String importNo, String errorMessage) {
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            progressInfo.setStatus("failed");
            progressInfo.setMessage("导入失败：" + errorMessage);
            progressInfo.setEndTime(LocalDateTime.now());
            log.error("导入失败，流水号：{}，错误：{}", importNo, errorMessage);
        }
    }

    /**
     * 查询导入进度
     *
     * @param importNo 导入流水号
     * @return 进度信息
     */
    @Override
    public Map<String, Object> getImportProgress(String importNo) {
        log.info("查询导入进度，流水号：{}，当前线程：{}", importNo, Thread.currentThread().getName());

        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            log.info("找到导入进度记录，流水号：{}，状态：{}", importNo, progressInfo.getStatus());
            return progressInfo.toMap();
        } else {
            log.warn("未找到导入进度记录，流水号：{}", importNo);
            
            // 如果没有找到进度信息，返回默认状态
            Map<String, Object> progress = new HashMap<>();
            progress.put("percentage", 0);
            progress.put("status", "not_found");
            progress.put("message", "未找到导入记录，可能还未开始处理或已过期");
            progress.put("totalRows", 0);
            progress.put("processedRows", 0);
            return progress;
        }
    }

    /**
     * 异步导入审查问题
     *
     * @param file 要导入的 Excel 文件
     * @param importNo 导入流水号
     */
    @Override
    @Async
    public void importIssuesAsync(MultipartFile file, String importNo) {
        try {
            // 立即初始化进度信息
            initImportProgress(importNo);

            // 调用实际的处理逻辑
            processImportIssues(file, importNo);
        } catch (Exception e) {
            log.error("异步导入审查问题失败，流水号：{}，错误：{}", importNo, e.getMessage(), e);
            // 确保进度状态被标记为失败
            failImportProgress(importNo, e.getMessage());
        }
    }

    /**
     * 异步导入审查问题（不初始化进度）
     *
     * @param file 要导入的 Excel 文件
     * @param importNo 导入流水号
     */
    @Override
    @Async
    public void importIssuesAsyncWithoutInit(MultipartFile file, String importNo) {
        try {
            // 调用实际的处理逻辑（不初始化进度）
            processImportIssues(file, importNo);
        } catch (Exception e) {
            log.error("异步导入审查问题失败，流水号：{}，错误：{}", importNo, e.getMessage(), e);
            // 确保进度状态被标记为失败
            failImportProgress(importNo, e.getMessage());
        }
    }

    /**
     * 在Controller线程中初始化导入进度
     *
     * @param importNo 导入流水号
     */
    @Override
    public void initImportProgressInController(String importNo) {
        initImportProgress(importNo);
    }

    /**
     * 处理导入逻辑（不包括初始化进度）
     */
    private void processImportIssues(MultipartFile file, String importNo) throws Exception {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传的文件不能为空");
        }

        updateImportProgress(importNo, 10, "开始解析Excel文件...", 0);

        ReviewIssuesImportListener listener = new ReviewIssuesImportListener(reviewStaffService, importNo, this);

        // 读取Excel文件，并使用自定义的监听器
        EasyExcel.read(file.getInputStream(), BsReviewIssuesImportVO.class, listener)
                 .sheet() // 读取第一个sheet
                 .doRead();

        List<BsReviewIssues> importedIssues = listener.getImportedIssues();
        
        // 获取实际的行数信息
        int totalRows = listener.getTotalRowCount();
        int processedRows = listener.getProcessedRowCount();
        
        log.info("Excel解析完成，流水号：{}，监听器返回 - 总行数：{}，已处理行数：{}，导入记录数：{}", 
                importNo, totalRows, processedRows, importedIssues.size());
        
        // 由于行数信息已经由监听器实时更新，这里只需要验证和记录
        ImportProgressInfo progressInfo = IMPORT_PROGRESS_CACHE.get(importNo);
        if (progressInfo != null) {
            log.info("Excel解析完成，流水号：{}，进度缓存中的行数信息 - 总行数：{}，已处理：{}行，实际导入记录数：{}", 
                    importNo, progressInfo.getTotalRows(), progressInfo.getProcessedRows(), importedIssues.size());
            
            // 如果监听器没有更新行数信息（边缘情况），则使用回退机制
            if (progressInfo.getTotalRows() == 0 && progressInfo.getProcessedRows() == 0) {
                int fallbackRows = Math.max(Math.max(totalRows, processedRows), importedIssues.size());
                progressInfo.setTotalRows(fallbackRows);
                progressInfo.setProcessedRows(fallbackRows);
                log.warn("监听器未更新行数信息，使用回退值：{}，流水号：{}", fallbackRows, importNo);
            }
        } else {
            log.warn("未找到进度信息缓存，流水号：{}", importNo);
        }

        // 更新进度：Excel解析完成
        updateImportProgress(importNo, 70, "Excel解析完成，正在处理重复数据...", importedIssues.size());

        // 处理重复数据：先查询已存在的审查意见，删除重复的旧数据
        processDuplicateData(importedIssues);

        // 更新进度：数据保存阶段
        updateImportProgress(importNo, 90, "正在保存数据...", importedIssues.size());

        baseMapper.insertBatch(importedIssues);

        // 完成导入
        completeImportProgress(importNo, importedIssues.size());
        log.info("成功导入 {} 条审查问题记录。", importedIssues.size());
    }

    /**
     * 内存中的MultipartFile实现类
     */
    public static class InMemoryMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;

        public InMemoryMultipartFile(byte[] content, String originalFilename) {
            this.content = content;
            this.name = "file";
            this.originalFilename = originalFilename;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException {
            throw new UnsupportedOperationException("transferTo not supported");
        }
    }
}
