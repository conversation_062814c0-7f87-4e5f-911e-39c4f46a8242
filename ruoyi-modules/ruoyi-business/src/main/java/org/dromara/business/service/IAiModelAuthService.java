package org.dromara.business.service;

import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.domain.vo.AiModelAuthVo;
import org.dromara.business.domain.bo.AiModelAuthBo;
import org.dromara.business.vo.AiModelAuthCountVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模型授权Service接口
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface IAiModelAuthService {

    /**
     * 查询模型授权
     *
     * @param id 主键
     * @return 模型授权
     */
    AiModelAuthVo queryById(Long id);

    /**
     * 分页查询模型授权列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模型授权分页列表
     */
    TableDataInfo<AiModelAuthVo> queryPageList(AiModelAuthBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的模型授权列表
     *
     * @param bo 查询条件
     * @return 模型授权列表
     */
    List<AiModelAuthVo> queryList(AiModelAuthBo bo);

    /**
     * 新增模型授权
     *
     * @param bo 模型授权
     * @return 是否新增成功
     */
    Boolean insertByBo(AiModelAuthBo bo);

    /**
     * 修改模型授权
     *
     * @param bo 模型授权
     * @return 是否修改成功
     */
    Boolean updateByBo(AiModelAuthBo bo);

    /**
     * 校验并批量删除模型授权信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 模型key绑定用户id
     * @param bo
     * @return
     */
    int bindUser(AiModelAuthBo bo);

    Map<String, String> getInfoByKey(String modelKey);

    Map<String,String> getUserByKey();

    AiModelAuth getAuthInChat(String modelKey);

    List<AiModelAuthCountVO> getAuthModelInfo();

    List<AiModelAuthCountVO> getAuthModelInfoAll();
}
