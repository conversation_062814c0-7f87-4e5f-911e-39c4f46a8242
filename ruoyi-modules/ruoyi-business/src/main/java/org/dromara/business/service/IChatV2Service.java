package org.dromara.business.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendReqVO;
import org.dromara.common.core.domain.vo.ChatMessageSendRespVO;
import reactor.core.publisher.Flux;

/**
 * 数据归纳Service接口
 */
public interface IChatV2Service {

    Flux<R<ChatMessageSendRespVO>> generateStreamChat(ChatMessageSendReqVO chatMessageSendReqVO, String lockKey);

}
