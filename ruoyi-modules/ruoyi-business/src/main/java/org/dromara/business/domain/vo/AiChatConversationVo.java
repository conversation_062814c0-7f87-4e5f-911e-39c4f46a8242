package org.dromara.business.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;



/**
 * AI 聊天对话视图对象 ai_chat_conversation
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@ExcelIgnoreUnannotated
public class AiChatConversationVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private static final long maxContextsCount = 10L;


    /**
     * 对话编号
     */
    @ExcelProperty(value = "对话编号")
    private Long id;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private Long userId;

    /**
     * 知识库编号
     */
    @ExcelProperty(value = "知识库编号")
    private Long knowledgeId;

    /**
     * 对话标题
     */
    @ExcelProperty(value = "对话标题")
    private String title;

    /**
     * 是否置顶
     */
    @ExcelProperty(value = "是否置顶")
    private Integer pinned;

    /**
     * 置顶时间
     */
    @ExcelProperty(value = "置顶时间")
    private Date pinnedTime;

    /**
     * 角色设定
     */
    @ExcelProperty(value = "角色设定")
    private String systemMessage;

    /**
     * 温度参数
     */
    @ExcelProperty(value = "温度参数")
    private Double temperature;

    /**
     * 单条回复的最大 Token 数量
     */
    @ExcelProperty(value = "单条回复的最大 Token 数量")
    private Long maxTokens;

    /**
     * 上下文的最大 Message 数量
     */
    @ExcelProperty(value = "上下文的最大 Message 数量")
    private Integer maxContexts;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 模型logo地址
     */
    private String modelLogoUrl;

    /**
     * 使用单位
     */
    private String userCom;

    /**
     * 使用项目
     */
    private String project;

    /**
     * 浏览器标志
     */
    private String browserId;

}
