package org.dromara.business.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 施工图项目受理清单统计记录对象 bs_project_accept_statistics
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bs_project_accept_statistics")
public class BsProjectAcceptStatistics extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 统计起始时间
     */
    private Date startDate;

    /**
     * 统计结束日期
     */
    private Date endDate;

    /**
     * 统计状态0分析中 1分析完成
     */
    private String status;

    /**
     * 统计报表文件ID
     */
    private Long ossId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
