package org.dromara.business.domain.dto;

import lombok.Data;
import org.dromara.business.utils.baseEntity.PageBase;
import org.dromara.common.mybatis.core.page.PageQuery;

/**
 * 我的知识库列表查询DTO
 */
@Data
public class KnowledgeBaseMyListDTO  {

    /**
     * 知识库类型（institute-院级，department-部门级，user-个人级）
     */
    private String knowledgeBaseType;

    /**
     * 知识库名称（模糊查询）
     */
    private String knowledgeName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 排序方式（asc-升序，desc-降序）
     */
    private String sortType;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分页参数
     */
    private PageQuery pageQuery;
}
