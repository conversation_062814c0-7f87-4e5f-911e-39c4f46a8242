package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.business.domain.bo.BsReviewIssuesBo;
import org.dromara.business.domain.vo.BsReviewIssuesVo;
import org.dromara.business.service.IBsReviewIssuesService;
import org.dromara.business.service.impl.BsReviewIssuesServiceImpl;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.awt.Color;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审查问题记录
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/reviewIssues")
public class BsReviewIssuesController extends BaseController {

    private final IBsReviewIssuesService bsReviewIssuesService;

    /**
     * 查询审查问题记录列表
     */
    @SaCheckPermission("business:reviewIssues:list")
    @GetMapping("/list")
    public TableDataInfo<BsReviewIssuesVo> list(BsReviewIssuesBo bo, PageQuery pageQuery) {
        return bsReviewIssuesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出审查问题记录列表
     */
    @SaCheckPermission("business:reviewIssues:export")
    @Log(title = "审查问题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BsReviewIssuesBo bo, HttpServletResponse response) {
        List<BsReviewIssuesVo> list = bsReviewIssuesService.queryList(bo);
        ExcelUtil.exportExcel(list, "审查问题记录", BsReviewIssuesVo.class, response);
    }

    /**
     * 获取审查问题记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:reviewIssues:query")
    @GetMapping("/{id}")
    public R<BsReviewIssuesVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(bsReviewIssuesService.queryById(id));
    }

    /**
     * 新增审查问题记录
     */
    @SaCheckPermission("business:reviewIssues:add")
    @Log(title = "审查问题记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BsReviewIssuesBo bo) {
        return toAjax(bsReviewIssuesService.insertByBo(bo));
    }

    /**
     * 修改审查问题记录
     */
    @SaCheckPermission("business:reviewIssues:edit")
    @Log(title = "审查问题记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BsReviewIssuesBo bo) {
        return toAjax(bsReviewIssuesService.updateByBo(bo));
    }

    /**
     * 删除审查问题记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:reviewIssues:remove")
    @Log(title = "审查问题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bsReviewIssuesService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 数据导入
     * @param file 导入文件
     * @param importNo 导入流水号
     * @return 导入结果
     */
    @PostMapping("/upload")
    public R<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file,
                                             @RequestParam("importNo") String importNo) throws IOException {
        if (file.isEmpty()) {
            return R.fail("上传的文件不能为空");
        }

        // 在Controller线程中立即初始化进度，确保能被查询到
        bsReviewIssuesService.initImportProgressInController(importNo);

        // 先将文件内容读取到内存中，避免异步执行时临时文件被删除
        byte[] fileBytes = file.getBytes();
        String originalFilename = file.getOriginalFilename();

        // 创建内存中的MultipartFile实现
        MultipartFile memoryFile = new BsReviewIssuesServiceImpl.InMemoryMultipartFile(fileBytes, originalFilename);

        // 异步开始导入处理，立即返回响应（不再在异步方法中初始化进度）
        bsReviewIssuesService.importIssuesAsyncWithoutInit(memoryFile, importNo);

        // 返回导入流水号，客户端可通过此流水号查询进度
        Map<String, Object> result = new HashMap<>();
        result.put("importNo", importNo);
        result.put("message", "文件上传成功，数据处理中...");
        return R.ok(result);
    }

    /**
     * 查询导入进度
     * @param importNo 导入流水号
     * @return 导入进度信息
     */
    @GetMapping("/importProgress/{importNo}")
    public R<Map<String, Object>> getImportProgress(@PathVariable String importNo) {
        Map<String, Object> progress = bsReviewIssuesService.getImportProgress(importNo);
        return R.ok(progress);
    }

    /**
     * 获取意见类型可选项
     */
    @GetMapping("/commentTypeOptions")
    public R<List<String>> getCommentTypeOptions() {
        return R.ok(bsReviewIssuesService.getCommentTypeOptions());
    }

    /**
     * 下载导入模板
     */
    @SaCheckPermission("business:reviewIssues:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("审查记录导入模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 使用 Apache POI 创建 Excel 模板
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("审查记录导入模板");

            // 设置整个工作表的默认行高为22点
            sheet.setDefaultRowHeightInPoints(22);

            // 创建表头行
            Row headerRow = sheet.createRow(0);

            // 定义表头数据
            String[] headers = {"序号", "审查意见", "规范条文", "提出次数", "意见类型", "提出人", "项目名称", "设计单位"};

            // 创建表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 创建表头单元格和设置列宽
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 7500); // 15个字符宽度
            }

            workbook.write(response.getOutputStream());
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();

        // 设置对齐方式
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置背景色和边框
        if (workbook instanceof XSSFWorkbook) {
            org.apache.poi.xssf.usermodel.XSSFCellStyle xssfStyle =
                (org.apache.poi.xssf.usermodel.XSSFCellStyle) headerStyle;

            // 背景色 #e6f1f8
            XSSFColor backgroundColor = new XSSFColor(new Color(230, 241, 248), null);
            xssfStyle.setFillForegroundColor(backgroundColor);
            xssfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 边框色 #cfe6f5
            XSSFColor borderColor = new XSSFColor(new Color(207, 230, 245), null);
            xssfStyle.setBorderTop(BorderStyle.MEDIUM);
            xssfStyle.setBorderBottom(BorderStyle.MEDIUM);
            xssfStyle.setBorderLeft(BorderStyle.MEDIUM);
            xssfStyle.setBorderRight(BorderStyle.MEDIUM);
            xssfStyle.setTopBorderColor(borderColor);
            xssfStyle.setBottomBorderColor(borderColor);
            xssfStyle.setLeftBorderColor(borderColor);
            xssfStyle.setRightBorderColor(borderColor);
        } else {
            // 兼容旧版本 Excel
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.MEDIUM);
            headerStyle.setBorderBottom(BorderStyle.MEDIUM);
            headerStyle.setBorderLeft(BorderStyle.MEDIUM);
            headerStyle.setBorderRight(BorderStyle.MEDIUM);
        }

        // 设置字体
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        return headerStyle;
    }
}
