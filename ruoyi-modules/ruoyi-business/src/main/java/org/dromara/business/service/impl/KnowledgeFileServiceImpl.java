package org.dromara.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import jakarta.validation.constraints.NotNull;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.domain.KnowledgeFile;
import org.dromara.business.domain.bo.KnowledgeFileBo;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.domain.vo.KnowledgeFileVo;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.business.mapper.KnowledgeFileMapper;
import org.dromara.business.service.IDataGovernanceService;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.service.IKnowledgeFileService;
import org.dromara.business.utils.VectorServiceUtils;
import org.dromara.common.core.config.KnowledgeConfig;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.impl.SysOssServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 知识库文件服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeFileServiceImpl implements IKnowledgeFileService {

    private final KnowledgeFileMapper baseMapper;
    private final DataSaveServiceImpl dataSaveService;
    private final IDataGovernanceService dataGovernanceService;
    private final SysOssServiceImpl sysOssService;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final KnowledgeConfig knowledgeConfig;

    @Lazy
    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    // 定义空白字符数组，方便以后增加特殊字符
    static final char[] blankChars = {'\n', '\t', '\r'};
    /**
     * 查询知识库文件
     *
     * @param fileId 文件主键
     * @return 知识库文件
     */
    @Override
    public KnowledgeFileVo queryById(Long fileId) {
        return baseMapper.selectVoById(fileId);
    }

    /**
     * 查询知识库文件列表
     *
     * @param bo 知识库文件
     * @return 知识库文件
     */
    @Override
    public TableDataInfo<KnowledgeFileVo> queryPageList(KnowledgeFileBo bo, PageQuery pageQuery) {
        return baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo));
    }


    /**
     * 新增知识库文件
     *
     * @param bo 知识库文件
     * @return 结果
     */
    @Override
    public Boolean insertByBo(KnowledgeFileBo bo) {
        KnowledgeFile add = MapstructUtils.convert(bo, KnowledgeFile.class);
        // 默认状态为处理中
        if (StringUtils.isEmpty(add.getStatus())) {
            add.setStatus("0");
        }
        if (baseMapper.insert(add) > 0){
            bo.setFileId(add.getFileId());
            return true;
        }else {
            return false;
        }

    }

    /**
     * 修改知识库文件
     *
     * @param bo 知识库文件
     * @return 结果
     */
    @Override
    public Boolean updateByBo(KnowledgeFileBo bo) {
        KnowledgeFile update = MapstructUtils.convert(bo, KnowledgeFile.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除知识库文件
     *
     * @param ids 需要删除的知识库文件主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        // 先批量查询所有文件信息，减少数据库查询次数
        List<KnowledgeFile> fileList = baseMapper.selectByIds(ids);
        if (CollectionUtils.isEmpty(fileList)) {
            return false;
        }

        // 先删除数据库记录，保证数据库操作的快速完成
        boolean result = baseMapper.deleteByIds(ids) > 0;
        // 获取当前用户信息
        // 用户登录信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        //如果用户信息为空，抛出异常
        if (loginUser == null) {
            throw new ServiceException("用户信息为空，请重新登录");
        }
        // 异步处理向量删除，不阻塞主流程
        CompletableFuture.runAsync(() -> {
            log.info("开始异步删除知识库文件向量数据，文件数量：{}", fileList.size());
            for (KnowledgeFile file : fileList) {
                try {
                    // 在异步线程中使用传递的loginUser
                    dataGovernanceService.vectorDelete(file,loginUser);
                    log.info("成功删除文件向量数据，fileId={}，用户：{}", file.getFileId(), loginUser.getUsername());
                } catch (Exception e) {
                    log.error("删除文件向量数据失败，fileId={}，用户：{}，错误：{}", file.getFileId(), loginUser.getUsername(), e.getMessage(), e);
                    // 异步删除失败不影响主流程，只记录日志
                }
            }
            log.info("完成异步删除知识库文件向量数据，操作用户：{}", loginUser.getUsername());
        });

        return result;
    }

    /**
     * 上传非结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @param fileDescription 文件描述
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class,ExecutionException.class})
    public Boolean uploadUnstructuredFile(Long knowledgeId, Long ossId, String fileDescription) {
        // 记录创建文件记录开始时间
        long startTime = System.currentTimeMillis();
        // 创建文件记录
        KnowledgeFileBo fileBo = createFileRecord(knowledgeId, ossId, fileDescription);
        // 记录创建文件记录结束时间
        long endTime = System.currentTimeMillis();
        // 计算创建文件记录耗时
        long elapsedTime = endTime - startTime;
        log.info("创建文件记录耗时: {} 毫秒", elapsedTime);

        // 用户登录信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        //如果用户信息为空，抛出异常
        if (loginUser == null) {
            throw new ServiceException("用户信息为空，请重新登录");
        }
        // 用户手机号
        String mobile = loginUser.getUsername();
        // 用户类型
        String userType = loginUser.getUserType();

        try {
            // 查询知识库类型
            String knowledgeType = knowledgeBaseMapper.selectById(knowledgeId).getKnowledgeType();
            List<Long> fileIds = new ArrayList<>();
            fileIds.add(ossId);

            // 记录开始时间
            long startTime1 = System.currentTimeMillis();
            // 获取文件内容
            SysOssVo fileContentSegment = dataSaveService.getFileContentSegment(ossId);
            // 记录结束时间
            long endTime1 = System.currentTimeMillis();
            // 计算耗时
            long elapsedTime1 = endTime1 - startTime1;
            log.info("获取文件内容耗时: {} 毫秒", elapsedTime1);

            // 调用向量接口接口
            if (StringUtil.isNotEmpty(fileContentSegment.getFileContent()) &&
                !isOnlyContainsBlankChars(fileContentSegment.getFileContent(), blankChars)) {
                // 调用向量存储接口，传入用户信息
                String result = vectorStoreWithUserInfo(knowledgeType,knowledgeId, fileBo.getFileId(),
                    fileContentSegment, mobile, userType);
                if ("success".equals(result)) {
                    updateFileStatus(fileBo.getFileId(), "1", null);
                    log.info("文件向量存储成功：fileId={}", fileBo.getFileId());
                } else if (result == null && (fileContentSegment.getFileParagraphs() == null || fileContentSegment.getFileParagraphs().isEmpty())) {
                    // 文件片段列表为空，将状态设置为成功
                    updateFileStatus(fileBo.getFileId(), "1", null);
                    log.info("文件没有可处理的片段，但这是预期的情况，设置为成功：fileId={}", fileBo.getFileId());
                } else {
                    // 处理失败，抛出异常触发事务回滚
                    log.error("网络波动文件传输失败，请重试：fileId={}", fileBo.getFileId());
                    throw new ServiceException("网络波动文件传输失败，请重试");
                }
            } else {
                // 处理失败，抛出异常触发事务回滚
                log.error("无法识别到有效的文字内容，请重换文件：fileId={}", fileBo.getFileId());
                throw new ServiceException("无法识别到有效的文字内容，请重换文件");
            }
        } catch (ExecutionException e) {
            // 业务异常直接抛出，触发事务回滚
            Throwable cause = e.getCause();
            if (cause instanceof ServiceException) {
                throw (ServiceException) cause; // 抛出业务异常
            }
        } catch (Exception e) {
            // 处理失败，抛出异常触发事务回滚
            log.error("处理文件内容失败,请重试", e);
            throw new ServiceException("处理文件内容失败,请重试：" + e.getMessage());
        }

        return true;
    }

    /**
     * 使用指定用户信息调用向量存储接口
     *
     * @param knowledgeId 知识库ID
     * @param fileId      文件ID
     * @param sysOssVo    文件内容对象
     * @param userPhone   用户手机号
     * @param userType    用户类型
     * @return 成功返回"success"，失败返回null
     */
    private String vectorStoreWithUserInfo(String knowledgeType,Long knowledgeId, Long fileId, SysOssVo sysOssVo,
                                          String userPhone, String userType) {
        log.info("使用指定用户信息调用向量存储接口：knowledgeId={}, fileId={}, userPhone={}, userType={}",
                knowledgeId, fileId, userPhone, userType);

        try {
            // 获取文件片段列表
            List<String> fileParagraphs = sysOssVo.getFileParagraphs();
            if (fileParagraphs == null || fileParagraphs.isEmpty()) {
                log.warn("文件片段列表为空，没有分段，跳过向量存储操作");
                // 如果没有分段，则不调用向量新增接口，直接返回null
                return null;
            }
            // 删除这个片段
            fileParagraphs.removeIf(paragraph -> isOnlyContainsBlankChars(paragraph, blankChars));
            // 记录是否所有片段都成功处理
            final boolean[] allSuccess = {true};

            // 使用CountDownLatch等待所有任务完成
            CountDownLatch latch = new CountDownLatch(fileParagraphs.size());
            // 记录多线程任务开始时间
            long startTime = System.currentTimeMillis();
            // 循环处理每个文件片段，使用线程池并行执行
            for (int i = 0; i < fileParagraphs.size(); i++) {
                final String paragraph = fileParagraphs.get(i);
                final int indexNumber = i + 1;

                if (StringUtil.isNotEmpty(paragraph)) {
                    // 提交任务到线程池
                    threadPoolTaskExecutor.execute(() -> {
                        try {
                            // 调用向量存储接口
                            String result = VectorServiceUtils.saveVectorData(
                                userPhone,
                                userType,
                                String.valueOf(knowledgeId),
                                knowledgeType,
                                String.valueOf(fileId),
                                paragraph, // 文本片段内容
                                indexNumber, // 索引编号，从1开始
                                String.valueOf(paragraph.length()), // 当前片段长度
                                null
                            );

                            // 检查结果
                            if ("success".equals(result)) {
                                log.info("文件片段[{}]向量存储成功", indexNumber);
                            } else {
                                log.error("文件片段[{}]向量存储失败", indexNumber);
                                // 使用同步块更新共享变量
                                synchronized (allSuccess) {
                                    allSuccess[0] = false;
                                }
                            }
                        } catch (Exception e) {
                            log.error("文件片段[{}]向量存储异常: {}", indexNumber, e.getMessage(), e);
                            // 使用同步块更新共享变量
                            synchronized (allSuccess) {
                                allSuccess[0] = false;
                            }
                        } finally {
                            // 无论成功失败，都减少计数
                            latch.countDown();
                        }
                    });
                } else {
                    log.warn("文件片段[{}]内容为空，跳过处理", indexNumber);
                    // 空内容直接减少计数
                    latch.countDown();
                }
            }

            // 等待所有任务完成
            try {
                log.info("等待所有向量存储任务完成...");
                latch.await();
                log.info("所有向量存储任务已完成，处理结果: {}", allSuccess[0] ? "全部成功" : "部分失败");
            } catch (InterruptedException e) {
                log.error("等待向量存储任务完成时被中断", e);
                Thread.currentThread().interrupt();
                return null;
            }

            // 记录多线程任务结束时间
            long endTime = System.currentTimeMillis();
            // 计算多线程部分处理耗时
            long elapsedTime = endTime - startTime;
            log.info("向量传输处理耗时: {} 毫秒", elapsedTime);

            // 统计成功和失败的片段数量
            int totalParagraphs = fileParagraphs.size();
            int successCount = 0;
            int failCount = 0;

            if (allSuccess[0]) {
                successCount = totalParagraphs;
            } else {
                // 如果有失败，需要计算具体数量
                // 由于我们没有具体记录每个片段的成功状态，这里只能粗略估计
                // 实际项目中可以考虑使用计数器或状态列表来精确统计
                failCount = totalParagraphs - successCount;
            }

            log.info("向量存储统计 - 总片段数: {}, 成功数: {}, 失败数: {}",
                    totalParagraphs, successCount, failCount);

            // 如果有任何片段处理失败，则删除所有已上传的向量数据
            if (!allSuccess[0]) {
                log.warn("部分文件片段向量存储失败，开始清理已上传的向量数据：knowledgeId={}, fileId={}", knowledgeId, fileId);
                try {
                    // 调用向量删除接口
                    boolean deleteResult = VectorServiceUtils.deleteVectorData(
                        userPhone,
                        userType,
                        String.valueOf(knowledgeId),
                        knowledgeType,
                        String.valueOf(fileId)
                    );
                    if (deleteResult) {
                        log.info("成功清理向量数据：knowledgeId={}, fileId={}", knowledgeId, fileId);
                    } else {
                        log.error("清理向量数据失败：knowledgeId={}, fileId={}", knowledgeId, fileId);
                    }
                } catch (Exception e) {
                    log.error("清理向量数据异常：knowledgeId={}, fileId={}, error={}", knowledgeId, fileId, e.getMessage(), e);
                }
            }

            // 如果所有片段都成功处理，返回success
            return allSuccess[0] ? "success" : null;
        } catch (Exception e) {
            log.error("网络波动文件传输失败，请重试", e);
            throw new ServiceException("网络波动文件传输失败，请重试：" + e.getMessage());
        }
    }

    /**
     * 检查字符串是否只包含指定的空白字符
     *
     * @param str 待检查的字符串
     * @param blankChars 被视为空白的字符数组
     * @return 如果字符串只包含指定的空白字符则返回true，否则返回false
     */
    private boolean isOnlyContainsBlankChars(String str, char[] blankChars) {
        if (str == null || str.isEmpty()) {
            return true;
        }

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            boolean isBlank = false;

            for (char blankChar : blankChars) {
                if (c == blankChar) {
                    isBlank = true;
                    break;
                }
            }

            if (!isBlank) {
                return false;
            }
        }

        return true;
    }

    /**
     * 上传结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @param fileDescription 文件描述
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadStructuredFile(Long knowledgeId, Long ossId, String fileDescription) {
        // 创建文件记录
        KnowledgeFileBo fileBo = createFileRecord(knowledgeId, ossId, fileDescription);
        String userName = LoginHelper.getUsername();

        try{
            // 使用Excel转换，将文件内容格式化输出，遍历形成json串，最后生成完整jsonArray
            log.info("开始处理结构化文件：knowledgeId={}, ossId={}, fileName={}", knowledgeId, ossId, fileBo.getFileName());
            //1 解析Excel文件，获取文件内容
            Map<String, List<Map<String,String>>> content = dataSaveService.getExcelRow(ossId,knowledgeId);
            if(null == content){
                throw new ServiceException("文档转换失败，请确保文档结构化");
            }

            //2 对Excel文档进行分片
            List<String> contentList = dataSaveService.fragmentation(content,ossId);
            if(contentList.isEmpty()){
                throw new ServiceException("文档转换失败，请确保文档结构化并重试");
            }
            //文件总字符长度 取contentList的总字符长度
            long totalFileLength = contentList.stream().mapToLong(String::length).sum();

            // 使用原子计数器记录成功数量
            AtomicInteger successCount = new AtomicInteger(0);
            // 使用CountDownLatch等待所有任务完成
            CountDownLatch latch = new CountDownLatch(contentList.size());

            //3 存储结构化向量数据，使用线程池并行处理
            for (int i = 0; i < contentList.size(); i++) {
                final int index = i;
                final String content_i = contentList.get(i);

                threadPoolTaskExecutor.execute(() -> {
                    try {
                        JSONArray jsonArray = JSON.parseArray(content_i);
                        String resultStr = VectorServiceUtils.saveVectorData(
                            userName,
                            "defaultUser",
                            String.valueOf(knowledgeId),
                            "structured",
                            String.valueOf(fileBo.getFileId()),
                            null,
                            index + 1,
                            String.valueOf(totalFileLength),
                            jsonArray
                        );

                        if(ObjectUtil.equals(resultStr, "success")){
                            successCount.incrementAndGet();
                            log.info("结构化文件片段[{}]向量存储成功", index + 1);
                        } else {
                            log.error("结构化文件片段[{}]向量存储失败", index + 1);
                        }
                    } catch (Exception e) {
                        log.error("结构化文件片段[{}]向量存储异常: {}", index + 1, e.getMessage(), e);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有任务完成
            try {
                log.info("等待所有结构化向量存储任务完成...");
                latch.await();
                log.info("所有结构化向量存储任务已完成，成功数量: {}/{}", successCount.get(), contentList.size());
            } catch (InterruptedException e) {
                log.error("等待结构化向量存储任务完成时被中断", e);
                Thread.currentThread().interrupt();
                throw new ServiceException("处理结构化文件被中断");
            }

            // 检查是否全部成功
            if(successCount.get() != contentList.size()){
                //向量失败，走删除接口
                VectorServiceUtils.deleteVectorData(userName,"defaultUser",String.valueOf(knowledgeId),
                    "structured",String.valueOf(fileBo.getFileId()));
                // 抛出异常触发事务回滚
                log.error("结构化文件向量存储失败：knowledgeId={}, ossId={}, fileName={}", knowledgeId, ossId, fileBo.getFileName());
                throw new ServiceException("文件上传失败");
            }else {
                // 更新文件状态 成功
                updateFileStatus(fileBo.getFileId(), "1", null);
                log.info("结束处理结构化文件：knowledgeId={}, ossId={}, fileName={}", knowledgeId, ossId, fileBo.getFileName());
            }
        } catch (ServiceException e) {
            // 业务异常直接抛出，触发事务回滚
            throw e;
        } catch (Exception e){
            log.error("结束处理结构化文件失败：knowledgeId={}, ossId={}, fileName={} ,message={}", knowledgeId, ossId, fileBo.getFileName(),e.getMessage());
            // 抛出异常触发事务回滚
            throw new ServiceException("处理结构化文件失败：" + e.getMessage());
        }
        return true;
    }



    /**
     * 上传结构化文件到知识库
     *
     * @param knowledgeId 知识库ID
     * @param ossId       OSS文件ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadStructuredFile(Long knowledgeId, Long ossId) {
        // 调用带文件描述的方法，描述为空
        return uploadStructuredFile(knowledgeId, ossId, null);
    }

    /**
     * 创建文件记录
     *
     * @param knowledgeId 知识库ID
     * @param ossId OSS文件ID
     * @param fileDescription 文件描述
     * @return 文件业务对象
     */
    private KnowledgeFileBo createFileRecord(Long knowledgeId, Long ossId, String fileDescription) {
        // 获取知识库信息
        KnowledgeBaseVo knowledgeBaseVo = knowledgeBaseService.queryById(knowledgeId);
        // 检查知识库文件数量是否已达到上限
        int fileCount = this.countByKnowledgeId(knowledgeId);
        // 判断是不是个人知识库
        if (knowledgeBaseVo.getDeptId() == null){
            if (fileCount >= knowledgeConfig.getMaxPersonalFileCount()) {
                log.error("知识库文件数量已达到上限：knowledgeId={}, currentCount={}, maxCount={}", knowledgeId, fileCount, knowledgeConfig.getMaxPersonalFileCount());
                throw new ServiceException("知识库文件数量已达到上限");
            }
        }else {
            if (fileCount >= knowledgeConfig.getMaxFileCount()) {
                log.error("知识库文件数量已达到上限：knowledgeId={}, currentCount={}, maxCount={}", knowledgeId, fileCount, knowledgeConfig.getMaxFileCount());
                throw new ServiceException("知识库文件数量已达到上限");
            }
        }

        // 获取文件内容
        SysOssVo ossVo = sysOssService.getById(ossId);
        if (ossVo == null) {
            log.error("获取文件内容失败：ossId={}", ossId);
            throw new ServiceException("获取文件内容失败");
        }
        try {
            // 创建知识库文件记录
            KnowledgeFileBo fileBo = new KnowledgeFileBo();
            fileBo.setKnowledgeId(knowledgeId);
            fileBo.setOssId(ossId);
            fileBo.setFileName(ossVo.getOriginalName());
            fileBo.setFileType(ossVo.getFileSuffix());
            fileBo.setFileSize(0L);
            fileBo.setStatus("0"); // 处理中
            fileBo.setRemark(JSON.parseObject(fileDescription, Map.class).get("fileDescription").toString()); // 设置文件描述
            this.insertByBo(fileBo);

            return fileBo;
        } catch (Exception e) {
            log.error("创建文件记录失败", e);
            throw new ServiceException("创建文件记录失败");
        }
    }

    /**
     * 更新文件状态
     *
     * @param fileId 文件ID
     * @param status 状态
     * @param vectorId 向量ID
     */
    private void updateFileStatus(Long fileId, String status, String vectorId) {
        KnowledgeFileBo updateBo = new KnowledgeFileBo();
        updateBo.setFileId(fileId);
        updateBo.setStatus(status);
        if (StringUtils.isNotEmpty(vectorId)) {
            updateBo.setVectorId(vectorId);
        }
        this.updateByBo(updateBo);
    }


    /**
     * 处理多个文件
     *
     * @param fileIdList 文件ID列表
     * @return 文件内容Map
     */
//    private Map<String, String> processMultipleFiles(List<Long> fileIdList) throws InterruptedException {
//        Map<String, String> files = new ConcurrentHashMap<>();
//        CountDownLatch countDownLatch = new CountDownLatch(fileIdList.size());
//        AtomicInteger atomicInteger = new AtomicInteger(0);
//
//        for (Long id : fileIdList) {
//            CompletableFuture.runAsync(() -> {
//                SysOssVo content = dataSaveService.getFileContent(id);
//                if (content != null && content.getFileContent() != null) {
//                    files.put("文件" + (atomicInteger.get() + 1), content.getFileContent());
//                }
//                atomicInteger.addAndGet(1);
//                countDownLatch.countDown();
//            });
//        }
//
//        countDownLatch.await();
//        return files;
//    }

    /**
     * 根据知识库ID查询文件列表
     *
     * @param knowledgeId 知识库ID
     * @return 知识库文件集合
     */
    @Override
    public List<KnowledgeFileVo> queryListByKnowledgeId(Long knowledgeId) {
        KnowledgeFileBo bo = new KnowledgeFileBo();
        bo.setKnowledgeId(knowledgeId);
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    /**
     * 获取知识库文件数量
     *
     * @param knowledgeId 知识库ID
     * @return 文件数量
     */
    @Override
    public int countByKnowledgeId(Long knowledgeId) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeFile> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(KnowledgeFile::getKnowledgeId, knowledgeId);
        return Math.toIntExact(baseMapper.selectCount(lqw));
    }

    @Override
    public KnowledgeFile selectById(Long knowledgeFileId) {
        return baseMapper.selectById(knowledgeFileId);
    }


    /**
     * 构建查询条件
     *
     * @param bo 知识库文件信息
     * @return 查询条件
     */
    private com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeFile> buildQueryWrapper(KnowledgeFileBo bo) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeFile> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(bo.getKnowledgeId() != null, KnowledgeFile::getKnowledgeId, bo.getKnowledgeId());
        lqw.eq(bo.getOssId() != null, KnowledgeFile::getOssId, bo.getOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), KnowledgeFile::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), KnowledgeFile::getFileName, bo.getFileName());
        return lqw;
    }

}
