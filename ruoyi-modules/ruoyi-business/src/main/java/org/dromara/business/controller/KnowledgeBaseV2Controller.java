package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.business.domain.bo.KbDatabaseConfigBo;
import org.dromara.business.service.IKnowledgeBaseV2Service;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendReqVO;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 知识库V2 数据库直连控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/knowledge/v2")
public class KnowledgeBaseV2Controller extends BaseController {

    private final IKnowledgeBaseV2Service knowledgeBaseV2Service;


    @Log(title = "进阶知识库-数据库直连模式-测试连接", businessType = BusinessType.OTHER)
    @PostMapping("/testConnection")
    public R<String> testConnection(@RequestBody @Validated(AddGroup.class) KbDatabaseConfigBo bo) {
        try {
            knowledgeBaseV2Service.testConnection(bo);
            return R.ok("测试连接成功");
        }catch (Exception e){
            return R.fail("测试连接失败:"+ e.getMessage());
        }
    }


    @Log(title = "进阶知识库-数据库直连模式-保存连接", businessType = BusinessType.INSERT)
    @PostMapping("/saveConnection")
    public R<String> saveConnection(@RequestBody @Validated(AddGroup.class) KbDatabaseConfigBo bo) {
        try {
            knowledgeBaseV2Service.saveConnection(bo);
            return R.ok("保存成功");
        }catch (Exception e){
            return R.fail("保存失败:"+ e.getMessage());
        }
    }

    @Log(title = "进阶知识库-数据库直连模式-刷新连接", businessType = BusinessType.INSERT)
    @PostMapping("/refreshConnection")
    public R<String> refreshConnection(@RequestBody KbDatabaseConfigBo bo) {
        try {
            if (bo.getDatabaseConfigId() == null) {
                return R.fail("刷新失败:数据库配置id不能为空");
            }
            knowledgeBaseV2Service.refreshConnection(bo);
            return R.ok("刷新成功");
        }catch (Exception e){
            return R.fail("刷新失败:"+ e.getMessage());
        }
    }



}
