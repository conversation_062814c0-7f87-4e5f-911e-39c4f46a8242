package org.dromara.business.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.constant.ServiceApiConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Configuration
@Service
@Slf4j
public class StatisticsServiceImpl {

    private String HOST = "http://*************:8002";

    @Cacheable(cacheNames = CacheNames.COM_INFO)
    public String loadData() {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            String url = HOST + ServiceApiConstant.getAreaHuaCount;
            Map<String, Object> param = new HashMap<>();
            String resString = HttpUtil.get(url, param);
            JSONObject resJSON = JSONObject.parseObject(resString);
            log.info("loadData：{}", resJSON);
            if (resJSON.getInteger("code") == 200) {
                JSONArray data = JSONObject.parseArray(resJSON.getString("data"));
                if (data != null && !data.isEmpty()) {
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        String com_area = jsonObject.getString("com_area");
                        String hua_count = jsonObject.getString("hua_count");
                        stringBuffer.append(com_area).append(":").append(hua_count).append(";");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return stringBuffer.toString();
        }
        return stringBuffer.toString();
    }

    @Cacheable(cacheNames = CacheNames.COM_INFO)
    public String loadDataSum() {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            int count = 0;
            String url = HOST + ServiceApiConstant.getAreaHuaCount;
            Map<String, Object> param = new HashMap<>();
            String resString = HttpUtil.get(url, param);
            JSONObject resJSON = JSONObject.parseObject(resString);
            log.info("loadDataSum：{}", resJSON);
            if (resJSON.getInteger("code") == 200) {
                JSONArray data = JSONObject.parseArray(resJSON.getString("data"));
                if (data != null && !data.isEmpty()) {
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        count = count + Integer.parseInt(jsonObject.getString("hua_count").replace("家", ""));
                    }
                }
            }
            stringBuffer.append("截至到目前为止,沂蒙慧眼已为临沂市各县区画像：").append(count).append("份");
        } catch (Exception e) {
            log.error(e.getMessage());
            return stringBuffer.toString();
        }
        return stringBuffer.toString();
    }

    @Cacheable(cacheNames = CacheNames.COM_COUNT)
    public String loadCompanyCount() {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            String url = HOST + ServiceApiConstant.getComCount;
            Map<String, Object> param = new HashMap<>();
            String resString = HttpUtil.get(url, param);
            JSONObject resJSON = JSONObject.parseObject(resString);
            log.info("loadCompanyCount：{}", resJSON);
            if (resJSON.getInteger("code") == 200) {
                JSONArray data = JSONObject.parseArray(resJSON.getString("data"));
                if (data != null && !data.isEmpty()) {
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        String com_area = jsonObject.getString("com_area");
                        String hua_count = jsonObject.getString("comCount");
                        stringBuffer.append(com_area).append(":").append(hua_count).append(";");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return stringBuffer.toString();
        }
        return stringBuffer.toString();
    }

    @Cacheable(cacheNames = CacheNames.COM_COUNT)
    public String loadCompanyCountSum() {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            int count = 0;
            String url = HOST + ServiceApiConstant.getComCount;
            Map<String, Object> param = new HashMap<>();
            String resString = HttpUtil.get(url, param);
            JSONObject resJSON = JSONObject.parseObject(resString);
            log.info("loadCompanyCountSum：{}", resJSON);
            if (resJSON.getInteger("code") == 200) {
                JSONArray data = JSONObject.parseArray(resJSON.getString("data"));
                if (data != null && !data.isEmpty()) {
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        count = count + Integer.parseInt(jsonObject.getString("comCount"));
                    }
                }
            }
            stringBuffer.append("截至到目前为止,沂蒙慧眼已收集企业：").append(count).append("家");
        } catch (Exception e) {
            log.error(e.getMessage());
            return stringBuffer.toString();
        }
        return stringBuffer.toString();
    }

    /**
     * 查询评分前十的企业
     */
    @Cacheable(cacheNames = CacheNames.COM_RANK)
    public String loadCompanyRank() {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            String url = HOST + ServiceApiConstant.getComRank;
            Map<String, Object> param = new HashMap<>();
            String resString = HttpUtil.get(url, param);
            JSONObject resJSON = JSONObject.parseObject(resString);
            log.info("loadCompanyRank：{}", resJSON);
            if (resJSON.getInteger("code") == 200) {
                stringBuffer.append("截至到目前为止统计信息如下：");
                JSONArray data = JSONObject.parseArray(resJSON.getString("data"));
                if (data != null && !data.isEmpty()) {
                    for (int i = 0; i < data.size(); i++) {
                        stringBuffer.append(data.getJSONObject(i).getString("comRank")).append(":").append(data.getJSONObject(i).getString("rankCount")).append(";");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return stringBuffer.toString();
        }
        return stringBuffer.toString();
    }
}
