package org.dromara.business.service;

import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.bo.AiModelHistoryMsgBo;
import org.dromara.business.domain.vo.AiModelHistoryMsgVo;
import org.dromara.business.domain.vo.AiModelVo;
import org.dromara.business.domain.bo.AiModelBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 大模型配置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface IAiModelService {

    /**
     * 查询大模型配置
     *
     * @param id 主键
     * @return 大模型配置
     */
    AiModelVo queryById(Long id);

    /**
     * 分页查询大模型配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 大模型配置分页列表
     */
    TableDataInfo<AiModelVo> queryPageList(AiModelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的大模型配置列表
     *
     * @param bo 查询条件
     * @return 大模型配置列表
     */
    List<AiModelVo> queryList(AiModelBo bo);

    /**
     * 新增大模型配置
     *
     * @param bo 大模型配置
     * @return 是否新增成功
     */
    Boolean insertByBo(AiModelBo bo);

    /**
     * 修改大模型配置
     *
     * @param bo 大模型配置
     * @return 是否修改成功
     */
    Boolean updateByBo(AiModelBo bo);

    /**
     * 校验并批量删除大模型配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void changeModel(AiModelBo bo);

    Map<String,Object> getModelInfo();

    Map<String, Object> getTokensCount();

    void resetChannel();

    AiModel getModelInfoInChat(Long modelId);

    List<AiModel> getIdentifyModel();

    List<AiModel> getCoderModel();

    Map<String,Object> getModelHistoryInfo(AiModelHistoryMsgBo aiModelHistoryMsgBo);

}
