package org.dromara.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.KbDatabaseConfig;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.domain.KnowledgeFile;
import org.dromara.business.domain.KnowledgeUserRel;
import org.dromara.business.domain.KnowledgeCategoryRelation;
import org.dromara.business.domain.bo.KnowledgeBaseBo;
import org.dromara.business.domain.bo.KnowledgeUserRelBo;
import org.dromara.business.domain.dto.KnowledgeBaseMyListDTO;
import org.dromara.business.domain.vo.KnowledgeBaseListVo;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.domain.vo.KnowledgeFileVo;
import org.dromara.business.domain.vo.KnowledgeUserRelVo;
import org.dromara.business.dto.KnowledgeCategoryRelationDTO;
import org.dromara.business.mapper.KbDatabaseConfigMapper;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.business.mapper.KnowledgeUserRelMapper;
import org.dromara.business.mapper.KnowledgeCategoryRelationMapper;
import org.dromara.business.service.IKnowledgeBaseService;
import org.dromara.business.service.IKnowledgeCategoryRelationService;
import org.dromara.business.service.IKnowledgeFileService;
import org.dromara.business.service.IKnowledgeUserRelService;
import org.dromara.business.utils.PageUtils;
import org.dromara.business.utils.SemanticSqlUtils;
import org.dromara.business.utils.VectorServiceUtils;
import org.dromara.business.utils.baseEntity.PageResult;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.KnowledgeBaseSourceTypeEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.UserService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 知识库服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeBaseServiceImpl implements IKnowledgeBaseService {

    private final KnowledgeBaseMapper baseMapper;
    private final IKnowledgeFileService knowledgeFileService;
    private final IKnowledgeUserRelService knowledgeUserRelService;
    private final DataSaveServiceImpl dataSaveService;
    private final ISysDeptService deptService;
    private final SemanticSqlUtils semanticSqlUtils;
    private final KbDatabaseConfigMapper kbDatabaseConfigMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final SysUserMapper sysUserMapper;


    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private KnowledgeUserRelMapper knowledgeUserRelMapper;
    @Autowired
    private UserService userService;

    @Resource
    private KnowledgeCategoryRelationMapper knowledgeCategoryRelationMapper;

    @Resource
    private IKnowledgeCategoryRelationService knowledgeCategoryRelationService;

    /**
     * 查询知识库
     *
     * @param knowledgeId 知识库主键
     * @return 知识库
     */
    @Override
    public KnowledgeBaseVo queryById(Long knowledgeId) {
        KnowledgeBaseVo vo = baseMapper.selectVoById(knowledgeId);
        if (vo != null) {
            vo.setFileCount(knowledgeFileService.countByKnowledgeId(knowledgeId));
        }
        return vo;
    }

    /**
     * 查询知识库列表
     *
     * @param bo                  知识库
     * @param knowledgeBaseListVo
     * @return 知识库
     */
    @Override
    public TableDataInfo<KnowledgeBaseVo> queryPageList(KnowledgeBaseBo bo, PageQuery pageQuery, KnowledgeBaseListVo knowledgeBaseListVo) {
        //部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        deptIds.add(bo.getDeptId());

        // 构建知识库查询条件
        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnowledgeBase::getDeptId, deptIds)
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName, bo.getKnowledgeName());
        if (!deptIds.isEmpty()){
            queryWrapper.isNotNull(KnowledgeBase::getDeptId);
        }
        if (StringUtils.isNotEmpty(knowledgeBaseListVo.getBuildType())){
            if (knowledgeBaseListVo.getBuildType().equals("institute")){
                queryWrapper.eq(KnowledgeBase::getDeptCategory, knowledgeBaseListVo.getBuildType());
            }else if (knowledgeBaseListVo.getBuildType().equals("department")){
                queryWrapper.nested(w ->
                    w.isNull(KnowledgeBase::getDeptCategory)
                        .or().eq(KnowledgeBase::getDeptCategory, "")
                        .or().ne(KnowledgeBase::getDeptCategory, "institute")
                );
            }
        }
        // 动态排序逻辑
        if (StringUtils.isNotBlank(knowledgeBaseListVo.getSortType())) {
            boolean asc = "asc".equalsIgnoreCase(knowledgeBaseListVo.getSortType());
            // 先按 sortOrder 排序，再按 createTime 降序
            queryWrapper
                .orderBy(true, asc, KnowledgeBase::getSortOrder)
                .orderByDesc(KnowledgeBase::getCreateTime);
        } else {
            // 默认：只按 createTime 降序
            queryWrapper.orderByDesc(KnowledgeBase::getCreateTime);
        }
        if (knowledgeBaseListVo.getCategoryId() != null){
            List<Long> knowledgeIds = selectKnowledgeIdByCategory(knowledgeBaseListVo.getCategoryId());
            // 如果有符合条件的知识库ID，则添加到查询条件中
            if (!knowledgeIds.isEmpty()) {
                queryWrapper.in(KnowledgeBase::getKnowledgeId, knowledgeIds);
            }else {
                queryWrapper.eq(KnowledgeBase::getKnowledgeId, null);
            }
        }
        Page<KnowledgeBaseVo> knowledgeBaseVoIPage = baseMapper.selectVoPage(pageQuery.build(), queryWrapper);
//        List<KnowledgeBaseVo> distinctLis2t = knowledgeBaseVoIPage.getRecords();

        // 设置知识库来源类型和部门名称
        fillKnowledgeBaseSourceType(knowledgeBaseVoIPage.getRecords());
        // 根据部门名称模糊筛 知识库列表
        if(StringUtils.isNotEmpty(knowledgeBaseListVo.getDeptName())){
            fuzzyFilterDeptName(knowledgeBaseVoIPage.getRecords(),knowledgeBaseListVo.getDeptName());
        }
        return TableDataInfo.build(knowledgeBaseVoIPage);
    }

    /**
     * 根据部门名称模糊筛 知识库列表
     * @param distinctList
     * @param deptName
     */
    private void fuzzyFilterDeptName(List<KnowledgeBaseVo> distinctList, String deptName) {
        distinctList.removeIf(vo ->
            vo.getSourceType() != null && !vo.getSourceType().contains(deptName)
        );
    }

    private List<Long> selectKnowledgeIdByCategory(Long categoryId) {
        List<Long> knowledgeIds = new ArrayList<>();
        // 如果有分类ID，则联表查询
        if (categoryId != null) {
            // 先查询该分类下的所有知识库ID
            knowledgeIds = knowledgeCategoryRelationMapper.selectList(
                new LambdaQueryWrapper<KnowledgeCategoryRelation>()
                    .eq(KnowledgeCategoryRelation::getCategoryId, categoryId)
                    .select(KnowledgeCategoryRelation::getKnowledgeId)
            ).stream().map(KnowledgeCategoryRelation::getKnowledgeId).collect(Collectors.toList());
        }
        // 返回空数组
        return knowledgeIds;
    }

    /**
     * 设置知识库来源类型和部门名称
     *
     * @param knowledgeBaseVoList 知识库VO列表
     */
    @Override
    public void fillKnowledgeBaseSourceType(List<KnowledgeBaseVo> knowledgeBaseVoList) {
        if (knowledgeBaseVoList == null || knowledgeBaseVoList.isEmpty()) {
            return;
        }

        List<Long> knowledgeIds = knowledgeBaseVoList.stream()
            .map(KnowledgeBaseVo::getKnowledgeId)
            .collect(Collectors.toList());

        // 查询知识库用户关系表的sourceType
        if (!knowledgeIds.isEmpty()) {
            // 批量查询知识库用户关系表和部门信息，避免循环查询提高性能
            List<Map<String, Object>> knowledgeRelList = knowledgeUserRelMapper.selectSourceTypeAndDeptByKnowledgeIds(knowledgeIds);

            Map<Long, String> knowledgeSourceTypeMap = new HashMap<>();
            Map<Long, Long> knowledgeDeptIdMap = new HashMap<>();

            // 构建映射关系
            for (Map<String, Object> map : knowledgeRelList) {
                if (map.get("knowledge_id") != null) {
                    Long knowledgeId = ((Number) map.get("knowledge_id")).longValue();
                    String sourceType = (String) map.get("source_type");
                    Long deptId = map.get("dept_id") != null ? ((Number) map.get("dept_id")).longValue() : null;

                    if (sourceType != null) {
                        knowledgeSourceTypeMap.put(knowledgeId, sourceType);
                    }

                    if (deptId != null) {
                        knowledgeDeptIdMap.put(knowledgeId, deptId);
                    }
                }
            }

            // 将sourceType设置到对应的知识库VO对象中
            // 收集需要查询部门名称的知识库ID和部门ID
            Map<Long, Long> deptIdMap = new HashMap<>();
            knowledgeBaseVoList.forEach(vo -> {
                String sourceType = knowledgeSourceTypeMap.get(vo.getKnowledgeId());
                if (StringUtils.isNotEmpty(sourceType)) {
                    // 如果是部门或院级类型，收集部门ID
                    if (KnowledgeBaseSourceTypeEnum.DEPARTMENT.getCode().equals(sourceType) ||
                        KnowledgeBaseSourceTypeEnum.INSTITUTE.getCode().equals(sourceType)) {
                        Long deptId = knowledgeDeptIdMap.get(vo.getKnowledgeId());
                        if (deptId != null) {
                            deptIdMap.put(vo.getKnowledgeId(), deptId);
                        } else {
                            vo.setSourceType(KnowledgeBaseSourceTypeEnum.getByCode(sourceType).getInfo());
                        }
                    } else {
                        // 对于其他类型，使用枚举值的info
                        vo.setSourceType(KnowledgeBaseSourceTypeEnum.getByCode(sourceType).getInfo());
                    }
                }
            });

            // 批量查询部门信息
            if (!deptIdMap.isEmpty()) {
                List<Long> queryDeptIds = new ArrayList<>(deptIdMap.values());
                // queryDeptIds去重
                queryDeptIds = queryDeptIds.stream().distinct().collect(Collectors.toList());
                List<SysDeptVo> deptVos = sysDeptMapper.selectVoByIds(queryDeptIds);

                // 构建部门ID到部门名称的映射
                Map<Long, String> deptNameMap = deptVos.stream()
                    .collect(Collectors.toMap(SysDeptVo::getDeptId, SysDeptVo::getDeptName));

                // 设置部门名称
                deptIdMap.forEach((knowledgeId, deptId) -> {
                    String deptName = deptNameMap.get(deptId);
                    if (StringUtils.isNotEmpty(deptName)) {
                        knowledgeBaseVoList.stream()
                            .filter(vo -> vo.getKnowledgeId().equals(knowledgeId))
                            .findFirst()
                            .ifPresent(vo -> vo.setSourceType(deptName));
                    } else {
                        // 如果未找到部门名称，则使用枚举值的info
                        String sourceType = knowledgeSourceTypeMap.get(knowledgeId);
                        knowledgeBaseVoList.stream()
                            .filter(vo -> vo.getKnowledgeId().equals(knowledgeId))
                            .findFirst()
                            .ifPresent(vo -> vo.setSourceType(KnowledgeBaseSourceTypeEnum.getByCode(sourceType).getInfo()));
                    }
                });
            }
        }
    }

    /**
     * 查询知识库列表
     *
     * @param bo 知识库
     * @return 知识库
     */
    @Override
    public List<KnowledgeBaseVo> queryList(KnowledgeBaseBo bo) {
        // 部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        deptIds.add(bo.getDeptId());

        List<KnowledgeBase> list = baseMapper.selectList(new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getDeptId, deptIds)
            .or()
            .eq(bo.getCreateUserId() != null, KnowledgeBase::getCreateUserId, bo.getCreateUserId())
            .orderByDesc(KnowledgeBase::getCreateTime));
//        for (KnowledgeBaseVo vo : list) {
//            vo.setFileCount(knowledgeFileService.countByKnowledgeId(vo.getKnowledgeId()));
//        }
        return MapstructUtils.convert(list, KnowledgeBaseVo.class);
    }

    /**
     * 查询部门知识库列表根据用户
     *
     * @param bo 知识库
     * @param deptId 部门ID
     * @param sortType 排序方式
     * @return 知识库
     */
    @Override
    public IPage<KnowledgeBaseVo> queryListDepartment(KnowledgeBaseBo bo, String deptId, String sortType,KnowledgeBaseMyListDTO dto) {
        List<Long> deptIds = new ArrayList<>();
        if (deptId == null || deptId.isEmpty()) {
            SysUser sysUser = sysUserMapper.selectById(LoginHelper.getUserId());
            if(null == sysUser){
                throw new ServiceException("用户信息不存在");
            }
            // 部门id查询所有字部门
            List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
            deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
            deptIds.add(bo.getDeptId());
            //如果没有选中部门，则同时查询当前登录用户的关联部门的知识库信息
            if(StringUtils.isNotBlank(sysUser.getOtherDept())){
                List<String> deptIdsList = Stream.of(sysUser.getOtherDept().split(",")).toList();
                for (String dept : deptIdsList) {
                    //根据部门id查询所有子部门id
                    List<SysDeptVo> sysDeptVoChild = deptService.selectDeptListByUserId(Long.valueOf(dept));
                    List<Long> deptIdsChild = sysDeptVoChild.stream().map(SysDeptVo::getDeptId).toList();
                    deptIds.addAll(deptIdsChild);
                }
            }
        }else {
            List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(Long.valueOf(deptId));
            deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
            deptIds.add(Long.valueOf(deptId));
        }

        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getDeptId, deptIds)
            .isNotNull(KnowledgeBase::getDeptId)
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName, bo.getKnowledgeName());

        queryWrapper.nested(w ->
            w.isNull(KnowledgeBase::getDeptCategory)
                .or().eq(KnowledgeBase::getDeptCategory, "")
                .or().ne(KnowledgeBase::getDeptCategory, "institute")
        );
        // 根据分类查询
        if (bo.getCategoryId() != null){
            List<Long> knowledgeIds = this.selectKnowledgeIdByCategory(bo.getCategoryId());
            if (CollectionUtil.isNotEmpty(knowledgeIds)){
                queryWrapper.in(KnowledgeBase::getKnowledgeId, knowledgeIds);
            }else {
                queryWrapper.eq(KnowledgeBase::getKnowledgeId, null);
            }
        }
        // 根据排序字段进行排序
        if (StringUtils.isNotEmpty(sortType)) {
            if ("asc".equals(sortType)) {
                queryWrapper.orderByAsc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            } else if ("desc".equals(sortType)) {
                queryWrapper.orderByDesc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            }
        } else {
            // 默认按创建时间排序
            queryWrapper.orderByDesc(KnowledgeBase::getCreateTime);
        }

        return baseMapper.selectVoPage(dto.getPageQuery().build(), queryWrapper);
    }

    /**
     * 查询个人知识库列表根据用户
     *
     * @param bo 知识库
     * @param sortType 排序方式
     * @return 知识库
     */
    @Override
    public IPage<KnowledgeBaseVo> queryListUser(KnowledgeBaseBo bo, String sortType,KnowledgeBaseMyListDTO dto) {
        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<KnowledgeBase>()
            .eq(bo.getCreateUserId() != null, KnowledgeBase::getCreateUserId, bo.getCreateUserId())
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName, bo.getKnowledgeName());
        queryWrapper.nested(w ->
            w.isNull(KnowledgeBase::getDeptId)
                .or().eq(KnowledgeBase::getDeptId, 0L)
        );
        // 根据排序字段进行排序
        if (StringUtils.isNotEmpty(sortType)) {
            if ("asc".equals(sortType)) {
                queryWrapper.orderByAsc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            } else if ("desc".equals(sortType)) {
                queryWrapper.orderByDesc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            }
        } else {
            // 默认按创建时间排序
            queryWrapper.orderByDesc(KnowledgeBase::getCreateTime);
        }
        IPage<KnowledgeBaseVo> knowledgeBaseVoIPage = baseMapper.selectVoPage(dto.getPageQuery().build(), queryWrapper);
        return knowledgeBaseVoIPage;
    }

    /**
     * 新增知识库
     *
     * @param bo 知识库
     * @return 结果
     */
    @Override
    public Boolean insertByBo(KnowledgeBaseBo bo) {
        KnowledgeBase add = MapstructUtils.convert(bo, KnowledgeBase.class);
        // 默认状态为正常
        if (StringUtils.isEmpty(add.getStatus())) {
            add.setStatus("0");
        }
        // 默认数据类型为非结构化数据
        if (StringUtils.isEmpty(add.getDataType())) {
            add.setDataType("1");
        }
        boolean result = baseMapper.insert(add) > 0;
        if (result) {
            // 将生成的ID设置回BO对象
            bo.setKnowledgeId(add.getKnowledgeId());
        }
        return result;
    }

    /**
     * 修改知识库
     *
     * @param bo 知识库
     * @return 结果
     */
    @Override
    public Boolean updateByBo(KnowledgeBaseBo bo) {
        KnowledgeBase update = MapstructUtils.convert(bo, KnowledgeBase.class);
        Long deptId = bo.getDeptId();
        Long knowledgeId = bo.getKnowledgeId();
        List<KnowledgeUserRelVo> knowledgeUserRelVos = knowledgeUserRelMapper.selectVoList(new LambdaQueryWrapper<KnowledgeUserRel>().eq(KnowledgeUserRel::getKnowledgeId, knowledgeId));

        // 查询部门信息
        if (deptId != null) {
            // 知识库类型
            String knowledgeType = "";
            SysDeptVo sysDeptVo = deptService.selectDeptById(deptId);
            // 院级
            if (sysDeptVo != null && StringUtils.isNotEmpty(sysDeptVo.getDeptCategory())) {
                if (update != null) {
                    update.setDeptCategory("institute");
                }
                knowledgeUserRelVos.forEach(knowledgeUserRelVo -> {
                    knowledgeUserRelVo.setSourceType(KnowledgeBaseSourceTypeEnum.INSTITUTE.getCode());
                });
            }
            // 部门级
            else {
                if (update != null) {
                    update.setDeptCategory("");
                }
                knowledgeUserRelVos.forEach(knowledgeUserRelVo -> {
                    knowledgeUserRelVo.setSourceType(KnowledgeBaseSourceTypeEnum.DEPARTMENT.getCode());
                });
            }
        }
        List<KnowledgeUserRel> knowledgeUserRelList = new ArrayList<>();
        knowledgeUserRelVos.forEach(knowledgeUserRelVo -> {
            KnowledgeUserRel knowledgeUserRel = new KnowledgeUserRel();
            knowledgeUserRel = MapstructUtils.convert(knowledgeUserRelVo, KnowledgeUserRel.class);
            knowledgeUserRelList.add(knowledgeUserRel);
        });
        knowledgeUserRelMapper.updateBatchById(knowledgeUserRelList);
        knowledgeCategoryRelationService.updateKnowledgeCategory(knowledgeId, bo.getCategoryId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改知识库校验权限，院知识库同级的部门管理员，以及上级的管理员都可以修改
     */
    @Override
    public void checkUpdatePermission(KnowledgeBaseBo bo) {
        Long knowledgeId = bo.getKnowledgeId();
        KnowledgeBase knowledgeBase = baseMapper.selectById(knowledgeId);
        // 只校验院知识库
        if (knowledgeBase.getDeptCategory() != null && !knowledgeBase.getDeptCategory().equals("institute")){
            return;
        }
        // 部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(knowledgeBase.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        if (LoginHelper.getLoginUser() == null || LoginHelper.getLoginUser().getDeptId() == null) {
            throw new ServiceException("登录信息异常");
        }
        Long currentDeptId = LoginHelper.getLoginUser().getDeptId();
        // 判断当前用户是否是是父级部门管理员
        if (deptIds.contains(currentDeptId)) {
            throw new ServiceException("您没有权限修改该知识库");
        }
    }

    /**
     * 批量删除知识库
     *
     * @param knowledgeIds 需要删除的知识库主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return false;
        }

        // 1. 批量查询相关数据，减少数据库查询次数
        Map<Long, List<KnowledgeFileVo>> knowledgeFilesMap = new HashMap<>();
        for (Long id : knowledgeIds) {
            List<KnowledgeFileVo> files = knowledgeFileService.queryListByKnowledgeId(id);
            if (!files.isEmpty()) {
                knowledgeFilesMap.put(id, files);
            }
        }

        // 2. 批量删除用户关联关系
        knowledgeUserRelMapper.delete(
            new LambdaQueryWrapper<KnowledgeUserRel>()
                .in(KnowledgeUserRel::getKnowledgeId, knowledgeIds)
        );

        // 3. 批量删除文件记录
        // 收集所有的文件ID
        List<Long> allFileIds = knowledgeFilesMap.values().stream()
            .flatMap(Collection::stream)
            .map(KnowledgeFileVo::getFileId)
            .toList();

        // 如果有文件需要删除
        if (!allFileIds.isEmpty()) {
            knowledgeFileService.deleteWithValidByIds(allFileIds);
        }

        // 4. 删除知识库
        boolean result = baseMapper.deleteByIds(knowledgeIds) > 0;

        // 5. 日志记录
        log.info("成功删除知识库数量: {}, 关联文件数量: {}", knowledgeIds.size(), allFileIds.size());

        return result;
    }

    /**
     * 获取知识库检索信息
     *
     * @param knowledgeBaseId 知识库id
     * @param userMessage     用户提示词
     * @return
     */
    @Override
    public Map<String, String> getDocumentInfos(String knowledgeBaseId, String userMessage, LoginUser loginUser) throws ServiceException  {
        return getDocumentInfos(knowledgeBaseId, userMessage, loginUser, null);
    }

    /**
     * 获取知识库检索信息（支持检索配置）
     *
     * @param knowledgeBaseId 知识库id
     * @param userMessage     用户提示词
     * @param loginUser       登录用户
     * @param retrieveConfig  检索配置
     * @return 检索结果
     */
    public Map<String, String> getDocumentInfos(String knowledgeBaseId, String userMessage, LoginUser loginUser,
                                               org.dromara.common.core.domain.vo.ChatMessageSendReqVO.RetrieveConfig retrieveConfig) throws ServiceException  {
        Map<String, String> resultMap = new HashMap<>();
        KnowledgeBase knowledgeBase = baseMapper.selectById(knowledgeBaseId);
        if (knowledgeBase == null) {
            log.warn("知识库 {} 不存在", knowledgeBaseId);
            return resultMap;
        }

        try {
            if(ObjectUtil.equals(knowledgeBase.getDataType(), "0") && knowledgeBase.getLearningStyle()==2 ){
                // 进阶知识库 2-数据库直连模式
                resultMap.putAll(structuredDatabase(userMessage,knowledgeBase));
            }else {
                //判断知识库中是否有文件，如果没有文件，则直接返回
                int fileCount = knowledgeFileService.countByKnowledgeId(knowledgeBase.getKnowledgeId());
                if(fileCount == 0){
                    return resultMap;
                }
                //拼接请求参数
                VectorServiceUtils.VectorStoreSearch param = buildVectorStoreSearchParam(knowledgeBase, userMessage, loginUser, retrieveConfig);
                if(null == param){
                    resultMap.put("查询结果", "知识库内容无法查看");
                    return resultMap;
                }

                // 发送 HTTP 请求，进行向量检索
                String result = VectorServiceUtils.searchVectorData(param);
                if (StringUtils.isEmpty(result)) {
                    log.warn("向量检索结果为空，知识库ID: {}", knowledgeBaseId);
                    return resultMap;
                }

                // 根据知识库类型区分返回值取值
                String dataType = knowledgeBase.getDataType();
                if ("0".equals(dataType)) {
                    // 结构化数据即 进阶知识库 1-文件上传
                    resultMap.putAll(processStructuredData(result, knowledgeBase));
                } else { // 非结构化数据
                    resultMap.putAll(processUnstructuredData(result));
                }
            }
        }catch (ServiceException e){
            throw new ServiceException(e.getMessage());
        }catch (Exception e) {
            log.error("知识库检索异常 => 用户: {}, 知识库: {}, 错误信息: {}", LoginHelper.getUsername(), knowledgeBaseId, e.getMessage(), e);
        }
        return resultMap;
    }


    /**
     * 处理数据库直连知识库，获取结果
     */
    private Map<String, String> structuredDatabase(String userMessage, KnowledgeBase knowledgeBase) throws ServiceException {
        Map<String, String> resultMap = new HashMap<>();
        //根据知识库查询直连数据库信息
        List<KbDatabaseConfig> kbDatabaseConfigList = kbDatabaseConfigMapper.selectList(new LambdaQueryWrapper<KbDatabaseConfig>()
            .eq(KbDatabaseConfig::getKnowledgeId, knowledgeBase.getKnowledgeId()).eq(KbDatabaseConfig::getDelFlag, 0));
        if(kbDatabaseConfigList.isEmpty()){
            return resultMap;
        }

        //执行调用语义sql服务，获取sql
        JSONArray sql = semanticSqlUtils.semanticSql(userMessage, kbDatabaseConfigList.get(0));
        if(null == sql || sql.isEmpty()){
            return resultMap;
        }

        // 调用执行sql方法
        String sqlResult = semanticSqlUtils.doSql(sql, kbDatabaseConfigList.get(0));
        if(StringUtils.isNotBlank(sqlResult)){
            resultMap.put("查询结果：",sqlResult);
        }
        return resultMap;
    }

    /**
     * 拼接数据查询条件
     */
    private VectorServiceUtils.VectorStoreSearch buildVectorStoreSearchParam(KnowledgeBase knowledgeBase, String userMessage, LoginUser loginUser) {
        return buildVectorStoreSearchParam(knowledgeBase, userMessage, loginUser, null);
    }

    /**
     * 拼接数据查询条件（支持检索配置）
     */
    private VectorServiceUtils.VectorStoreSearch buildVectorStoreSearchParam(KnowledgeBase knowledgeBase, String userMessage, LoginUser loginUser,
                                                                            org.dromara.common.core.domain.vo.ChatMessageSendReqVO.RetrieveConfig retrieveConfig) {
        VectorServiceUtils.VectorStoreSearch param = new VectorServiceUtils.VectorStoreSearch();
        //默认走自己创建的知识库
        param.setUserPhone(loginUser.getUsername());
        //如果创建人的id不是自己，则代表是部门或者分享过来的知识库
        if (!knowledgeBase.getCreateUserId().toString().equals(loginUser.getUserId().toString())) {
            String userName = userService.selectUserNameById(knowledgeBase.getCreateUserId());
            if (StringUtils.isEmpty(userName)) {
                return null;
            }
            param.setUserPhone(userName);
        }
        param.setUserPrompt(StringUtils.isEmpty(userMessage) ? "" : userMessage);
        param.setUserType("defaultUser");
        param.setKnowledgeBaseId(knowledgeBase.getKnowledgeId().toString());
        param.setKnowledgeBaseType(knowledgeBase.getKnowledgeType());
        //结构化数据库增加字段映射
        if ("0".equals(knowledgeBase.getDataType()) && StringUtils.isNotEmpty(knowledgeBase.getFieldMapper())) {
            String fieldMapper = dataSaveService.getFieldMapper(knowledgeBase.getFieldMapper(), knowledgeBase.getKnowledgeId().toString());
            if (StringUtils.isNotEmpty(fieldMapper)) {
                param.setSchemaInfos(fieldMapper);
            }
        }

        // 设置检索配置参数
        if (retrieveConfig != null) {
            if (retrieveConfig.getTopK() != null) {
                param.setTopK(retrieveConfig.getTopK());
            }
            if (retrieveConfig.getSimilarityThreshold() != null) {
                param.setSimilarityThreshold(retrieveConfig.getSimilarityThreshold());
            }
            if (retrieveConfig.getFileRecall() != null) {
                param.setFileRecall(retrieveConfig.getFileRecall());
            }
        }

        return param;
    }


    /**
     * 处理结构化数据返回内容
     */
    private Map<String, String> processStructuredData(String result, KnowledgeBase knowledgeBase) {
        Map<String, String> resultMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject == null || !jsonObject.containsKey("查询结果")) {
            log.warn("解析结果失败，未找到 '查询结果' 字段，知识库ID: {}", knowledgeBase.getKnowledgeId().toString());
            return resultMap;
        }

        String data = jsonObject.getString("查询结果");
        if (data == null || data.isEmpty()) {
            log.warn("查询结果为空，知识库ID: {}", knowledgeBase.getKnowledgeId().toString());
            return resultMap;
        }

        if (data.startsWith("[")) { // 数组类型
            JSONArray dataArray = parseJsonArray(data);
            JSONArray fieldArray = parseJsonArray(knowledgeBase.getFieldMapper());
            if (dataArray != null && fieldArray != null) {
                Map<String, String> fieldMap = new HashMap<>();
                for (int i = 0; i < fieldArray.size(); i++) {
                    JSONObject fieldObject = fieldArray.getJSONObject(i);
                    fieldObject.forEach((key, value) -> fieldMap.put(key, (String) value));
                }
                JSONArray transformedData = new JSONArray();
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject dataObject = dataArray.getJSONObject(i);
                    JSONObject newDataObject = new JSONObject();
                    dataObject.forEach((key, value) -> {
                        String newKey = fieldMap.getOrDefault(key, key);
                        newDataObject.put(newKey, value);
                    });
                    transformedData.add(newDataObject);
                }
                resultMap.put("查询结果", JSON.toJSONString(transformedData));
            }
        } else { // 字符串类型
            resultMap.put("统计结果", data);
        }

        return resultMap;
    }

    /**
     * 处理非结构化数据返回内容
     */
    private Map<String, String> processUnstructuredData(String result) {
        Map<String, String> resultMap = new HashMap<>();
        if (!"{}".equals(result)) {
            JSONObject jsonObject = JSONObject.parseObject(result);
            //判断JsonObject中是否有：error
            if (jsonObject != null) {
                resultMap.putAll(jsonObject.toJavaObject(Map.class));
            }
        }
        return resultMap;
    }

    private JSONArray parseJsonArray(String jsonArrayStr) {
        try {
            return JSONArray.parseArray(jsonArrayStr);
        } catch (Exception e) {
            log.error("JSON 数组解析失败: {}", jsonArrayStr, e);
            return null;
        }
    }


    /**
     * 根据部门查询知识库
     *
     * @param bo
     */
    @Override
    public List<KnowledgeBase> listByDept(KnowledgeBaseBo bo) {
        List<KnowledgeBase> knowledgeBases = baseMapper.selectList(new LambdaQueryWrapper<KnowledgeBase>()
            .eq(KnowledgeBase::getDeptId, bo.getDeptId()));
        return knowledgeBases;
    }

    @Override
    public IPage<KnowledgeBaseVo> queryInstituteList(KnowledgeBaseBo bo, String sortType,KnowledgeBaseMyListDTO dto) {
        // 如果部门id是空，返回空
        if (ObjectUtil.isNull(bo.getDeptId())) {
            return new Page<>();
        }
        // 子部门id
        // 部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        deptIds.add(bo.getDeptId());
//        List<Long> knowledgeBaseVos = this.queryList(bo).stream().map(KnowledgeBaseVo::getKnowledgeId).toList();
        // 父部门
        Long deptId = bo.getDeptId();
        SysDeptVo sysDeptVo = deptService.selectDeptById(deptId);

        // 添加空值检查，防止空指针异常
        List<Long> ancestorsList = new ArrayList<>();
        if (sysDeptVo != null && StringUtils.isNotEmpty(sysDeptVo.getAncestors())) {
            String ancestors = sysDeptVo.getAncestors();
            // ancestors存放所有的父部门id,转为List<Lone>
            ancestorsList = Arrays.stream(ancestors.split(",")).map(Long::parseLong).toList();
        }

        // 字部门id 加 父部门id
        List<Long> allDeptId = new ArrayList<>();
        allDeptId.addAll(ancestorsList);
        allDeptId.addAll(deptIds);



        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getDeptId, allDeptId)
            .eq(KnowledgeBase::getDeptCategory, "institute")
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName, bo.getKnowledgeName());

        List<Long> knowledgeIds = this.selectKnowledgeIdByCategory(bo.getCategoryId());
        if (CollectionUtil.isNotEmpty(knowledgeIds)){
            queryWrapper.in(KnowledgeBase::getKnowledgeId, knowledgeIds);
        }
        // 根据排序字段进行排序
        if (StringUtils.isNotEmpty(sortType)) {
            if ("asc".equals(sortType)) {
                queryWrapper.orderByAsc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            } else if ("desc".equals(sortType)) {
                queryWrapper.orderByDesc(KnowledgeBase::getSortOrder)
                    .orderByDesc(KnowledgeBase::getCreateTime); // 二级排序
            }
        } else {
            // 默认按创建时间排序
            queryWrapper.orderByDesc(KnowledgeBase::getCreateTime);
        }
        IPage<KnowledgeBaseVo> knowledgeBaseVoIPage = baseMapper.selectVoPage(dto.getPageQuery().build(), queryWrapper);
        return knowledgeBaseVoIPage;
    }

    @Override
    public List<KnowledgeBaseVo> queryInstituteListDept(KnowledgeBaseBo bo) {
        // 如果部门id是空，返回空
        if (ObjectUtil.isNull(bo.getDeptId())) {
            return new ArrayList<KnowledgeBaseVo>();
        }
        // 子部门id
        // 部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        deptIds.add(bo.getDeptId());
        // 父部门
        Long deptId = bo.getDeptId();
        // 字部门id 加 父部门id
        List<Long> allDeptId = new ArrayList<>(deptIds);
        // 查询知识库部门id在ancestorsList中的知识库，并且部门类别为院级，按创建时间倒序排序
        List<KnowledgeBase> list = baseMapper.selectList(new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getDeptId, allDeptId)
            .eq(KnowledgeBase::getDeptCategory, "institute")
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName,bo.getKnowledgeName())
            .orderByDesc(KnowledgeBase::getCreateTime));
        return MapstructUtils.convert(list, KnowledgeBaseVo.class);
    }


    /**
     * 知识库-并行检索
     * @param knowledgeBaseIdList 选中知识库id
     * @param userMessage 用户提示词
     */
    @Override
    public Map<String, Object> getDocumentInfosParallel(List<String> knowledgeBaseIdList, String userMessage) {
        return getDocumentInfosParallel(knowledgeBaseIdList, userMessage, null);
    }

    /**
     * 知识库-并行检索（支持检索配置）
     * @param knowledgeBaseIdList 选中知识库id
     * @param userMessage 用户提示词
     * @param retrieveConfig 检索配置
     */
    public Map<String, Object> getDocumentInfosParallel(List<String> knowledgeBaseIdList, String userMessage,
                                                      org.dromara.common.core.domain.vo.ChatMessageSendReqVO.RetrieveConfig retrieveConfig) throws ServiceException {
        // 返回结果
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> searchMap = new LinkedHashMap<>();

        // 查询知识库信息
        List<KnowledgeBase> knowledgeBases = baseMapper.selectList(new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getKnowledgeId, knowledgeBaseIdList));
        if (knowledgeBases.isEmpty()) {
            log.warn("知识库列表查询为空");
            return searchMap;
        }
        Map<Long, KnowledgeBase> kbMap = knowledgeBases.stream()
            .collect(Collectors.toMap(KnowledgeBase::getKnowledgeId, Function.identity()));

        // 登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();

        // 使用 CompletableFuture 来简化并行任务的管理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < knowledgeBaseIdList.size(); i++) {
            final int index = i;
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    String knowledgeBaseId = knowledgeBaseIdList.get(index);
                    KnowledgeBase knowledgeBase = kbMap.get(Long.valueOf(knowledgeBaseId));
                    String name = (knowledgeBase == null) ? knowledgeBaseId : knowledgeBase.getKnowledgeName();

                    // 调用 getDocumentInfos 获取文档信息，传递检索配置
                    Map<String, String> futureResult = getDocumentInfos(knowledgeBaseId, userMessage, loginUser, retrieveConfig);

                    // 处理 futureResult
                    if(knowledgeBase != null && ObjectUtil.equals(knowledgeBase.getDataType(),"0")){
                        searchMap.put("数据库:" + name, futureResult);
                    }else {
                        Map<String, String> processedResult = processDocumentInfos(futureResult);
                        searchMap.put("知识库:" + name, processedResult);
                    }
                    // 将处理结果存入 searchMap
                } catch (ServiceException e){
                    throw new ServiceException("检索异常" +e.getMessage());
                } catch (Exception e) {
                    log.error("知识库 {} 检索异常: {}", knowledgeBaseIdList.get(index), e.getMessage(), e);
                    searchMap.put(knowledgeBaseIdList.get(index), "");
                }
            }));
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        resultMap.put("查询结果", searchMap);
        return resultMap;
    }

    /**
     * 处理文档信息，将文件名称格式化，并移除不必要的字段
     */
    private Map<String, String> processDocumentInfos(Map<String, String> futureResult) {
        Map<String, String> processedResult = new HashMap<>();
        for (Map.Entry<String, String> entry : futureResult.entrySet()) {
            String key = entry.getKey();
            String strJson = JSON.toJSONString(entry.getValue());
            JSONObject jsonObject = JSONObject.parseObject(strJson);

            String newKey = key;
            String fileId = jsonObject.getString("文件名称");

            if (StringUtils.isNotEmpty(fileId)) {
                KnowledgeFile knowledgeFile = knowledgeFileService.selectById(Long.valueOf(fileId));
                if (knowledgeFile != null) {
//                    String fileName = "@<fileName>" + knowledgeFile.getFileName() + "@<fileId>" + knowledgeFile.getOssId() + "@</fileId>@</fileName>";
                    String fileName = "@[filedId]{" + knowledgeFile.getOssId() + "}{" + knowledgeFile.getFileName() + "}";
                    jsonObject.put("文件名称", fileName);
                    newKey = knowledgeFile.getFileName();
                }
            }

            if (StringUtils.isNotEmpty(jsonObject.getString("知识库名称"))) {
                jsonObject.remove("知识库名称");
            }

            processedResult.put(newKey, jsonObject.toJSONString());
        }
        return processedResult;
    }



    /**
     * 构建查询条件
     *
     * @param bo 知识库信息
     * @return 查询条件
     */
    private com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeBase> buildQueryWrapper(KnowledgeBaseBo bo) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeBase> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(bo.getCreateUserId() != null, KnowledgeBase::getCreateUserId, bo.getCreateUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getKnowledgeType()), KnowledgeBase::getKnowledgeType, bo.getKnowledgeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDataType()), KnowledgeBase::getDataType, bo.getDataType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), KnowledgeBase::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName, bo.getKnowledgeName());
        lqw.eq(bo.getDeptId() != null, KnowledgeBase::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 创建知识库并建立用户关联
     *
     * @param bo 知识库信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createKnowledgeBase(KnowledgeBaseBo bo) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        // 知识库最大数量限制为5个
        if (countByUserId(userId) >= 8) {
            throw new ServiceException("知识库数量已达上限，无法创建新的知识库");
        }

        // 设置创建用户ID
        bo.setCreateUserId(userId);
        if (ObjectUtil.equals("0", bo.getDataType())) {
            bo.setKnowledgeType("structured");
        } else {
            bo.setKnowledgeType("not_structured");
        }
        Long deptId = bo.getDeptId();
        KnowledgeUserRelBo relBo = new KnowledgeUserRelBo();
        // 查询部门信息
        if (deptId != null) {
            SysDeptVo sysDeptVo = deptService.selectDeptById(deptId);
            // 院级
            if (sysDeptVo != null && StringUtils.isNotEmpty(sysDeptVo.getDeptCategory())) {
                bo.setDeptCategory("institute");
                relBo.setSourceType(KnowledgeBaseSourceTypeEnum.INSTITUTE.getCode());
            }
            // 部门级
            else {
                relBo.setSourceType(KnowledgeBaseSourceTypeEnum.DEPARTMENT.getCode());
            }
        }else {
            // 个人级别
            relBo.setSourceType(KnowledgeBaseSourceTypeEnum.PERSONAL.getCode());
        }

        // 创建知识库
        boolean result = this.insertByBo(bo);
        // 创建知识库用户关联关系
        if (result && bo.getKnowledgeId() != null) {
            // 添加创建者为管理员权限
            relBo.setKnowledgeId(bo.getKnowledgeId());
            relBo.setUserId(userId);
            relBo.setPermissionType("2"); // 管理权限
            knowledgeUserRelService.insertByBo(relBo);

            // 如果有指定其他用户，则添加用户关联
            if (bo.getUserIds() != null && !bo.getUserIds().isEmpty()) {
                for (Long otherUserId : bo.getUserIds()) {
                    // 跳过创建者自身，因为已经添加过了
                    if (otherUserId.equals(userId)) {
                        continue;
                    }

                    KnowledgeUserRelBo userRelBo = new KnowledgeUserRelBo();
                    userRelBo.setKnowledgeId(bo.getKnowledgeId());
                    userRelBo.setUserId(otherUserId);

                    // 如果指定了权限类型，则使用指定的权限类型，否则默认为查看权限
                    String permissionType = bo.getDefaultPermissionType();
                    if (StringUtils.isEmpty(permissionType)) {
                        permissionType = "0"; // 默认查看权限
                    }
                    userRelBo.setPermissionType(permissionType);

                    knowledgeUserRelService.insertByBo(userRelBo);
                }
            }
            // 创建分类关联关系
            if (bo.getCategoryId() != null) {
                knowledgeCategoryRelationService.bindKnowledgesToCategory(new KnowledgeCategoryRelationDTO(bo.getCategoryId(), Collections.singletonList(bo.getKnowledgeId())));
            }
        }

        return result;
    }

    private Long countByUserId(Long userId) {
        // 通过user查询数据库的数量
//        return knowledgeUserRelMapper
//            .selectCount(new LambdaQueryWrapper<KnowledgeUserRel>().eq(KnowledgeUserRel::getUserId, userId));
        return knowledgeUserRelMapper.selectKnowledgeCountByUserId(userId);
    }

    /**
     * 根据分类ID查询院级知识库列表
     *
     * @param bo 知识库
     * @param categoryId 分类ID
     * @return 知识库集合
     */
    @Override
    public List<KnowledgeBaseVo> queryInstituteListDeptByCategory(KnowledgeBaseBo bo, Long categoryId) {


        // 如果部门id是空，返回空
        if (ObjectUtil.isNull(bo.getDeptId())) {
            return new ArrayList<KnowledgeBaseVo>();
        }
        // 子部门id
        // 部门id查询所有字部门
        List<SysDeptVo> sysDeptVos = deptService.selectDeptListByUserId(bo.getDeptId());
        List<Long> deptIds = new ArrayList<>(sysDeptVos.stream().map(SysDeptVo::getDeptId).toList());
        deptIds.add(bo.getDeptId());
        // 父部门
        Long deptId = bo.getDeptId();
        // 字部门id 加 父部门id
        List<Long> allDeptId = new ArrayList<>(deptIds);

        List<Long> knowledgeIds  = selectKnowledgeIdByCategory(categoryId);
        // 查询知识库部门id在ancestorsList中的知识库，并且部门类别为院级，按创建时间倒序排序
        List<KnowledgeBase> list = baseMapper.selectList(new LambdaQueryWrapper<KnowledgeBase>()
            .in(KnowledgeBase::getDeptId, allDeptId)
             .in(CollectionUtil.isNotEmpty(knowledgeIds), KnowledgeBase::getKnowledgeId, knowledgeIds)
            .eq(KnowledgeBase::getDeptCategory, "institute")
            .like(org.apache.commons.lang3.StringUtils.isNotEmpty(bo.getKnowledgeName()), KnowledgeBase::getKnowledgeName,bo.getKnowledgeName())
            .orderByDesc(KnowledgeBase::getCreateTime));
        return MapstructUtils.convert(list, KnowledgeBaseVo.class);
    }

}
