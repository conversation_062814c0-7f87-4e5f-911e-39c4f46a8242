package org.dromara.business.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.business.domain.BsProjectAccept;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 施工图项目受理清单视图对象 bs_project_accept
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BsProjectAccept.class)
public class BsProjectAcceptVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 县区
     */
    @ExcelProperty(value = "县区")
    private String district;

    /**
     * 图审机构
     */
    @ExcelProperty(value = "图审机构")
    private String drawingReviewAgency;

    /**
     * 政审机构
     */
    @ExcelProperty(value = "政审机构")
    private String politicalReviewAgency;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String projectNumber;

    /**
     * 建设单位
     */
    @ExcelProperty(value = "建设单位")
    private String constructionCompany;

    /**
     * 设计单位
     */
    @ExcelProperty(value = "设计单位")
    private String designCompany;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 建筑类型
     */
    @ExcelProperty(value = "建筑类型")
    private String buildingType;

    /**
     * 建筑面积（㎡）
     */
    @ExcelProperty(value = "建筑面积(㎡)")
    private BigDecimal constructionAreaSqm;

    /**
     * 规划面积（㎡）
     */
    @ExcelProperty(value = "规划面积(㎡)")
    private BigDecimal plannedAreaSqm;

    /**
     * 机构遴选日期
     */
    @ExcelProperty(value = "机构遴选日期")
    private Date agencySelectionDate;

    /**
     * 是否自费
     */
    @ExcelProperty(value = "是否自费")
    private String isSelfFunded;

    /**
     * 合格书盖章日期
     */
    @ExcelProperty(value = "合格书盖章日期")
    private Date qualifiedStampDate;


}
