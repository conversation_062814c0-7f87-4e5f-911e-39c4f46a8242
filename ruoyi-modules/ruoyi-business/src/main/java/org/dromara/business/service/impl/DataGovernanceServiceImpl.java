package org.dromara.business.service.impl;

import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.business.domain.KnowledgeFile;
import org.dromara.business.domain.vo.KnowledgeBaseVo;
import org.dromara.business.mapper.KnowledgeBaseMapper;
import org.dromara.business.service.IDataGovernanceService;
import org.dromara.business.utils.VectorServiceUtils;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

/**
 * 数据治理服务实现
 * 集成向量数据库服务
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DataGovernanceServiceImpl implements IDataGovernanceService {

    @Resource
    private KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 向量存储接口
     *
     * @param knowledgeId 知识库ID
     * @param fileId      文件ID
     * @param content     文件内容
     * @return 向量ID
     */
    @Override
    public String vectorStore(Long knowledgeId, Long fileId, String content) {
        log.info("向量存储接口调用：knowledgeId={}, fileId={}, contentLength={}", knowledgeId, fileId, content.length());

        try {
            // 获取当前用户信息
            String userPhone = LoginHelper.getUsername();
            String userType = LoginHelper.getUserType().getUserType();

            // 调用向量服务工具类存储向量数据
            return VectorServiceUtils.saveVectorData(
                userPhone,
                userType,
                String.valueOf(knowledgeId),
                "", // 知识库类型，暂时为空
                String.valueOf(fileId),
                content,
                1, // 索引编号默认为1
                "1" ,// 文件总长度默认为1
                null
            );
        } catch (Exception e) {
            log.error("向量存储失败", e);
            return null;
        }
    }

    /**
     * 向量删除接口
     *
     * @param fileId 文件id
     * @return 结果
     */
    @Override
    public Boolean vectorDelete(KnowledgeFile file,LoginUser loginUser) {
        log.info("向量删除接口调用：fileId={}", file);
        KnowledgeBaseVo knowledgeBaseVo = knowledgeBaseMapper.selectVoById(file.getKnowledgeId());
        // 用户手机号
        String userPhone = loginUser.getUsername();
        // 用户类型
        String userType = loginUser.getUserType();


        // 调用向量服务工具类删除向量数据
        return VectorServiceUtils.deleteVectorData(
            userPhone,
            userType,
            knowledgeBaseVo.getKnowledgeId().toString(),
            knowledgeBaseVo.getKnowledgeType(),
            file.getFileId().toString()
        );
    }

    /**
     * 向量检索接口
     *
     * @param knowledgeId 知识库ID
     * @param userId      用户ID
     * @param userType    用户类型
     * @param keyword     检索关键词
     * @return 检索结果
     */
    @Override
    public String vectorSearch(Long knowledgeId, Long userId, String userType, String keyword) {
        log.info("向量检索接口调用：knowledgeId={}, userId={}, userType={}, keyword={}", knowledgeId, userId, userType, keyword);
        // TODO: 对接实际的向量数据库服务
        return "模拟的检索结果：" + keyword;
    }
}
