package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.business.domain.bo.BsProjectAcceptBo;
import org.dromara.business.domain.vo.BsProjectAcceptVo;
import org.dromara.business.service.IBsProjectAcceptService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.awt.Color;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 施工图项目受理清单
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/projectAccept")
public class BsProjectAcceptController extends BaseController {

    private final IBsProjectAcceptService bsProjectAcceptService;

    /**
     * 查询施工图项目受理清单列表
     */
    @SaCheckPermission("business:projectAccept:list")
    @GetMapping("/list")
    public TableDataInfo<BsProjectAcceptVo> list(BsProjectAcceptBo bo, PageQuery pageQuery) {
        return bsProjectAcceptService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出施工图项目受理清单列表
     */
    @SaCheckPermission("business:projectAccept:export")
    @Log(title = "施工图项目受理清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BsProjectAcceptBo bo, HttpServletResponse response) {
        List<BsProjectAcceptVo> list = bsProjectAcceptService.queryList(bo);
        ExcelUtil.exportExcel(list, "施工图项目受理清单", BsProjectAcceptVo.class, response);
    }

    /**
     * 获取施工图项目受理清单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:projectAccept:query")
    @GetMapping("/{id}")
    public R<BsProjectAcceptVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(bsProjectAcceptService.queryById(id));
    }

    /**
     * 新增施工图项目受理清单
     */
    @SaCheckPermission("business:projectAccept:add")
    @Log(title = "施工图项目受理清单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BsProjectAcceptBo bo) {
        return toAjax(bsProjectAcceptService.insertByBo(bo));
    }

    /**
     * 修改施工图项目受理清单
     */
    @SaCheckPermission("business:projectAccept:edit")
    @Log(title = "施工图项目受理清单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BsProjectAcceptBo bo) {
        return toAjax(bsProjectAcceptService.updateByBo(bo));
    }

    /**
     * 删除施工图项目受理清单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:projectAccept:remove")
    @Log(title = "施工图项目受理清单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(bsProjectAcceptService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 数据导入
     * @param file 导入文件
     * @return 导入结果
     */
    @PostMapping("/upload")
    public R<String> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return R.fail("上传的文件不能为空");
        }

        // 同步处理导入
        bsProjectAcceptService.importProjectAcceptSync(file);
        return R.ok("导入成功");
    }


    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("施工图项目受理清单导入模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 使用 Apache POI 创建 Excel 模板
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("施工图项目受理清单导入模板");

            // 设置整个工作表的默认行高为22点
            sheet.setDefaultRowHeightInPoints(22);

            // 创建表头行
            Row headerRow = sheet.createRow(0);

            // 定义表头数据
            String[] headers = {"县区", "图审机构", "政审机构", "编号", "建设单位", "设计单位", "项目名称", "建筑类型", "建筑面积(㎡)", "规划面积(㎡)", "机构遴选日期", "是否自费", "合格书盖章日期"};

            // 创建表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 创建表头单元格和设置列宽
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 5000); // 15个字符宽度
            }

            workbook.write(response.getOutputStream());
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();

        // 设置对齐方式
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置背景色和边框
        if (workbook instanceof XSSFWorkbook) {
            org.apache.poi.xssf.usermodel.XSSFCellStyle xssfStyle =
                (org.apache.poi.xssf.usermodel.XSSFCellStyle) headerStyle;

            // 背景色 #e6f1f8
            XSSFColor backgroundColor = new XSSFColor(new Color(139, 139, 139), null);
            xssfStyle.setFillForegroundColor(backgroundColor);
            xssfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 边框色 #cfe6f5
            XSSFColor borderColor = new XSSFColor(new Color(139, 139, 139), null);
            xssfStyle.setBorderTop(BorderStyle.MEDIUM);
            xssfStyle.setBorderBottom(BorderStyle.MEDIUM);
            xssfStyle.setBorderLeft(BorderStyle.MEDIUM);
            xssfStyle.setBorderRight(BorderStyle.MEDIUM);
            xssfStyle.setTopBorderColor(borderColor);
            xssfStyle.setBottomBorderColor(borderColor);
            xssfStyle.setLeftBorderColor(borderColor);
            xssfStyle.setRightBorderColor(borderColor);
        } else {
            // 兼容旧版本 Excel
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.MEDIUM);
            headerStyle.setBorderBottom(BorderStyle.MEDIUM);
            headerStyle.setBorderLeft(BorderStyle.MEDIUM);
            headerStyle.setBorderRight(BorderStyle.MEDIUM);
        }

        // 设置字体
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);

        return headerStyle;
    }
}
