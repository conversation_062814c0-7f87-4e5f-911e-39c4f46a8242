package org.dromara.business.utils.baseEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = -6689010616909835472L;

//    private Rows<T> data;

    private List<T> rows;

    private PageResponse page;

}
