package org.dromara.business.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.business.domain.AiModelChild;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 模型算力地址业务对象 ai_model_child
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiModelChild.class, reverseConvertGenerate = false)
public class AiModelChildBo extends BaseEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型地址
     */
    private String modelUrl;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型速度
     */
    private String modelSpeed;


}
