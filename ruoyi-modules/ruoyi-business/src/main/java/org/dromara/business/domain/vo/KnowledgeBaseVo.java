package org.dromara.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.KnowledgeBase;
import org.dromara.business.vo.KnowledgeCategoryVO;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 知识库视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeBase.class)
public class KnowledgeBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    @ExcelProperty(value = "知识库名称")
    private String knowledgeName;

    /**
     * 知识库类型
     */
    @ExcelProperty(value = "知识库类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "knowledge_type")
    private String knowledgeType;

    /**
     * 知识库描述
     */
    @ExcelProperty(value = "知识库描述")
    private String description;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "knowledge_data_type")
    private String dataType;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "common_status")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 文件数量
     */
    @ExcelProperty(value = "文件数量")
    private Integer fileCount;

    /**
     * 结构化字段映射关系
     */
    private String fieldMapper;

    /**
     * 部门id
     */
    private Long deptId;


    /**
     * 部门关系
     */
    private String deptCategory;

    /**
     * 知识库学习方式(1-文件上传模式，2-数据库直连模式)
     */
    private Integer learningStyle;

    /**
     * 创建类型
     */
    private String buildType;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 排序字段（值越小越靠前）
     */
    private Integer sortOrder;

}
