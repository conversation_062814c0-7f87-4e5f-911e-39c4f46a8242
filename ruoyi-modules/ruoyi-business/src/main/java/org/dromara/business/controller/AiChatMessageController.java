package org.dromara.business.controller;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.business.domain.AiChatMessage;
import org.dromara.business.domain.bo.AiChatMessageBo;
import org.dromara.business.domain.vo.AiChatMessageVo;
import org.dromara.business.domain.vo.EditChatMessageVo;
import org.dromara.business.service.IAiChatMessageService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

/**
 * AI 聊天消息
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@SaIgnore
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/chatMessage")
public class AiChatMessageController extends BaseController {

    private final IAiChatMessageService iAiChatMessageService;

    /**
     * 查询AI 聊天消息列表
     */
    @SaCheckPermission("system:chatMessage:list")
    @GetMapping("/list")
    public TableDataInfo<AiChatMessage> list(AiChatMessageBo bo, PageQuery pageQuery) {
        return iAiChatMessageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出AI 聊天消息列表
     */
    @SaCheckPermission("system:chatMessage:export")
    @Log(title = "AI 聊天消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiChatMessageBo bo, HttpServletResponse response) {
        List<AiChatMessageVo> list = iAiChatMessageService.queryList(bo);
        ExcelUtil.exportExcel(list, "AI 聊天消息", AiChatMessageVo.class, response);
    }

    /**
     * 获取AI 聊天消息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:chatMessage:query")
    @GetMapping("/{id}")
    public R<AiChatMessageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAiChatMessageService.queryById(id));
    }

    /**
     * 新增AI 聊天消息
     */
    @SaCheckPermission("system:chatMessage:add")
    @Log(title = "AI 聊天消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiChatMessageBo bo) {
        return toAjax(iAiChatMessageService.insertByBo(bo));
    }

    /**
     * 修改AI 聊天消息
     */
    @SaCheckPermission("system:chatMessage:edit")
    @Log(title = "AI 聊天消息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiChatMessageBo bo) {
        return toAjax(iAiChatMessageService.updateByBo(bo));
    }

    /**
     * 删除AI 聊天消息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:chatMessage:remove")
    @Log(title = "AI 聊天消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAiChatMessageService.deleteWithValidByIds(Arrays.asList(ids), true));
    }


    @SaIgnore
    @GetMapping("/list-by-conversation-id")
//    @Log(title = "获得指定对话的消息列表", businessType = BusinessType.OTHER)
    public R<List<AiChatMessageVo>> getChatMessageListByConversationId(
        @RequestParam("conversationId") Long conversationId) {
        return R.ok(iAiChatMessageService.getChatMessageListByConversationId(conversationId));
    }

    /**
     * 编辑一条对话消息
     * @param editChatMessageVo 编辑的消息
     */
    @SaIgnore
    @PostMapping("/edit")
    public R<Boolean> editChatMessage(@RequestBody EditChatMessageVo editChatMessageVo) {
        return R.ok(iAiChatMessageService.editChatMessage(editChatMessageVo.getId(), editChatMessageVo.getContent()));
    }

    @DeleteMapping("/delete")
//    @Log(title = "删除聊天对话", businessType = BusinessType.OTHER)
    public R<Boolean> deleteChatMessage(@RequestParam("id") Long id) {
        iAiChatMessageService.deleteChatMessage(id);
        return R.ok("删除成功");
    }

    @DeleteMapping("/delete-by-conversation-id")
//    @Log(title = "删除指定对话的消息", businessType = BusinessType.OTHER)
    public R<Boolean> deleteChatMessageByConversationId(@RequestParam("conversationId") Long conversationId) {
        iAiChatMessageService.deleteChatMessageByConversationId(conversationId);
        return R.ok("删除成功");
    }

    @PostMapping("/clearMessages")
    public R<Boolean> clearMessages(@RequestBody AiChatMessageBo bo) {
        return R.ok(iAiChatMessageService.clearMessages(bo.getConversationId()));
    }


}
