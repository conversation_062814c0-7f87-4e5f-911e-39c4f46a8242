package org.dromara.business.aspect;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.dromara.business.domain.AiModel;
import org.dromara.business.domain.AiModelAuth;
import org.dromara.business.mapper.AiModelAuthMapper;
import org.dromara.business.mapper.AiModelMapper;
import org.dromara.common.core.annotation.CheckAuth;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.vo.ChatMessageSendRespVO;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import reactor.core.publisher.Flux;

/**
 * 校验模型key合法性
 */
@Aspect
@Component
public class CheckAuthAspect {

    @Autowired
    private AiModelMapper aiModelMapper;
    @Autowired
    private AiModelAuthMapper aiModelAuthMapper;


    @Before("@annotation(checkAuth)")
    public void doBefore(JoinPoint point, CheckAuth checkAuth) {

    }




}
