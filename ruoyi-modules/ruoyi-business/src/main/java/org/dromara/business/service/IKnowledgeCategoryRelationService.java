package org.dromara.business.service;

import org.dromara.business.domain.KnowledgeCategoryRelation;
import org.dromara.business.dto.KnowledgeCategoryRelationDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 知识库分类关联Service接口
 */
public interface IKnowledgeCategoryRelationService extends IService<KnowledgeCategoryRelation> {

    /**
     * 绑定知识库到分类
     *
     * @param dto 绑定信息
     * @return 结果
     */
    boolean bindKnowledgesToCategory(KnowledgeCategoryRelationDTO dto);

    /**
     * 解绑知识库与分类的关联
     *
     * @param categoryId 分类ID
     * @param knowledgeId 知识库ID
     * @return 结果
     */
    boolean unbindKnowledgeFromCategory(Long categoryId, Long knowledgeId);

    /**
     * 根据分类ID获取关联的知识库ID列表
     *
     * @param categoryId 分类ID
     * @return 知识库ID列表
     */
    List<Long> getKnowledgeIdsByCategoryId(Long categoryId);

    /**
     * 根据知识库ID获取关联的分类ID列表
     *
     * @param knowledgeId 知识库ID
     * @return 分类ID列表
     */
    List<Long> getCategoryIdsByKnowledgeId(Long knowledgeId);

    /**
     * 更新知识库关联的分类
     *
     * @param knowledgeId 知识库ID
     * @param categoryId 新的分类ID
     * @return 结果
     */
    boolean updateKnowledgeCategory(Long knowledgeId, Long categoryId);
}
