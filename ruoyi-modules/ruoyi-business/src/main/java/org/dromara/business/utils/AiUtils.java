package org.dromara.business.utils;

import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.ollama.api.OllamaOptions;

/**
 * Spring AI 工具类
 */
public class AiUtils {

    public static ChatOptions buildChatOptions(String model, Double temperature, Integer maxTokens) {
        return OllamaOptions.builder().model(model).temperature(temperature).numPredict(maxTokens).build();
    }

}
