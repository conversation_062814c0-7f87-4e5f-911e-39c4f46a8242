package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 知识库实体类
 */
@Data
@TableName("bs_knowledge_base")
public class KnowledgeBase extends BaseEntity {

    /**
     * 知识库ID
     */
    @TableId
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    private String knowledgeName;

    /**
     * 知识库类型
     */
    private String knowledgeType;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 数据类型（0结构化数据 1非结构化数据）
     */
    private String dataType;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 文件数量
     */
    @TableField(exist = false)
    private Integer fileCount;


    /**
     * 结构化字段映射关系
     */
    private String fieldMapper;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门关系
     */
    private String deptCategory;

    /**
     * 知识库学习方式(1-文件上传模式，2-数据库直连模式)
     */
    private Integer learningStyle;

    /**
     * 创建类型
     */
    private String buildType;

    /**
     * 排序字段（值越小越靠前）
     */
    private Integer sortOrder;

}
