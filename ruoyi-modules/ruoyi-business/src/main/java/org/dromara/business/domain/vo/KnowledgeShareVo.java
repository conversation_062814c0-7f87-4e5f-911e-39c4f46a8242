package org.dromara.business.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.business.domain.bo.KnowledgeShareBo;

/**
 * 知识库分享视图对象
 */
@Data
@AutoMapper(target = KnowledgeShareBo.class)
public class KnowledgeShareVo {

    /**
     * 分享链接
     */
    private String shareUrl;
    
    /**
     * 加密后的用户ID
     */
    private String encryptedUserId;
}
