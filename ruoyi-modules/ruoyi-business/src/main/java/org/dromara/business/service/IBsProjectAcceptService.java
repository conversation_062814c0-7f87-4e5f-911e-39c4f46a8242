package org.dromara.business.service;

import org.dromara.business.domain.vo.BsProjectAcceptVo;
import org.dromara.business.domain.bo.BsProjectAcceptBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 施工图项目受理清单Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IBsProjectAcceptService {

    /**
     * 查询施工图项目受理清单
     *
     * @param id 主键
     * @return 施工图项目受理清单
     */
    BsProjectAcceptVo queryById(Long id);

    /**
     * 分页查询施工图项目受理清单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 施工图项目受理清单分页列表
     */
    TableDataInfo<BsProjectAcceptVo> queryPageList(BsProjectAcceptBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的施工图项目受理清单列表
     *
     * @param bo 查询条件
     * @return 施工图项目受理清单列表
     */
    List<BsProjectAcceptVo> queryList(BsProjectAcceptBo bo);

    /**
     * 新增施工图项目受理清单
     *
     * @param bo 施工图项目受理清单
     * @return 是否新增成功
     */
    Boolean insertByBo(BsProjectAcceptBo bo);

    /**
     * 修改施工图项目受理清单
     *
     * @param bo 施工图项目受理清单
     * @return 是否修改成功
     */
    Boolean updateByBo(BsProjectAcceptBo bo);

    /**
     * 校验并批量删除施工图项目受理清单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 异步导入施工图项目受理清单
     * @param file 要导入的 Excel 文件
     * @param importNo 导入流水号
     */
    void importProjectAcceptAsync(MultipartFile file, String importNo);

    /**
     * 异步导入施工图项目受理清单（不初始化进度）
     * @param file 要导入的 Excel 文件
     * @param importNo 导入流水号
     */
    void importProjectAcceptAsyncWithoutInit(MultipartFile file, String importNo);

    /**
     * 在Controller线程中初始化导入进度
     * @param importNo 导入流水号
     */
    void initImportProgressInController(String importNo);

    /**
     * 查询导入进度
     *
     * @param importNo 导入流水号
     * @return 进度信息
     */
    Map<String, Object> getImportProgress(String importNo);

    /**
     * 从监听器更新导入进度（仅更新百分比和消息，不更新行数）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     */
    void updateImportProgressFromListener(String importNo, int percentage, String message);

    /**
     * 从监听器更新导入进度（包含行数信息）
     *
     * @param importNo 导入流水号
     * @param percentage 进度百分比
     * @param message 进度消息
     * @param totalRows 总行数
     * @param processedRows 已处理行数
     */
    void updateImportProgressWithRowsFromListener(String importNo, int percentage, String message, Long totalRows, Long processedRows);

    /**
     * 同步导入施工图项目受理清单
     * @param file 要导入的 Excel 文件
     */
    void importProjectAcceptSync(MultipartFile file);
}
