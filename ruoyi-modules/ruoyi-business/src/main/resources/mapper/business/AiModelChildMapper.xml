<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.AiModelChildMapper">



    <select id="queryPageList" resultType="org.dromara.business.domain.vo.AiModelChildVo">
        SELECT
            aaa.id,
            aaa.model_url,
            aaa.model_id,
            aaa.model_speed,
            bbb.model_code,
            bbb.model_name,
            bbb.model_logo_url
        FROM
            ai_model_child aaa
                LEFT JOIN ai_model bbb ON aaa.model_id = bbb.id
        WHERE
            aaa.del_flag = 0
        <include refid="common_where"></include>
        ORDER BY aaa.create_time DESC
    </select>

    <sql id="common_where">
        <if test="bo.modelId != null and bo.modelId != ''">
            and aaa.model_id = #{bo.modelId}
        </if>
        <if test="bo.modelName != null and bo.modelName != ''">
            and bbb.model_name like concat( #{bo.modelName}, '%')
        </if>
    </sql>


</mapper>
