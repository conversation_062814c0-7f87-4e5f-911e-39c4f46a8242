<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.AiModelMapper">

    <select id="queryPageList" resultType="org.dromara.business.domain.vo.AiModelVo">
        SELECT
            aaa.*,
            COUNT(bbb.id) AS modelUrlCount
        FROM
            ai_model aaa
                LEFT JOIN ai_model_child bbb ON aaa.id = bbb.model_id
                AND bbb.del_flag = 0
        WHERE
            aaa.del_flag = 0
        GROUP BY
            aaa.id
        ORDER BY
            aaa.create_time ASC
    </select>

</mapper>
