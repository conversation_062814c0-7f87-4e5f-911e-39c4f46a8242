<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.AiChatConversationMapper">



    <select id="getChatConversationListByUserId" resultType="org.dromara.business.domain.vo.AiChatConversationVo" parameterType="org.dromara.business.domain.bo.AiChatConversationBo">
        SELECT
            aaa.*,
            bbb.model_logo_url
        FROM
            ai_chat_conversation aaa
                LEFT JOIN ai_model bbb ON aaa.model_id = bbb.id
        WHERE aaa.del_flag = 0
            <include refid="common_where"></include>
        ORDER BY aaa.create_time DESC
    </select>
    <sql id="common_where">
        <if test="bo.modelKey != null and bo.modelKey != ''">
            and aaa.model_key = #{bo.modelKey}
        </if>
        <if test="bo.browserId != null and bo.browserId != ''">
            and aaa.browser_id = #{bo.browserId} AND aaa.user_id IS NULL
        </if>
        <if test="bo.projectId != null and bo.projectId != ''">
            and aaa.project_id like concat( #{bo.projectId}, '%')
        </if>
        <if test="bo.userId != null and bo.userId != ''">
            and aaa.user_id = #{bo.userId}
        </if>
    </sql>


    <select id="queryPageList" resultType="org.dromara.business.domain.vo.AiChatConversationVo">
        SELECT
            aaa.*,
            bbb.project,
            bbb.user_com
        FROM
            ai_chat_conversation aaa
            left join ai_model_auth bbb on aaa.model_key = bbb.model_key
        WHERE aaa.id is not null
        <include refid="common_where_two"></include>
        ORDER BY aaa.create_time DESC
    </select>

    <sql id="common_where_two">
        <if test="bo.title != null and bo.title != ''">
            and aaa.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.modelName != null and bo.modelName != ''">
            and aaa.model_name like concat('%', #{bo.modelName}, '%')
        </if>
        <if test="bo.project != null and bo.project != ''">
            and bbb.project like concat('%', #{bo.project}, '%')
        </if>
        <if test="bo.userCom != null and bo.userCom != ''">
            and bbb.user_com like concat('%', #{bo.userCom}, '%')
        </if>
        <if test="bo.userId != null and bo.userId != '' and (bo.modelKey == null or bo.modelKey == '')">
            and aaa.user_id = #{bo.userId}
        </if>
        <if test="bo.modelKey != null and bo.modelKey != '' and bo.userId != null and bo.userId != '' ">
            and (
                aaa.model_key = #{bo.modelKey} or aaa.user_id = #{bo.userId}
            )
        </if>
    </sql>

</mapper>
