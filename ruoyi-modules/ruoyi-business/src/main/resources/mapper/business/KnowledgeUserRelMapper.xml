<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.KnowledgeUserRelMapper">


    <select id="selectKnowledgeCountByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM bs_knowledge_user_rel rel
                 JOIN bs_knowledge_base base ON rel.knowledge_id = base.knowledge_id
        WHERE rel.user_id = #{userId}
          AND (base.dept_id IS NULL OR base.dept_id = '')
          AND (base.dept_category IS NULL OR base.dept_category = '')
    </select>

    <select id="selectSourceTypeByKnowledgeIds" resultType="org.dromara.business.domain.KnowledgeUserRel">
        SELECT
            knowledge_id,
            source_type
        FROM
            bs_knowledge_user_rel
        WHERE
            knowledge_id IN
            <foreach collection="knowledgeIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    
    <select id="selectSourceTypeAndDeptByKnowledgeIds" resultType="java.util.Map">
        SELECT
            rel.knowledge_id,
            rel.source_type,
            base.dept_id
        FROM
            bs_knowledge_user_rel rel
        JOIN
            bs_knowledge_base base ON rel.knowledge_id = base.knowledge_id
        WHERE
            rel.knowledge_id IN
            <foreach collection="knowledgeIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
</mapper>
