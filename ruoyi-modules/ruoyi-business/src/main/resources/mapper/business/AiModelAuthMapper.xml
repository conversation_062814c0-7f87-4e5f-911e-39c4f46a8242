<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.AiModelAuthMapper">

    <select id="queryPageList" resultType="org.dromara.business.domain.vo.AiModelAuthVo">
        SELECT
            aaa.*,
            bbb.temperature AS temperature,
            bbb.max_tokens AS maxTokens,
            ccc.user_name
        FROM
            ai_model_auth aaa
            LEFT JOIN ai_model bbb ON aaa.model_id = bbb.id
            LEFT JOIN sys_user ccc ON aaa.user_id = ccc.user_id
        WHERE
            aaa.del_flag = 0
            <include refid="common_where"></include>
        ORDER BY aaa.create_time DESC
    </select>

    <select id="getMonthCount" resultType="org.dromara.business.vo.AiModelAuthCountVO">
        SELECT
        a.model_key,
        a.model_name,
        a.user_com,
        a.project,
        IFNULL(conv.conversation_count, 0) AS conversation_count,
        IFNULL(msg.message_count, 0) AS message_count
        FROM
        ai_model_auth a
        LEFT JOIN (
        SELECT
        model_key,
        COUNT(*) AS conversation_count
        FROM
        ai_chat_conversation
        WHERE
        create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
        AND create_time &lt; DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
        GROUP BY
        model_key
        ) conv ON a.model_key = conv.model_key
        LEFT JOIN (
        SELECT
        model_key,
        COUNT(*) AS message_count
        FROM
        ai_chat_message
        WHERE
        type = 'user'
        AND create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
        AND create_time &lt; DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
        GROUP BY
        model_key
        ) msg ON a.model_key = msg.model_key
        WHERE
        a.del_flag = 0
        ORDER BY message_count DESC,
            conversation_count DESC
    </select>

    <select id="getAuthModelInfoAll" resultType="org.dromara.business.vo.AiModelAuthCountVO">
        SELECT
            a.model_key,
            a.model_name,
            a.user_com,
            a.project,
            IFNULL( conv.conversation_count, 0 ) AS conversation_count,
            IFNULL( msg.message_count, 0 ) AS message_count
        FROM
            ai_model_auth a
                LEFT JOIN ( SELECT model_key, COUNT(*) AS conversation_count FROM ai_chat_conversation GROUP BY model_key ) conv ON a.model_key = conv.model_key
                LEFT JOIN ( SELECT model_key, COUNT(*) AS message_count FROM ai_chat_message WHERE type = 'user' GROUP BY model_key ) msg ON a.model_key = msg.model_key
        WHERE
            a.del_flag = 0
        ORDER BY message_count DESC,
                 conversation_count DESC
    </select>

    <sql id="common_where">
        <if test="bo.modelName != null and bo.modelName != ''">
            and aaa.model_name like concat( #{bo.modelName}, '%')
        </if>

        <if test="bo.userCom != null and bo.userCom != ''">
            and aaa.user_com like concat( #{bo.userCom}, '%')
        </if>

        <if test="bo.project != null and bo.project != ''">
            and aaa.project like concat( #{bo.project}, '%')
        </if>
    </sql>
</mapper>
