-- ----------------------------
-- 知识库表
-- ----------------------------
drop table if exists bs_knowledge_base;
create table bs_knowledge_base
(
    knowledge_id   bigint(20)      not null auto_increment    comment '知识库ID',
    knowledge_name varchar(50)     not null                   comment '知识库名称',
    knowledge_type varchar(20)     not null                   comment '知识库类型',
    description    varchar(500)    default null               comment '知识库描述',
    create_user_id bigint(20)      not null                   comment '创建用户ID',
    data_type      char(1)         default '1'                comment '数据类型（0结构化数据 1非结构化数据）',
    status         char(1)         default '0'                comment '状态（0正常 1停用）',
    create_by      varchar(64)     default ''                 comment '创建者',
    create_time    datetime        default current_timestamp  comment '创建时间',
    update_by      varchar(64)     default ''                 comment '更新者',
    update_time    datetime        default null on update current_timestamp comment '更新时间',
    remark         varchar(500)    default null               comment '备注',
    primary key (knowledge_id)
) engine=innodb auto_increment=100 comment = '知识库表';

-- ----------------------------
-- 知识库文件表
-- ----------------------------
drop table if exists bs_knowledge_file;
create table bs_knowledge_file
(
    file_id        bigint(20)      not null auto_increment    comment '文件ID',
    knowledge_id   bigint(20)      not null                   comment '知识库ID',
    oss_id         bigint(20)      not null                   comment 'OSS文件ID',
    file_name      varchar(100)    not null                   comment '文件名称',
    file_type      varchar(20)     default null               comment '文件类型',
    file_size      bigint(20)      default 0                  comment '文件大小(KB)',
    status         char(1)         default '0'                comment '文件状态（0处理中 1已完成 2处理失败）',
    vector_id      varchar(100)    default null               comment '向量ID',
    create_by      varchar(64)     default ''                 comment '创建者',
    create_time    datetime        default current_timestamp  comment '创建时间',
    update_by      varchar(64)     default ''                 comment '更新者',
    update_time    datetime        default null on update current_timestamp comment '更新时间',
    remark         varchar(500)    default null               comment '备注',
    primary key (file_id)
) engine=innodb auto_increment=100 comment = '知识库文件表';

-- ----------------------------
-- 知识库用户关联表
-- ----------------------------
drop table if exists bs_knowledge_user_rel;
create table bs_knowledge_user_rel
(
    rel_id          bigint(20)      not null auto_increment    comment '关联ID',
    knowledge_id    bigint(20)      not null                   comment '知识库ID',
    user_id         bigint(20)      not null                   comment '用户ID',
    permission_type char(1)         default '0'                comment '权限类型（0查看 1编辑 2管理）',
    create_by       varchar(64)     default ''                 comment '创建者',
    create_time     datetime        default current_timestamp  comment '创建时间',
    update_by       varchar(64)     default ''                 comment '更新者',
    update_time     datetime        default null on update current_timestamp comment '更新时间',
    remark          varchar(500)    default null               comment '备注',
    primary key (rel_id)
) engine=innodb auto_increment=100 comment = '知识库用户关联表';

-- ----------------------------
-- 初始化-字典数据
-- ----------------------------
insert into sys_dict_type(dict_name, dict_type, status, create_by, remark)
values('知识库类型', 'knowledge_type', '0', 'admin', '知识库类型列表');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(1, '通用知识库', '1', 'knowledge_type', '', 'primary', 'Y', '0', 'admin', '通用知识库');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(2, '专业知识库', '2', 'knowledge_type', '', 'success', 'N', '0', 'admin', '专业知识库');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(3, '个人知识库', '3', 'knowledge_type', '', 'info', 'N', '0', 'admin', '个人知识库');

insert into sys_dict_type(dict_name, dict_type, status, create_by, remark)
values('知识库文件状态', 'knowledge_file_status', '0', 'admin', '知识库文件状态列表');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(1, '处理中', '0', 'knowledge_file_status', '', 'warning', 'Y', '0', 'admin', '处理中');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(2, '已完成', '1', 'knowledge_file_status', '', 'success', 'N', '0', 'admin', '已完成');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(3, '处理失败', '2', 'knowledge_file_status', '', 'danger', 'N', '0', 'admin', '处理失败');

insert into sys_dict_type(dict_name, dict_type, status, create_by, remark)
values('知识库数据类型', 'knowledge_data_type', '0', 'admin', '知识库数据类型列表');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(1, '结构化数据', '0', 'knowledge_data_type', '', 'primary', 'N', '0', 'admin', '结构化数据');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(2, '非结构化数据', '1', 'knowledge_data_type', '', 'info', 'Y', '0', 'admin', '非结构化数据');

insert into sys_dict_type(dict_name, dict_type, status, create_by, remark)
values('知识库权限类型', 'knowledge_permission_type', '0', 'admin', '知识库权限类型列表');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(1, '查看', '0', 'knowledge_permission_type', '', 'info', 'Y', '0', 'admin', '查看权限');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(2, '编辑', '1', 'knowledge_permission_type', '', 'success', 'N', '0', 'admin', '编辑权限');
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark)
values(3, '管理', '2', 'knowledge_permission_type', '', 'primary', 'N', '0', 'admin', '管理权限');
