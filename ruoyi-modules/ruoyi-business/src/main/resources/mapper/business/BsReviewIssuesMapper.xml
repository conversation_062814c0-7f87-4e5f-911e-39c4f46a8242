<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.BsReviewIssuesMapper">

    <select id="selectCommentTypeOptions" resultType="string">
        SELECT DISTINCT comment_type
        FROM bs_review_issues
        WHERE comment_type IS NOT NULL
        AND comment_type != ''
        AND del_flag = '0'
        ORDER BY comment_type
    </select>

    <select id="selectByReviewComments" resultType="org.dromara.business.domain.BsReviewIssues">
        SELECT id, review_comments
        FROM bs_review_issues
        WHERE del_flag = '0'
        AND review_comments IN
        <foreach collection="reviewCommentsList" item="reviewComment" open="(" separator="," close=")">
            #{reviewComment}
        </foreach>
    </select>

</mapper>
