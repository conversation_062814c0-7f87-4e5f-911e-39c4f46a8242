<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.AiChatMessageMapper">

    <update id="setGroupConcatMaxLen">
        SET SESSION group_concat_max_len = 999999999;
    </update>


    <select id="getSevenTokensByModel" resultType="java.util.Map">
        SELECT
            GROUP_CONCAT( DISTINCT aaa.model_name ) AS modelName,
            COALESCE(LENGTH(GROUP_CONCAT(bbb.content)), 0) AS tokenNum
        FROM
            ai_model aaa
                LEFT JOIN ai_chat_message bbb ON aaa.id = bbb.model_id
                AND bbb.del_flag = 0 AND DATE ( bbb.create_time ) >= DATE_SUB( CURDATE(), INTERVAL 7 DAY )
        WHERE
            aaa.del_flag = 0
          AND aaa.`status` = 1
        GROUP BY
            aaa.id
    </select>
    <select id="getTwelveTokens" resultType="java.lang.String" parameterType="java.lang.Long">
        WITH RECURSIVE hours AS (
            -- 生成从当前时间往前推12小时的时间序列
            SELECT
                DATE_FORMAT( NOW(), '%Y-%m-%d %H:00:00' )AS hour_start,
            1 AS hour_offset UNION ALL
        SELECT
            hour_start - INTERVAL 1 HOUR,
            hour_offset + 1
        FROM
            hours
        WHERE
            hour_offset &lt; 12 ),
            hourly_messages AS (
        -- 统计每个小时的消息数量，过滤指定的 model_id
        SELECT
            DATE_FORMAT( create_time, '%Y-%m-%d %H:00:00' ) AS hour_start,
            COUNT(*) AS message_count FROM ai_chat_message WHERE create_time >= NOW() - INTERVAL 12 HOUR AND model_id = #{modelId} AND type = 'user'
        GROUP BY
            DATE_FORMAT( create_time, '%Y-%m-%d %H:00:00' )
            )
        SELECT
            COALESCE(hm.message_count, 0) AS message_count
        FROM
            hours h
                LEFT JOIN hourly_messages hm ON h.hour_start = hm.hour_start
        ORDER BY
            h.hour_start ASC;
    </select>

    <select id="getConversationBs" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT aaa.browser_id) AS distinct_browser_count
        FROM
            ai_chat_message aaa
        WHERE
            aaa.create_time >= NOW() - INTERVAL 30 MINUTE
        <if test="modelUrl != null and modelUrl != ''">
            AND aaa.model_url = #{modelUrl}
        </if>
        <if test="modelId != null">
            AND aaa.model_id = #{modelId}
        </if>
    </select>


    <select id="getAllVisit" resultType="java.lang.Long">
        SELECT
        COUNT( DISTINCT user_id ) AS browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= CURDATE()
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY;
    </select>

    <select id="getAllMonth" resultType="java.lang.Long">
        SELECT
        COUNT( DISTINCT user_id ) AS total_browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= DATE_FORMAT( CURDATE(), '%Y-%m-01' )
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY;
    </select>

    <select id="getTodayMsg" resultType="java.lang.Long">
        SELECT
        COUNT(*) AS user_message_count
        FROM
        ai_chat_message
        WHERE
        create_time >= CURDATE()
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user';
    </select>

    <select id="getMonthMsg" resultType="java.lang.Long">
        SELECT
        COUNT(*) AS total_user_message_count
        FROM
        ai_chat_message
        WHERE
        create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user';
    </select>

    <select id="getTodayMsgPeak" resultType="java.util.Map">
        SELECT
        stat_hour AS hour,
        message_count AS msgCount
        FROM
        (
        SELECT HOUR
        ( create_time ) AS stat_hour,
        COUNT(*) AS message_count,
        COUNT( DISTINCT user_id ) AS DISTINCT_browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= CURDATE()
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user'
        GROUP BY
        stat_hour
        ) AS hourly_stats
        ORDER BY
        message_count DESC
        LIMIT 1
    </select>

    <select id="getMonthMsgPeak" resultType="java.util.Map">
        SELECT
        stat_date AS date,
        message_count AS msgCount
        FROM (
        SELECT
        DATE(create_time) AS stat_date,
        COUNT(*) AS message_count,
        COUNT(DISTINCT user_id) AS DISTINCT_browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user'
        GROUP BY
        DATE(create_time)
        ) AS daily_stats
        ORDER BY
        message_count DESC
        LIMIT 1
    </select>

    <select id="getTodayUserPeak" resultType="java.util.Map">
        SELECT
        stat_hour AS hour,
        browser_count AS userCount
        FROM (
        SELECT
        HOUR(create_time) AS stat_hour,
        COUNT(DISTINCT user_id) AS browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= CURDATE()
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user'
        GROUP BY
        stat_hour
        ) AS hourly_stats
        ORDER BY
        browser_count DESC
        LIMIT 1
    </select>

    <select id="getMonthUserPeak" resultType="java.util.Map">
        SELECT
        stat_date AS date,
        browser_count AS userCount
        FROM (
        SELECT
        DATE(create_time) AS stat_date,
        COUNT(DISTINCT user_id) AS browser_count
        FROM
        ai_chat_message
        WHERE
        create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND type = 'user'
        GROUP BY
        stat_date
        ) AS daily_stats
        ORDER BY
        browser_count DESC
        LIMIT 1
    </select>

    <select id="getDayCountInfo" resultType="org.dromara.business.domain.vo.AiModelHistoryMsgVo" parameterType="org.dromara.business.domain.bo.AiModelHistoryMsgBo">
        WITH RECURSIVE date_series AS (
            SELECT DATE(#{aiModelHistoryMsgBo.dayStartTime}) AS stat_date
            UNION ALL
            SELECT stat_date + INTERVAL 1 DAY
            FROM date_series
            WHERE stat_date + INTERVAL 1 DAY <![CDATA[<=]]> DATE(#{aiModelHistoryMsgBo.dayEndTime})
        ),
        hour_stats AS (
            SELECT
            DATE(create_time) AS stat_date,
            DATE_FORMAT(create_time, '%H') AS stat_hour,
            COUNT(DISTINCT user_id) AS user_count,
            COUNT(CASE WHEN type = 'user' THEN 1 END) AS user_conversation_count
            FROM ai_chat_message
            WHERE create_time >= #{aiModelHistoryMsgBo.dayStartTime} AND create_time <![CDATA[<]]> DATE_ADD(#{aiModelHistoryMsgBo.dayEndTime}, INTERVAL 1 DAY)
            GROUP BY stat_date, stat_hour
        ),
        daily_peak_user AS (
            SELECT
            stat_date,
            stat_hour,
            user_count,
            ROW_NUMBER() OVER (PARTITION BY stat_date ORDER BY user_count DESC, stat_hour ASC) AS rn
            FROM hour_stats
        ),
        daily_peak_conversation AS (
            SELECT
            stat_date,
            stat_hour,
            user_conversation_count AS peak_conversation_count_hour,
            ROW_NUMBER() OVER (PARTITION BY stat_date ORDER BY user_conversation_count DESC, stat_hour ASC) AS rn
            FROM hour_stats
        ),
        total_stats AS (
        SELECT
            DATE(create_time) AS stat_date,
            COUNT(DISTINCT user_id) AS total_user_count,
            COUNT(CASE WHEN type = 'user' THEN 1 END) AS total_conversation_count
            FROM ai_chat_message
            WHERE create_time >= #{aiModelHistoryMsgBo.dayStartTime} AND create_time <![CDATA[<]]> DATE_ADD(#{aiModelHistoryMsgBo.dayEndTime}, INTERVAL 1 DAY)
            GROUP BY stat_date
        )
        SELECT
            ds.stat_date AS dateTime,
            COALESCE(dpu.stat_hour, '') AS peakUserTime,
            COALESCE(dpu.user_count, 0) AS peakUserCount,
            COALESCE(dpc.stat_hour, '') AS peakMsgTime,
            COALESCE(dpc.peak_conversation_count_hour, 0) AS peakMsgCount,
            COALESCE(ts.total_user_count, 0) AS userCount,
            COALESCE(ts.total_conversation_count, 0) AS msgCount
        FROM date_series ds
        LEFT JOIN daily_peak_user dpu ON ds.stat_date = dpu.stat_date AND dpu.rn = 1
        LEFT JOIN daily_peak_conversation dpc ON ds.stat_date = dpc.stat_date AND dpc.rn = 1
        LEFT JOIN total_stats ts ON ds.stat_date = ts.stat_date
        ORDER BY ds.stat_date ASC;
    </select>

    <select id="getMonthCountInfo" resultType="org.dromara.business.domain.vo.AiModelHistoryMsgVo" parameterType="org.dromara.business.domain.bo.AiModelHistoryMsgBo">
        WITH RECURSIVE month_series AS (
            SELECT DATE_FORMAT(STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthStartTime}, '-01'), '%Y-%m-%d'), '%Y-%m-01') AS stat_month
            UNION ALL
            SELECT DATE_ADD(stat_month, INTERVAL 1 MONTH)
            FROM month_series
            WHERE stat_month <![CDATA[<]]> DATE_FORMAT(STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthEndTime}, '-01'), '%Y-%m-%d'), '%Y-%m-%d')
        ),
        daily_user_counts AS (
            SELECT
            DATE(create_time) AS stat_date,
            COUNT(DISTINCT user_id) AS daily_user_count
            FROM ai_chat_message
            WHERE create_time >= STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthStartTime}, '-01'), '%Y-%m-%d')
            AND create_time <![CDATA[<]]> DATE_ADD(STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthEndTime}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            GROUP BY stat_date
        ),
        monthly_peak_day AS (
            SELECT
            DATE_FORMAT(stat_date, '%Y-%m-01') AS stat_month,
            stat_date AS peak_day,
            daily_user_count AS peak_user_count,
            ROW_NUMBER() OVER (PARTITION BY DATE_FORMAT(stat_date, '%Y-%m-01') ORDER BY daily_user_count DESC, stat_date ASC) AS rn
            FROM daily_user_counts
        ),
        monthly_stats AS (
            SELECT
            DATE_FORMAT(create_time, '%Y-%m-01') AS stat_month,
            COUNT(DISTINCT user_id) AS total_user_count,
            COUNT(CASE WHEN type = 'user' THEN 1 END) AS total_conversation_count
            FROM ai_chat_message
            WHERE create_time >= STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthStartTime}, '-01'), '%Y-%m-%d')
            AND create_time <![CDATA[<]]> DATE_ADD(STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthEndTime}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            GROUP BY DATE_FORMAT(create_time, '%Y-%m-01')
        ),
        daily_peak_conversations AS (
            SELECT
            DATE(create_time) AS stat_date,
            COUNT(CASE WHEN type = 'user' THEN 1 END) AS daily_conversation_count
            FROM ai_chat_message
            WHERE create_time >= STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthStartTime}, '-01'), '%Y-%m-%d')
            AND create_time <![CDATA[<]]> DATE_ADD(STR_TO_DATE(CONCAT(#{aiModelHistoryMsgBo.monthEndTime}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            GROUP BY stat_date
        ),
        daily_peak_day AS (
            SELECT
            stat_date,
            daily_conversation_count,
            ROW_NUMBER() OVER (PARTITION BY DATE_FORMAT(stat_date, '%Y-%m-01') ORDER BY daily_conversation_count DESC) AS rn
            FROM daily_peak_conversations
        )
        SELECT
            DATE_FORMAT(ms.stat_month, '%Y-%m') AS dateTime,
            COALESCE(mp.peak_day, NULL) AS peakUserTime,
            COALESCE(mp.peak_user_count, 0) AS peakUserCount,
            COALESCE(st.total_user_count, 0) AS userCount,
            COALESCE(st.total_conversation_count, 0) AS msgCount,
            COALESCE(dp.stat_date, NULL) AS peakMsgTime,
            COALESCE(dp.daily_conversation_count, 0) AS peakMsgCount
        FROM month_series ms
        LEFT JOIN (
            SELECT stat_month, peak_day, peak_user_count
            FROM monthly_peak_day
            WHERE rn = 1
        ) mp ON ms.stat_month = mp.stat_month
        LEFT JOIN monthly_stats st ON ms.stat_month = st.stat_month
        LEFT JOIN (
            SELECT stat_date, daily_conversation_count
            FROM daily_peak_day
            WHERE rn = 1
        ) dp ON ms.stat_month = DATE_FORMAT(dp.stat_date, '%Y-%m-01')
        ORDER BY ms.stat_month ASC;
    </select>


</mapper>
