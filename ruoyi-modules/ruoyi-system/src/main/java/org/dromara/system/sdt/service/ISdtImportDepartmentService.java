package org.dromara.system.sdt.service;

import org.dromara.common.core.domain.R;

/**
 * 山东通服务接口
 */
public interface ISdtImportDepartmentService {

    /**
     * 导入部门信息
     * @param deptId 部门ID
     * @return 处理结果
     */
    R<String> importDepartmentFromSdt(String deptId);
    
    /**
     * 导入用户信息
     * @param deptId 部门ID
     * @return 处理结果
     */
    R<String> importUserFromSdt(String deptId);
}
