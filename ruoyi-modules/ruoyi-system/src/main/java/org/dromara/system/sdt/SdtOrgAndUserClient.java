package org.dromara.system.sdt;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.system.sdt.constant.SdtUrlConstants;
import org.dromara.system.sdt.properties.SdtProperties;
import org.dromara.system.sdt.vo.SdtDepartUserVo;
import org.dromara.system.sdt.vo.SdtDepartmentVo;
import org.dromara.system.sdt.vo.SdtUserVo;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;


/**
 * 山东通组织架构及用户信息获取
 *
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SdtOrgAndUserClient{

    private final RestTemplate restTemplate;

    private final SdtProperties sdtProperties;


    /**
     * 获取部门用户详情列表
     * @return
     */
    public List<SdtUserVo> getDepartUserInfo(String deptId){
        JSONObject result = sdtGet(StrUtil.format(SdtUrlConstants.userInfo, getAccessToken(), deptId, "0"));
        log.info("获取部门用户列表接口{}，{}","getDepartUserInfo", result);
        JSONArray departmentList = result.getJSONArray("userlist");
        return JSONUtil.toList(departmentList, SdtUserVo.class);
    }


    /**
     * 获取部门用户列表
     * @return
     */
    public List<SdtDepartUserVo> getDepartUser(String deptId){
        JSONObject result = sdtGet(StrUtil.format(SdtUrlConstants.simplelist, getAccessToken(), deptId, "1"));
        log.info("获取部门用户列表接口{}，{}","getDepartUser", result);
        JSONArray departmentList = result.getJSONArray("userlist");
        return JSONUtil.toList(departmentList, SdtDepartUserVo.class);
    }



    /**
     * 获取部门列表
     * @param deptId
     * @return
     */
    public List<SdtDepartmentVo> getDepartment(String deptId){
        JSONObject result = sdtGet(StrUtil.format(SdtUrlConstants.departmentList, getAccessToken(), deptId, "0"));
        log.info("获取部门列表接口{}，{}","getDepartment", result);
        JSONArray departmentList = result.getJSONArray("department");
        List<SdtDepartmentVo> departmentVoList = JSONUtil.toList(departmentList, SdtDepartmentVo.class);
        //处理部门数据，将数据整理成父子级树状结构数据
        if(departmentList != null && !departmentList.isEmpty()){
            departmentVoList = buildTreeList(departmentList);
        }
        return departmentVoList;
    }

    private List<SdtDepartmentVo> buildTreeList(JSONArray departmentList) {
        List<SdtDepartmentVo> departmentVoList = JSONUtil.toList(departmentList, SdtDepartmentVo.class);
        Map<String, SdtDepartmentVo> nodeMap = new HashMap<>();
        // 构建 id → 节点 map
        for (SdtDepartmentVo dept : departmentVoList) {
            dept.setDepartmentVoList(new ArrayList<>());
            nodeMap.put(dept.getId(), dept);
        }
        // 收集根节点
        List<SdtDepartmentVo> rootList = new ArrayList<>();
        // 建立父子关系
        for (SdtDepartmentVo dept : departmentVoList) {
            String parentId = dept.getParentid();
            if (parentId == null || !nodeMap.containsKey(parentId)) {
                // 根节点
                rootList.add(dept);
            } else {
                SdtDepartmentVo parent = nodeMap.get(parentId);
                parent.getDepartmentVoList().add(dept);
            }
        }
        return rootList;
    }


    /**
     * 获取应用信息
     * @return
     */
    public JSONArray getAgentInfo() {
        JSONObject result = sdtGet(StrUtil.format("cgi-bin/agent/get?access_token={}&agentid={}", getAccessToken(), sdtProperties.getAgentid()));
        log.info("获取应用信息接口{}，{}","getAgentInfo", result);
        JSONObject allow_partys = result.getJSONObject("allow_partys");
        return allow_partys.getJSONArray("partyid");
    }



    /**
     * 接口请求
     * @return
     */
    private cn.hutool.json.JSONObject sdtGet(String url) {
        String pushUrl = StrUtil.format("{}{}",sdtProperties.getUrl(),url);
        ResponseEntity<String> responseEntity = restTemplate.exchange(pushUrl, HttpMethod.GET, null, String.class);
        String body = responseEntity.getBody();
        log.info("********************************* sdt api req: {},resp:{}",url, body);
        if (ObjectUtil.isEmpty(body)) throw new ServiceException("未获取到登录信息，请退出应用重试");
        JSONObject obj = JSONUtil.parseObj(body);
        if (!"0".equals(obj.getStr("errcode"))) throw new ServiceException("未获取到登录信息，请退出应用重试");
        return obj;
    }



    private String getAccessToken() {
        String key = StrUtil.format("{}:{}", sdtProperties.getRedisPrefix(), "access_token");
        String accessToken = RedisUtils.getCacheObject(key);
        if (ObjectUtil.isEmpty(accessToken)) {
            cn.hutool.json.JSONObject obj = sdtGet(StrUtil.format("/cgi-bin/gettoken?corpid={}&corpsecret={}",sdtProperties.getCorpid(),sdtProperties.getSecret()));
            accessToken = obj.getStr("access_token");
            RedisUtils.setCacheObject(key,accessToken, Duration.ofSeconds(obj.getLong("expires_in")-100));
        }
        return accessToken;
    }


    /**
     * 获取某组织下全部组织架构及用户信息
     * @return
     */
    public List<SdtDepartmentVo> getAllUserInfoList(String partyid) {
        if(StringUtils.isEmpty(partyid)){
            throw new ServiceException("组织机构id不能为空");
        }
        if(partyid.length()<4){
            throw new ServiceException("组织机构过大无法获取");
        }
        List<SdtDepartmentVo> departmentList = getDepartment(partyid);
        if(departmentList!=null && !departmentList.isEmpty()){
            for (SdtDepartmentVo sdtDepartmentVo : departmentList) {
                processDepartments(sdtDepartmentVo);
            }
        }
        return departmentList;
//        //读取文件中的内容，转成字符串
//        ClassPathResource resource = new ClassPathResource("departUserData.json");
//        try (InputStream inputStream = resource.getStream()) {
//            String data = IoUtil.readUtf8(inputStream);
//            JSONArray jsonArray = JSONUtil.parseArray(data);
//            return JSONUtil.toList(jsonArray, SdtDepartmentVo.class);
//        }catch (Exception e){
//            return null;
//        }
    }

    /**
     * 递归子部门获取用户
     *
     * @param sdtDepartmentVo
     */
    private void processDepartments(SdtDepartmentVo sdtDepartmentVo) {
        //循环调用获取子部门下所有用户信息
        List<SdtUserVo> departUserInfo = getDepartUserInfo(sdtDepartmentVo.getId());
        sdtDepartmentVo.setDepartUserList(departUserInfo);
        // 遍历子部门
        if (sdtDepartmentVo.getDepartmentVoList() != null && !sdtDepartmentVo.getDepartmentVoList().isEmpty()) {
            for (SdtDepartmentVo subDepartment : sdtDepartmentVo.getDepartmentVoList()) {
                // 对子部门进行同样的操作
                processDepartments(subDepartment);
            }
        }
    }


    /**
     * 获取所有组织架构信息
     * @return
     */
    public List<SdtDepartmentVo> getAllSubDepartmentsRecursively(String deptId) {
        if(StringUtils.isEmpty(deptId)){
            throw new ServiceException("组织部门id不能为空");
        }
        if(deptId.length()<3){
            throw new ServiceException("组织机构过大无法获取");
        }
        return getDepartment(deptId);
        //读取文件中的内容，转成字符串
//        ClassPathResource resource = new ClassPathResource("city.json");
//        try (InputStream inputStream = resource.getStream()) {
//            String data = IoUtil.readUtf8(inputStream);
//            JSONArray jsonArray = JSONUtil.parseArray(data);
//            return JSONUtil.toList(jsonArray, SdtDepartmentVo.class);
//        }catch (Exception e){
//            return null;
//        }
    }
}
