package org.dromara.system.sdt.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "sdt")
public class SdtProperties {

    /**
     * 接口地址
     */
    private String url;

    /**
     * redis缓存前缀
     */
    private String redisPrefix;

    /**
     *
     */
    private String agentid;

    /**
     *
     */
    private String secret;

    /**
     *
     */
    private String corpid;

}
