package org.dromara.system.sdt.vo;

import lombok.Data;

/**
 * 山东通组织机构下用户信息实体
 *
 */
@Data
public class SdtDepartUserVo {

    /**
     * 统一用户编码
     */
    private String userid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户所属部门id列表。排序在第一个的部门id为主岗部门
     */
    private String department;

    /**
     * 表示在所在的部门内是否为上级。1表示为上级，0表示非上级。按department顺序设置。如果有这个字段优先选择本字段，不使用isleader
     */
    private String is_leader_in_dept;

    /**
     * 职位信息与部门一一映射，设置多个职位字段时，必须同时传递department字段。最多支持20个职务，长度为0-128个字符
     */
    private String positions;

}
