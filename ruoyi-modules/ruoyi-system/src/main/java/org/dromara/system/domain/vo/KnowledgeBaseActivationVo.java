package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.KnowledgeBaseActivation;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 部门知识库开通情况视图对象 knowledge_base_activation
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeBaseActivation.class)
public class KnowledgeBaseActivationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 申请部门名称
     */
    @ExcelProperty(value = "申请部门名称")
    private String departName;

    /**
     * 场景介绍
     */
    @ExcelProperty(value = "场景介绍")
    private String scene;

    /**
     * 开通情况（0-未开通，1-已开通）
     */
    @ExcelProperty(value = "开通情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-未开通，1-已开通")
    private Integer openFlag;

    /**
     * 管理员姓名
     */
    @ExcelProperty(value = "管理员姓名")
    private String managerName;

    /**
     * 管理员账号
     */
    @ExcelProperty(value = "管理员账号")
    private String managerNum;

    /**
     * 预计使用人数
     */
    @ExcelProperty(value = "预计使用人数")
    private String usersNum;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date updateTime;

    /**
     * 开通时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date openDate;

}
