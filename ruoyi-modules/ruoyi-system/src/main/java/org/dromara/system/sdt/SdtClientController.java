package org.dromara.system.sdt;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.json.JSONArray;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.sdt.service.ISdtImportDepartmentService;
import org.dromara.system.sdt.vo.SdtDepartmentVo;
import org.dromara.system.sdt.vo.SdtImportDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 山东通服务调用
 *
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sdtclient")
public class SdtClientController extends BaseController {

    private final SdtOrgAndUserClient sdtOrgAndUserClient;
    private final ISdtImportDepartmentService sdtService;


    /**
     * 查询指定组织架构信息及用户信息
     */
    @GetMapping("/getAllUserInfoList")
    public R<List<SdtDepartmentVo>> getAllUserInfoList(@RequestParam(value = "deptId") String deptId) {
        return R.ok(sdtOrgAndUserClient.getAllUserInfoList(deptId));
    }

    /**
     * 获取应用信息
     * @return
     */
    @GetMapping("/getAgentInfo")
    public R<JSONArray> getAgentInfo() {
        return R.ok(sdtOrgAndUserClient.getAgentInfo());
    }


    /**
     * 获取指定部门组织架构信息
     * @param deptId
     * @return
     */
    @GetMapping("/getAllDepartmentList")
    public R<List<SdtDepartmentVo>> getAllDepartmentList(@RequestParam(value = "deptId") String deptId) {
        List<SdtDepartmentVo> result = sdtOrgAndUserClient.getAllSubDepartmentsRecursively(deptId);
        return R.ok(result);
    }

    /**
     * 上传部门ID后获取指定部门组织架构信息并新增部门
     * @param deptId 部门ID
     * @return 新增结果
     */
    @PostMapping("/importDepartmentFromSdt")
    public R<String> importDepartmentFromSdt(@RequestBody SdtImportDto sdtImportDto) {
        return sdtService.importDepartmentFromSdt(sdtImportDto.getDeptId());
    }

    /**
     * 上传部门ID后获取组织架构信息及用户信息并新增用户
     * @param deptId 部门ID
     * @return 新增结果
     */
    @PostMapping("/importUserFromSdt")
    public R<String> importUserFromSdt(@RequestBody SdtImportDto sdtImportDto) {
        return sdtService.importUserFromSdt(sdtImportDto.getDeptId());
    }
}
