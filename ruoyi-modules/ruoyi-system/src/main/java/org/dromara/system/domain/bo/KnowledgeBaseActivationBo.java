package org.dromara.system.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.system.domain.KnowledgeBaseActivation;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 部门知识库开通情况业务对象 knowledge_base_activation
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = KnowledgeBaseActivation.class, reverseConvertGenerate = false)
public class KnowledgeBaseActivationBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 申请部门名称
     */
    @NotBlank(message = "申请部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String departName;

    /**
     * 场景介绍
     */
    private String scene;

    /**
     * 开通情况（0-未开通，1-已开通）
     */
    private Integer openFlag;

    /**
     * 管理员姓名
     */
    @NotBlank(message = "管理员姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerName;

    /**
     * 管理员账号
     */
    @NotBlank(message = "管理员账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerNum;

    /**
     * 预计使用人数
     */
    private String usersNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开通时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date openDate;


}
