package org.dromara.system.sdt.vo;

import lombok.Data;

import java.util.List;

/**
 * 山东通组织机构实体
 *
 */
@Data
public class SdtDepartmentVo {

    /**
     * 部门id
     */
    private String id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父部门id。
     */
    private String parentid;

    /**
     * 在父部门中的次序值。order值大的排序靠前，order值相等的情况下，按照部门id排，id越小排序越靠前。有效的值范围是[0, 2^32)
     */
    private String order;

    /**
     * 部门可添加成员上限
     */
    private String party_user_limit;

    /**
     * 部门类型的标记。0表示普通部门，1表示实体单位；2表示虚拟单位，3表示行政区划。未赋值时，默认是普通部门
     */
    private String type;

    /**
     * 部门详情。type为实体单位或虚拟单位时，该字段才有效
     */
    private String party_detail;

    /**
     * 单位全称。单位内必须唯一，未赋值时，默认是部门名称
     */
    private String full_name;

    /**
     * 单位简称。单位内必须唯一，未赋值时，默认是部门名称
     */
    private String short_name;

    /**
     *  单位简介。长度为0-256个字符
     */
    private String brief_introduction;

    /**
     * 单位网站。以“www”、”http://“或”https://“开头
     */
    private String domain_name;

    /**
     * 单位地址。长度为0-128个字符
     */
    private String address;

    /**
     * 单位电话。由1-32位的纯数字或’-‘号组成
     */
    private String telephone;

    /**
     * 社会信用代码，由18位大小写字母及数字组成，仅适用于部门类型为实体单位
     */
    private String organization_code;

    /**
     * 部门下所有用户信息
     */
    private List<SdtUserVo> departUserList;

    /**
     * 所有部门信息/子部门信息
     */
    private List<SdtDepartmentVo> departmentVoList;

}
