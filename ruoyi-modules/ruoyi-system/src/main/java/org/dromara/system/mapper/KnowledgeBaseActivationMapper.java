package org.dromara.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.domain.KnowledgeBaseActivation;
import org.dromara.system.domain.vo.KnowledgeBaseActivationVo;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 部门知识库开通情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface KnowledgeBaseActivationMapper extends BaseMapperPlus<KnowledgeBaseActivation, KnowledgeBaseActivationVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<KnowledgeBaseActivation> selectList(IPage<KnowledgeBaseActivation> page, @Param(Constants.WRAPPER) Wrapper<KnowledgeBaseActivation> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    List<KnowledgeBaseActivation> selectList(@Param(Constants.WRAPPER) Wrapper<KnowledgeBaseActivation> queryWrapper);

    @Override
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    }, joinStr = "AND")
    List<KnowledgeBaseActivation> selectByIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    int updateById(@Param(Constants.ENTITY) KnowledgeBaseActivation entity);

}
