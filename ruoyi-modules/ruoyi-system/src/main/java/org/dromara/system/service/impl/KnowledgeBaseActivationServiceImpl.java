package org.dromara.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.KnowledgeBaseActivation;
import org.dromara.system.domain.bo.KnowledgeBaseActivationBo;
import org.dromara.system.domain.vo.KnowledgeBaseActivationVo;
import org.dromara.system.mapper.KnowledgeBaseActivationMapper;
import org.dromara.system.service.IKnowledgeBaseActivationService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 部门知识库开通情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class KnowledgeBaseActivationServiceImpl implements IKnowledgeBaseActivationService {

    private final KnowledgeBaseActivationMapper baseMapper;

    /**
     * 查询部门知识库开通情况
     *
     * @param id 主键
     * @return 部门知识库开通情况
     */
    @Override
    public KnowledgeBaseActivationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询部门知识库开通情况列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 部门知识库开通情况分页列表
     */
    @Override
    public TableDataInfo<KnowledgeBaseActivationVo> queryPageList(KnowledgeBaseActivationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<KnowledgeBaseActivation> lqw = buildQueryWrapper(bo);
        Page<KnowledgeBaseActivationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的部门知识库开通情况列表
     *
     * @param bo 查询条件
     * @return 部门知识库开通情况列表
     */
    @Override
    public List<KnowledgeBaseActivationVo> queryList(KnowledgeBaseActivationBo bo) {
        LambdaQueryWrapper<KnowledgeBaseActivation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<KnowledgeBaseActivation> buildQueryWrapper(KnowledgeBaseActivationBo bo) {
        LambdaQueryWrapper<KnowledgeBaseActivation> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDepartName()), KnowledgeBaseActivation::getDepartName, bo.getDepartName());
        lqw.eq(bo.getOpenFlag() != null, KnowledgeBaseActivation::getOpenFlag, bo.getOpenFlag());
        lqw.like(StringUtils.isNotBlank(bo.getManagerName()), KnowledgeBaseActivation::getManagerName, bo.getManagerName());
        lqw.like(StringUtils.isNotBlank(bo.getManagerNum()), KnowledgeBaseActivation::getManagerNum, bo.getManagerNum());
        lqw.orderByDesc(KnowledgeBaseActivation::getCreateTime);
        return lqw;
    }

    /**
     * 新增部门知识库开通情况
     *
     * @param bo 部门知识库开通情况
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(KnowledgeBaseActivationBo bo) {
        KnowledgeBaseActivation add = MapstructUtils.convert(bo, KnowledgeBaseActivation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改部门知识库开通情况
     *
     * @param bo 部门知识库开通情况
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(KnowledgeBaseActivationBo bo) {
        KnowledgeBaseActivation update = MapstructUtils.convert(bo, KnowledgeBaseActivation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(KnowledgeBaseActivation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除部门知识库开通情况信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            List<KnowledgeBaseActivation> list = baseMapper.selectByIds(ids);
            if (list.size() != ids.size()) {
                throw new ServiceException("您没有删除权限!");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 标记开通知识库
     * @param bo
     * @return
     */
    @Override
    public int changeOpen(KnowledgeBaseActivationBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(null == loginUser){
            throw new ServiceException("未获取到登录用户信息");
        }
        if(null == bo.getId()){
            throw new ServiceException("开通记录id不能为空");
        }
        LambdaUpdateWrapper<KnowledgeBaseActivation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KnowledgeBaseActivation::getId, bo.getId())
                .set(KnowledgeBaseActivation::getOpenFlag, 1)
                .set(KnowledgeBaseActivation::getOpenDate, bo.getOpenDate())
                .set(KnowledgeBaseActivation::getUpdateBy, loginUser.getUserId())
                .set(KnowledgeBaseActivation::getUpdateTime, new Date());
        return baseMapper.update(null, updateWrapper);
    }
}
