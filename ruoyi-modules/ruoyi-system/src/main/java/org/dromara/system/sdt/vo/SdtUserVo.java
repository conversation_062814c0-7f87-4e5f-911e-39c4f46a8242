package org.dromara.system.sdt.vo;

import lombok.Data;

/**
 * 山东通用户信息实体
 *
 */
@Data
public class SdtUserVo {

    /**
     * 统一用户编码
     */
    private String userid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 用户所属部门id列表。排序在第一个的部门id为主岗部门
     */
    private String department;

    /**
     * 部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。有效的值范围是[0, 2^32)。在姓名A-Z排序模式下，为置顶用户的排序值，不为0则默认置顶；在自由排序模式下，为所有用户排序值
     */
    private String order;

    /**
     * 职位信息
     */
    private String position;

    /**
     * 职位信息，多部门多职位，跟随部门
     */
    private String positions;

    /**
     * 性别。0表示未定义，1表示男性，2表示女性
     */
    private String gender;

    /**
     * 邮箱，第三方不可获取
     */
    private String email;

    /**
     * 表示在所在的部门内是否为上级；第三方仅通讯录应用可获取
     */
    private String is_leader_in_dept;

    /**
     * 头像url。如果要获取小图将url最后的”/0”改成”/100”即可
     */
    private String avatar;

    /**
     * 是否隐藏手机号
     */
    private String hide_mobile;

    /**
     * 座机
     */
    private String telephone;

    /**
     * 别名
     */
    private String english_name;

    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活
     */
    private String status;

    /**
     * 扩展属性
     */
    private String extattr;

    /**
     * 用户二维码图片地址
     */
    private String qr_code;

    /**
     * 手机区号
     */
    private String country_code;

    /**
     * 用户对外属性
     */
    private String external_profile;

    /**
     * 对外简称
     */
    private String external_corp_name;

}
