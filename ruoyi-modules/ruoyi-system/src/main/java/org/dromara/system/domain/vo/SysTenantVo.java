package org.dromara.system.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.system.domain.SysTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 租户视图对象 sys_tenant
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysTenant.class)
public class SysTenantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactUserName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    private String companyName;

    /**
     * 对话页欢迎文本
     */
    @ExcelProperty(value = "对话页欢迎文本")
    private String welcomeText;

    /**
     * 图标url
     */
    @ExcelProperty(value = "图标url")
    private String logoUrl;

    /**
     * 对话页图标片标题
     */
    @ExcelProperty(value = "对话页图标片标题")
    private String imageUrlTitle;

    /**
     * 管理页标题
     */
    @ExcelProperty(value = "管理页标题")
    private String titleManagementPage;

    /**
     * 统一社会信用代码
     */
    @ExcelProperty(value = "统一社会信用代码")
    private String licenseNumber;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 域名
     */
    @ExcelProperty(value = "域名")
    private String domain;

    /**
     * 企业简介
     */
    @ExcelProperty(value = "企业简介")
    private String intro;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户套餐编号
     */
    @ExcelProperty(value = "租户套餐编号")
    private Long packageId;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expireTime;

    /**
     * 用户数量（-1不限制）
     */
    @ExcelProperty(value = "用户数量")
    private Long accountCount;

    /**
     * 租户状态（0正常 1停用）
     */
    @ExcelProperty(value = "租户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 浏览器标签标题
     */
    @ExcelProperty(value = "浏览器标签标题")
    private String browserTagTitle;
    /**
     * 浏览器标签图标url
     */
    @ExcelProperty(value = "浏览器标签图标url")
    private String browserTagImageUrl;
    /**
     * 登录页下面注释
     */
    @ExcelProperty(value = "登录页下面注释")
    private String loginPageComment;

    /**
     * 登录页标题
     */
    @ExcelProperty(value = "登录页标题")
    private String loginPageTitle;
    /**
     * 对话页面注释（技术支持...）
     */
    @ExcelProperty(value = "对话页面注释")
    private String chatPageComment;
    /**
     * 浏览器收藏
     */
    @ExcelProperty(value = "浏览器收藏")
    private String browserBookmark;
    /**
     * 知识库的注释
     */
    @ExcelProperty(value = "知识库的注释")
    private String knowledgeBaseComment;

    /** 文档下载开关 */
    @ExcelProperty(value = "文档下载开关")
    private String docDownloadSwitch;

    /** 手机端操作说明 */
    @ExcelProperty(value = "手机端操作说明")
    private String mobileOperateInstruction;

    /** 电脑端操作说明 */
    @ExcelProperty(value = "电脑端操作说明")
    private String pcOperateInstruction;

    /** 模型接入说明 */
    @ExcelProperty(value = "模型接入说明")
    private String modelAccessInstruction;

    /** 模型接入申请表 */
    @ExcelProperty(value = "模型接入申请表")
    private String modelAccessApplication;

    /** 模型接入API文档 */
    @ExcelProperty(value = "模型接入API文档")
    private String modelAccessApiDoc;


}
