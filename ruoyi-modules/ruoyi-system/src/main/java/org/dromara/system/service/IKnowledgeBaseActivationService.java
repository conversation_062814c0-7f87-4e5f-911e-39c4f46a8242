package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.system.domain.bo.KnowledgeBaseActivationBo;
import org.dromara.system.domain.vo.KnowledgeBaseActivationVo;

import java.util.Collection;
import java.util.List;

/**
 * 部门知识库开通情况Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IKnowledgeBaseActivationService {

    /**
     * 查询部门知识库开通情况
     *
     * @param id 主键
     * @return 部门知识库开通情况
     */
    KnowledgeBaseActivationVo queryById(Long id);

    /**
     * 分页查询部门知识库开通情况列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 部门知识库开通情况分页列表
     */
    TableDataInfo<KnowledgeBaseActivationVo> queryPageList(KnowledgeBaseActivationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的部门知识库开通情况列表
     *
     * @param bo 查询条件
     * @return 部门知识库开通情况列表
     */
    List<KnowledgeBaseActivationVo> queryList(KnowledgeBaseActivationBo bo);

    /**
     * 新增部门知识库开通情况
     *
     * @param bo 部门知识库开通情况
     * @return 是否新增成功
     */
    Boolean insertByBo(KnowledgeBaseActivationBo bo);

    /**
     * 修改部门知识库开通情况
     *
     * @param bo 部门知识库开通情况
     * @return 是否修改成功
     */
    Boolean updateByBo(KnowledgeBaseActivationBo bo);

    /**
     * 校验并批量删除部门知识库开通情况信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 标记开通知识库
     * @param bo
     * @return
     */
    int changeOpen(KnowledgeBaseActivationBo bo);
}
