package org.dromara.system.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.KnowledgeBaseActivationBo;
import org.dromara.system.domain.vo.KnowledgeBaseActivationVo;
import org.dromara.system.service.IKnowledgeBaseActivationService;

/**
 * 部门知识库开通情况
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/baseActivation")
public class KnowledgeBaseActivationController extends BaseController {

    private final IKnowledgeBaseActivationService knowledgeBaseActivationService;

    /**
     * 查询部门知识库开通情况列表
     */
    @SaCheckPermission("business:baseActivation:list")
    @GetMapping("/list")
    public TableDataInfo<KnowledgeBaseActivationVo> list(KnowledgeBaseActivationBo bo, PageQuery pageQuery) {
        return knowledgeBaseActivationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出部门知识库开通情况列表
     */
    @SaCheckPermission("business:baseActivation:export")
    @Log(title = "部门知识库开通情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(KnowledgeBaseActivationBo bo, HttpServletResponse response) {
        List<KnowledgeBaseActivationVo> list = knowledgeBaseActivationService.queryList(bo);
        ExcelUtil.exportExcel(list, "部门知识库开通情况", KnowledgeBaseActivationVo.class, response);
    }

    /**
     * 获取部门知识库开通情况详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:baseActivation:query")
    @GetMapping("/{id}")
    public R<KnowledgeBaseActivationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(knowledgeBaseActivationService.queryById(id));
    }

    /**
     * 新增部门知识库开通情况
     */
    @SaCheckPermission("business:baseActivation:add")
    @Log(title = "部门知识库开通情况", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody KnowledgeBaseActivationBo bo) {
        return toAjax(knowledgeBaseActivationService.insertByBo(bo));
    }

    /**
     * 修改部门知识库开通情况
     */
    @SaCheckPermission("business:baseActivation:edit")
    @Log(title = "部门知识库开通情况", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody KnowledgeBaseActivationBo bo) {
        return toAjax(knowledgeBaseActivationService.updateByBo(bo));
    }

    /**
     * 删除部门知识库开通情况
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:baseActivation:remove")
    @Log(title = "部门知识库开通情况", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(knowledgeBaseActivationService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 标记开通知识库
     * @param bo
     * @return
     */
    @Log(title = "标记开通知识库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/changeOpen")
    public R<Void> changeOpen(@RequestBody KnowledgeBaseActivationBo bo) {
        return toAjax(knowledgeBaseActivationService.changeOpen(bo));
    }


}
