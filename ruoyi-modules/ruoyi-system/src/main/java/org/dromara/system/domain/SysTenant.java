package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 租户对象 sys_tenant
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_tenant")
public class SysTenant extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 联系人
     */
    private String contactUserName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 对话页欢迎文本
     */
    private String welcomeText;

    /**
     * 图标url
     */
    private String logoUrl;

    /**
     * 对话页图标片标题
     */
    private String imageUrlTitle;

    /**
     * 管理页标题
     */
    private String titleManagementPage;

    /**
     * 统一社会信用代码
     */
    private String licenseNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 域名
     */
    private String domain;

    /**
     * 企业简介
     */
    private String intro;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户套餐编号
     */
    private Long packageId;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 用户数量（-1不限制）
     */
    private Long accountCount;

    /**
     * 租户状态（0正常 1停用）
     */
    private String status;

    /**
     * 浏览器标签标题
     */
    private String browserTagTitle;
    /**
     * 浏览器标签图标url
     */
    private String browserTagImageUrl;
    /**
     * 登录页下面注释
     */
    private String loginPageComment;

    /**
     * 登录页标题
     */
    private String loginPageTitle;
    /**
     * 对话页面注释（技术支持...）
     */
    private String chatPageComment;
    /**
     * 浏览器收藏
     */
    private String browserBookmark;
    /**
     * 知识库的注释
     */
    private String knowledgeBaseComment;

    /** 文档下载开关 */
    private String docDownloadSwitch;

    /** 手机端操作说明 */
    private String mobileOperateInstruction;

    /** 电脑端操作说明 */
    private String pcOperateInstruction;

    /** 模型接入说明 */
    private String modelAccessInstruction;

    /** 模型接入申请表 */
    private String modelAccessApplication;

    /** 模型接入API文档 */
    private String modelAccessApiDoc;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
