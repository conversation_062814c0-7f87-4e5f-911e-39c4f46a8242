package org.dromara.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Date;

/**
 * 部门知识库开通情况对象 knowledge_base_activation
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_base_activation")
public class KnowledgeBaseActivation extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

    /**
     * 申请部门名称
     */
    private String departName;

    /**
     * 场景介绍
     */
    private String scene;

    /**
     * 开通情况（0-未开通，1-已开通）
     */
    private Integer openFlag;

    /**
     * 管理员姓名
     */
    private String managerName;

    /**
     * 管理员账号
     */
    private String managerNum;

    /**
     * 预计使用人数
     */
    private String usersNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开通时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date openDate;
}
