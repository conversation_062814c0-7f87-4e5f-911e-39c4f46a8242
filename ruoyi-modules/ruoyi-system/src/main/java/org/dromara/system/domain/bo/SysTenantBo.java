package org.dromara.system.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.system.domain.SysTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 租户业务对象 sys_tenant
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysTenant.class, reverseConvertGenerate = false)
public class SysTenantBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactUserName;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactPhone;

    /**
     * 企业名称
     */
    @NotBlank(message = "企业名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyName;

    /**
     * 对话页欢迎文本
     */
    private String welcomeText;

    /**
     * 图标url
     */
    private String logoUrl;

    /**
     * 对话页图标片标题
     */
    private String imageUrlTitle;

    /**
     * 管理页标题
     */
    private String titleManagementPage;

    /**
     * 用户名（创建系统用户）
     */
    @NotBlank(message = "用户名不能为空", groups = { AddGroup.class })
    private String username;

    /**
     * 密码（创建系统用户）
     */
    @NotBlank(message = "密码不能为空", groups = { AddGroup.class })
    private String password;

    /**
     * 统一社会信用代码
     */
    private String licenseNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 域名
     */
    private String domain;

    /**
     * 企业简介
     */
    private String intro;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户套餐编号
     */
    @NotNull(message = "租户套餐不能为空", groups = { AddGroup.class })
    private Long packageId;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 用户数量（-1不限制）
     */
    private Long accountCount;

    /**
     * 租户状态（0正常 1停用）
     */
    private String status;

    /**
     * 浏览器标签标题
     */
    private String browserTagTitle;
    /**
     * 浏览器标签图标url
     */
    private String browserTagImageUrl;
    /**
     * 登录页下面注释
     */
    private String loginPageComment;

    /**
     * 登录页标题
     */
    private String loginPageTitle;
    /**
     * 对话页面注释（技术支持...）
     */
    private String chatPageComment;
    /**
     * 浏览器收藏
     */
    private String browserBookmark;
    /**
     * 知识库的注释
     */
    private String knowledgeBaseComment;

    /** 文档下载开关 */
    private String docDownloadSwitch;

    /** 手机端操作说明 */
    private String mobileOperateInstruction;

    /** 电脑端操作说明 */
    private String pcOperateInstruction;

    /** 模型接入说明 */
    private String modelAccessInstruction;

    /** 模型接入申请表 */
    private String modelAccessApplication;

    /** 模型接入API文档 */
    private String modelAccessApiDoc;


}
