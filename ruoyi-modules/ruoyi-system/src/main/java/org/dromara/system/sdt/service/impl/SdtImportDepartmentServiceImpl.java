package org.dromara.system.sdt.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.system.domain.bo.SysDeptBo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.sdt.SdtOrgAndUserClient;
import org.dromara.system.sdt.service.ISdtImportDepartmentService;
import org.dromara.system.sdt.vo.SdtDepartmentVo;
import org.dromara.system.sdt.vo.SdtUserVo;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysRoleService;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 山东通服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SdtImportDepartmentServiceImpl implements ISdtImportDepartmentService {

    private final SdtOrgAndUserClient sdtOrgAndUserClient;
    private final ISysDeptService sysDeptService;
    private final ISysUserService userService;
    private final ISysRoleService roleService;

    /**
     * 导入部门信息
     * @param deptId 部门ID
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> importDepartmentFromSdt(String deptId) {
        try {
            // 查询指定部门组织架构信息
            List<SdtDepartmentVo> departmentList = sdtOrgAndUserClient.getAllSubDepartmentsRecursively(deptId);
            if (departmentList == null || departmentList.isEmpty()) {
                return R.fail("未获取到部门信息");
            }

            int successCount = 0;
            // 遍历部门列表，逐个添加部门
            for (SdtDepartmentVo departmentVo : departmentList) {
                // 创建部门业务对象
                SysDeptBo deptBo = new SysDeptBo();
                deptBo.setDeptName(departmentVo.getName());

                deptBo.setParentId(Long.valueOf(deptId));

                deptBo.setDeptId(Long.valueOf(departmentVo.getId()));
                // 设置排序
                deptBo.setOrderNum(departmentVo.getOrder() != null ? Integer.valueOf(departmentVo.getOrder()) : 0);

                // 设置电话
                deptBo.setPhone(departmentVo.getTelephone());

                // 设置部门状态为正常
                deptBo.setStatus("0");

                SysDeptVo sysDeptVo = sysDeptService.selectDeptById(Long.valueOf(departmentVo.getId()));
                if (ObjectUtils.isEmpty(sysDeptVo)){
                    // 调用insertDept接口新增部门
                    sysDeptService.insertDept(deptBo);
                }else {
                    sysDeptService.updateDept(deptBo);
                }
                successCount++;

                // 如果部门有子部门，递归处理
                if (departmentVo.getDepartmentVoList() != null && !departmentVo.getDepartmentVoList().isEmpty()) {
                    processChildDepartments(departmentVo.getDepartmentVoList());
                }
            }

            return R.ok("成功导入" + successCount + "个部门");
        } catch (Exception e) {
            log.error("导入部门失败: ", e);
            return R.fail("导入部门失败: " + e.getMessage());
        }
    }

    public R<String> importDepartmentFromSdtUser(String deptId1,List<SdtDepartmentVo> departmentList) {
        try {
            // 查询指定部门组织架构信息
//            List<SdtDepartmentVo> departmentList = sdtOrgAndUserClient.getAllSubDepartmentsRecursively(deptId);
            if (departmentList == null || departmentList.isEmpty()) {
                return R.fail("未获取到部门信息");
            }

            int successCount = 0;
            // 遍历部门列表，逐个添加部门
            for (SdtDepartmentVo departmentVo : departmentList) {
                // 创建部门业务对象
                SysDeptBo deptBo = new SysDeptBo();
                deptBo.setDeptName(departmentVo.getName());

                deptBo.setParentId(Long.valueOf(departmentVo.getParentid()));
//                // 设置父部门ID
//                // 如果是第一层级的部门，parentid等于接口上传的部门id
//                if (deptId.equals(departmentVo.getParentid())) {
//
//                } else {
//                    deptBo.setParentId(Long.valueOf(departmentVo.getParentid()));
//                }
                deptBo.setDeptId(Long.valueOf(departmentVo.getId()));
                // 设置排序
                deptBo.setOrderNum(departmentVo.getOrder() != null ? Integer.valueOf(departmentVo.getOrder()) : 0);

                // 设置电话
                deptBo.setPhone(departmentVo.getTelephone());

                // 设置部门状态为正常
                deptBo.setStatus("0");

                SysDeptVo sysDeptVo = sysDeptService.selectDeptById(Long.valueOf(departmentVo.getId()));
                if (ObjectUtils.isEmpty(sysDeptVo)){
                    // 调用insertDept接口新增部门
                    sysDeptService.insertDept(deptBo);
                }else {
                    sysDeptService.updateDept(deptBo);
                }
                successCount++;

                // 如果部门有子部门，递归处理
                if (departmentVo.getDepartmentVoList() != null && !departmentVo.getDepartmentVoList().isEmpty()) {
                    processChildDepartments(departmentVo.getDepartmentVoList());
                }
            }

            return R.ok("成功导入" + successCount + "个部门");
        } catch (Exception e) {
            log.error("导入部门失败: ", e);
            return R.fail("导入部门失败: " + e.getMessage());
        }
    }

    /**
     * 导入用户信息
     * @param deptId 部门ID
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> importUserFromSdt(String deptId) {
        try {
            // 查询指定组织架构信息及用户信息
            List<SdtDepartmentVo> departmentList = sdtOrgAndUserClient.getAllUserInfoList(deptId);
            log.info("组织架构信息及用户信息: {}", departmentList);
            log.info("获取组织架构信息及用户信息成功");
            // 同步部门
            log.info("----------------同步部门信息开始---------------------");
            this.importDepartmentFromSdtUser(deptId,departmentList);

            log.info("----------------同步部门信息结束---------------------");
            log.info("----------------同步用户信息开始---------------------");
            if (departmentList == null || departmentList.isEmpty()) {
                return R.fail("未获取到组织架构信息");
            }

            // 用于统计用户导入结果
            int[] counts = new int[2]; // counts[0]为新增用户数，counts[1]为更新用户数

            // 递归处理所有部门及其用户
            processDepartmentsAndUsers(departmentList, counts, deptId);

            log.info("----------------同步用户信息结束---------------------");
            log.info("成功新增用户: {} 个", counts[0]);
            log.info("成功更新用户: {} 个", counts[1]);
            return R.ok(String.format("成功导入用户: 新增 %d 个, 更新 %d 个", counts[0], counts[1]));
        } catch (Exception e) {
            log.error("导入用户失败: ", e);
            return R.fail("导入用户失败: " + e.getMessage());
        }
    }

    /**
     * 递归处理部门及其用户
     * @param departmentList 部门列表
     * @param counts 统计数组，counts[0]为新增用户数，counts[1]为更新用户数
     * @param parentDeptId 父部门ID
     */
    private void processDepartmentsAndUsers(List<SdtDepartmentVo> departmentList, int[] counts, String parentDeptId) {
        if (departmentList == null || departmentList.isEmpty()) {
            return;
        }

        for (SdtDepartmentVo departmentVo : departmentList) {
            // 处理当前部门下的用户
            processUsersInDepartment(departmentVo, counts, parentDeptId);

            // 递归处理子部门
            if (departmentVo.getDepartmentVoList() != null && !departmentVo.getDepartmentVoList().isEmpty()) {
                processDepartmentsAndUsers(departmentVo.getDepartmentVoList(), counts, parentDeptId);
            }
        }
    }

    /**
     * 处理部门下的用户
     * @param departmentVo 部门信息
     * @param counts 统计数组，counts[0]为新增用户数，counts[1]为更新用户数
     * @param parentDeptId 父部门ID
     */
    private void processUsersInDepartment(SdtDepartmentVo departmentVo, int[] counts, String parentDeptId) {
        if (departmentVo.getDepartUserList() == null || departmentVo.getDepartUserList().isEmpty()) {
            return;
        }

        log.info("处理部门[{}]下的用户, 用户数量: {}", departmentVo.getName(), departmentVo.getDepartUserList().size());

        // 遍历部门下的用户
        for (SdtUserVo userVo : departmentVo.getDepartUserList()) {
            // 创建用户业务对象
            SysUserBo userBo = new SysUserBo();

            // 设置用户基本信息
            userBo.setUserName(userVo.getMobile()); // 使用手机号作为用户名
            userBo.setNickName(userVo.getName()); // 使用姓名作为昵称
            userBo.setPhonenumber(userVo.getMobile()); // 手机号

            // 设置部门ID
            String departmentStr = userVo.getDepartment();
            if (StringUtils.isNotEmpty(departmentStr)) {
                // 解析部门字符串 "[70176,68743]"
                String cleanedStr = departmentStr.replace("[", "").replace("]", "");
                String[] deptIds = cleanedStr.split(",");
                
                // 判断是否包含传入的deptId
                boolean containsParentDeptId = false;
                for (String id : deptIds) {
                    if (id.trim().equals(parentDeptId)) {
                        containsParentDeptId = true;
                        userBo.setDeptId(Long.valueOf(parentDeptId));
                        break;
                    }
                }
                
                // 如果不包含传入的deptId，则使用第一个部门ID
                if (!containsParentDeptId && deptIds.length > 0) {
                    userBo.setDeptId(Long.valueOf(deptIds[0].trim()));
                }
            } else {
                // 如果没有部门信息，使用当前部门ID
                userBo.setDeptId(Long.valueOf(departmentVo.getId()));
            }

            // 设置用户密码为默认密码123123
            userBo.setPassword(BCrypt.hashpw("123123"));

            // 设置用户状态为正常
            userBo.setStatus("0");

            // 设置用户性别
            if (StringUtils.isNotEmpty(userVo.getGender())) {
                // 性别。0表示未定义，1表示男性，2表示女性
                if ("1".equals(userVo.getGender())) {
                    userBo.setSex("0"); // 男
                } else if ("2".equals(userVo.getGender())) {
                    userBo.setSex("1"); // 女
                } else {
                    userBo.setSex("2"); // 未知
                }
            } else {
                userBo.setSex("2"); // 未知
            }

            // 设置用户邮箱
            userBo.setEmail(userVo.getEmail());

            // 设置备注
            userBo.setRemark(departmentVo.getName());

            // 检查用户是否已存在
            if (!userService.checkUserNameUnique(userBo)) {
                try {
                    SysUserVo sysUserVo = userService.selectUserByUserName(userBo.getUserName());
                    userBo.setUserId(sysUserVo.getUserId());
                    int result = userService.updateUser(userBo);
                    if (result > 0) {
                        counts[1]++; // 更新用户计数
                    } else {
                        log.error("修改用户失败: {}", userBo.getUserName());
                    }
                } catch (Exception e) {
                    log.error("修改用户失败: {}, 错误: {}", userBo.getUserName(), e.getMessage());
                }
            }
            else {
                // 调用insertUser接口新增用户
                try {
                    int result = userService.insertUser(userBo);
                    if (result > 0) {
                        counts[0]++; // 新增用户计数
                    } else {
                        log.error("新增用户失败: {}", userBo.getUserName());
                    }
                } catch (Exception e) {
                    log.error("新增用户失败: {}, 错误: {}", userBo.getUserName(), e.getMessage());
                }
            }
        }
    }

    /**
     * 处理子部门
     * @param childDepartments 子部门列表
     */
    private void processChildDepartments(List<SdtDepartmentVo> childDepartments) {
        if (childDepartments == null || childDepartments.isEmpty()) {
            return;
        }

        for (SdtDepartmentVo childDept : childDepartments) {
            SysDeptBo deptBo = new SysDeptBo();
            deptBo.setDeptName(childDept.getName());
            deptBo.setParentId(Long.valueOf(childDept.getParentid()));
            deptBo.setOrderNum(childDept.getOrder() != null ? Integer.valueOf(childDept.getOrder()) : 0);
            deptBo.setPhone(childDept.getTelephone());
            deptBo.setStatus("0");
            deptBo.setDeptId(Long.valueOf(childDept.getId()));

            SysDeptVo sysDeptVo = sysDeptService.selectDeptById(Long.valueOf(childDept.getId()));
            if (ObjectUtils.isEmpty(sysDeptVo)){
                // 调用insertDept接口新增部门
                sysDeptService.insertDept(deptBo);
            }else {
                sysDeptService.updateDept(deptBo);
            }

            // 递归处理子部门
            if (childDept.getDepartmentVoList() != null && !childDept.getDepartmentVoList().isEmpty()) {
                processChildDepartments(childDept.getDepartmentVoList());
            }
        }
    }
}
